version: '3.8'

services:
  # Prometheus服务
  prometheus:
    image: prom/prometheus:latest
    container_name: user-center-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - ./alert_rules.yml:/etc/prometheus/alert_rules.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=10GB'
    restart: unless-stopped
    networks:
      - monitoring
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      - alertmanager

  # Grafana服务
  grafana:
    image: grafana/grafana:latest
    container_name: user-center-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel,marcusolsson-json-datasource
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    restart: unless-stopped
    networks:
      - monitoring
    depends_on:
      - prometheus

  # AlertManager服务
  alertmanager:
    image: prom/alertmanager:latest
    container_name: user-center-alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - ./alertmanager/templates:/etc/alertmanager/templates
      - alertmanager-data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
      - '--cluster.advertise-address=0.0.0.0:9093'
    restart: unless-stopped
    networks:
      - monitoring

  # Zipkin链路追踪服务
  zipkin:
    image: openzipkin/zipkin:latest
    container_name: user-center-zipkin
    ports:
      - "9411:9411"
    environment:
      - STORAGE_TYPE=mem
      # 如果使用MySQL存储，取消注释以下配置
      # - STORAGE_TYPE=mysql
      # - MYSQL_HOST=mysql
      # - MYSQL_USER=zipkin
      # - MYSQL_PASS=zipkin
      # - MYSQL_DB=zipkin
    restart: unless-stopped
    networks:
      - monitoring

  # Node Exporter (可选，用于系统指标)
  node-exporter:
    image: prom/node-exporter:latest
    container_name: user-center-node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    restart: unless-stopped
    networks:
      - monitoring

  # cAdvisor (可选，用于容器指标)
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: user-center-cadvisor
    ports:
      - "8800:8800"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg
    restart: unless-stopped
    networks:
      - monitoring

  # Redis (可选，用于Grafana会话存储)
  redis:
    image: redis:alpine
    container_name: user-center-redis-monitoring
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    networks:
      - monitoring

  # Nginx (可选，用于反向代理)
  nginx:
    image: nginx:alpine
    container_name: user-center-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    restart: unless-stopped
    networks:
      - monitoring
    depends_on:
      - grafana
      - prometheus
      - alertmanager

volumes:
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  alertmanager-data:
    driver: local
  redis-data:
    driver: local

networks:
  monitoring:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
