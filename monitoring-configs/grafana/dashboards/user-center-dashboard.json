{"dashboard": {"id": null, "title": "用户中心监控仪表板", "tags": ["user-center", "business"], "timezone": "browser", "panels": [{"id": 1, "title": "用户注册趋势", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "datasource": "UserCenter API", "targets": [{"type": "json", "url": "/metrics/overview", "path": "$.data.user_metrics.total_registrations", "legendFormat": "注册速率 (每分钟)", "refId": "A"}], "yAxes": [{"label": "注册数/分钟", "min": 0}, {"show": false}], "xAxis": {"show": true}, "legend": {"show": true}}, {"id": 2, "title": "登录成功率", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "datasource": "UserCenter API", "targets": [{"type": "json", "url": "/metrics/overview", "path": "$.data.user_metrics.total_logins", "legendFormat": "成功率", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}}}}, {"id": 3, "title": "缓存性能", "type": "graph", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "datasource": "UserCenter API", "targets": [{"type": "json", "url": "/metrics/overview", "path": "$.data.performance_metrics.cache_hit_rate", "legendFormat": "缓存命中率 (%)", "queryType": "JSON", "method": "GET", "refId": "A"}, {"type": "json", "url": "/metrics/overview", "path": "$.data.performance_metrics.cache_hits", "legendFormat": "缓存命中数", "refId": "B"}]}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s", "schemaVersion": 27, "version": 1}}