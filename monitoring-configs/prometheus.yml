# Prometheus配置文件
# 用于监控用户中心应用

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'user-center-monitor'

# 告警规则文件
rule_files:
  - "alert_rules.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 数据抓取配置
scrape_configs:
  # 用户中心应用监控
  - job_name: 'user-center'
    static_configs:
      - targets: ['host.docker.internal:18080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 10s
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']
    
  # Prometheus自监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['prometheus:9090']
    scrape_interval: 30s

  # Grafana监控
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    scrape_interval: 30s

  # AlertManager监控
  - job_name: 'alertmanager'
    static_configs:
      - targets: ['alertmanager:9093']
    scrape_interval: 30s

# 远程写入配置（可选）
# remote_write:
#   - url: "http://remote-storage:9201/write"
#     queue_config:
#       max_samples_per_send: 1000
#       max_shards: 200
#       capacity: 2500

# 远程读取配置（可选）
# remote_read:
#   - url: "http://remote-storage:9201/read"
