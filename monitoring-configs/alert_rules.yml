# Prometheus告警规则配置
# 用户中心应用告警规则

groups:
  - name: user-center-alerts
    rules:
      # 应用健康状态告警
      - alert: ApplicationDown
        expr: up{job="user-center"} == 0
        for: 1m
        labels:
          severity: critical
          service: user-center
        annotations:
          summary: "用户中心应用不可用"
          description: "用户中心应用已经停止响应超过1分钟"
          
      # 高登录失败率告警
      - alert: HighLoginFailureRate
        expr: rate(user_login_failure_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: user-center
        annotations:
          summary: "登录失败率过高"
          description: "最近5分钟登录失败率超过0.1次/秒，当前值: {{ $value }}"
          
      # 缓存命中率低告警
      - alert: LowCacheHitRate
        expr: cache_hit_rate < 80
        for: 5m
        labels:
          severity: warning
          service: user-center
        annotations:
          summary: "缓存命中率过低"
          description: "缓存命中率低于80%，当前值: {{ $value }}%"
          
      # 数据库连接问题告警
      - alert: DatabaseConnectionIssue
        expr: database_connections_active == 0
        for: 1m
        labels:
          severity: critical
          service: user-center
        annotations:
          summary: "数据库连接异常"
          description: "没有活跃的数据库连接"
          
      # Redis连接问题告警
      - alert: RedisConnectionIssue
        expr: redis_connections_active == 0
        for: 1m
        labels:
          severity: critical
          service: user-center
        annotations:
          summary: "Redis连接异常"
          description: "Redis连接状态异常"
          
      # API响应时间过长告警
      - alert: HighAPIResponseTime
        expr: histogram_quantile(0.95, rate(http_server_requests_seconds_bucket{job="user-center"}[5m])) > 2
        for: 3m
        labels:
          severity: warning
          service: user-center
        annotations:
          summary: "API响应时间过长"
          description: "95%的API请求响应时间超过2秒，当前P95: {{ $value }}秒"
          
      # 高错误率告警
      - alert: HighErrorRate
        expr: rate(http_server_requests_total{job="user-center",status=~"5.."}[5m]) / rate(http_server_requests_total{job="user-center"}[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
          service: user-center
        annotations:
          summary: "API错误率过高"
          description: "5xx错误率超过5%，当前值: {{ $value | humanizePercentage }}"
          
      # 内存使用率过高告警
      - alert: HighMemoryUsage
        expr: (jvm_memory_used_bytes{job="user-center",area="heap"} / jvm_memory_max_bytes{job="user-center",area="heap"}) > 0.85
        for: 5m
        labels:
          severity: warning
          service: user-center
        annotations:
          summary: "JVM堆内存使用率过高"
          description: "JVM堆内存使用率超过85%，当前值: {{ $value | humanizePercentage }}"
          
      # CPU使用率过高告警
      - alert: HighCPUUsage
        expr: system_cpu_usage{job="user-center"} > 0.8
        for: 5m
        labels:
          severity: warning
          service: user-center
        annotations:
          summary: "CPU使用率过高"
          description: "系统CPU使用率超过80%，当前值: {{ $value | humanizePercentage }}"
          
      # 待处理操作过多告警
      - alert: TooManyPendingOperations
        expr: operations_pending > 100
        for: 3m
        labels:
          severity: warning
          service: user-center
        annotations:
          summary: "待处理操作过多"
          description: "待处理操作数量超过100，当前值: {{ $value }}"
          
      # 用户会话数异常告警
      - alert: AbnormalUserSessions
        expr: user_sessions_active > 1000 or user_sessions_active < 0
        for: 2m
        labels:
          severity: warning
          service: user-center
        annotations:
          summary: "用户会话数异常"
          description: "活跃用户会话数异常，当前值: {{ $value }}"

  - name: infrastructure-alerts
    rules:
      # 磁盘空间不足告警
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
        for: 5m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "磁盘空间不足"
          description: "磁盘可用空间少于10%，当前值: {{ $value | humanizePercentage }}"
          
      # 网络连接数过多告警
      - alert: TooManyConnections
        expr: node_netstat_Tcp_CurrEstab > 1000
        for: 5m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "TCP连接数过多"
          description: "当前TCP连接数: {{ $value }}"

  - name: business-alerts
    rules:
      # 用户注册异常告警
      - alert: AbnormalUserRegistration
        expr: rate(user_registration_total[1h]) > 100 or rate(user_registration_total[1h]) == 0
        for: 10m
        labels:
          severity: info
          service: user-center
        annotations:
          summary: "用户注册数量异常"
          description: "用户注册速率异常，当前每小时注册: {{ $value }}"
          
      # 数据库查询时间过长告警
      - alert: SlowDatabaseQueries
        expr: rate(database_query_duration_seconds_sum[5m]) / rate(database_query_duration_seconds_count[5m]) > 1
        for: 3m
        labels:
          severity: warning
          service: user-center
        annotations:
          summary: "数据库查询响应慢"
          description: "数据库平均查询时间超过1秒，当前值: {{ $value }}秒"
