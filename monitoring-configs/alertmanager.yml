# AlertManager配置文件
# 用户中心告警管理配置

global:
  # SMTP配置
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-email-password'
  
  # 企业微信配置（可选）
  wechat_api_url: 'https://qyapi.weixin.qq.com/cgi-bin/'
  wechat_api_secret: 'your-wechat-secret'
  wechat_api_corp_id: 'your-corp-id'

# 模板配置
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# 路由配置
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 12h
  receiver: 'default-receiver'
  
  routes:
    # 关键告警立即发送
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      repeat_interval: 5m
      
    # 警告级别告警
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 30s
      repeat_interval: 1h
      
    # 信息级别告警
    - match:
        severity: info
      receiver: 'info-alerts'
      group_wait: 5m
      repeat_interval: 24h
      
    # 业务告警特殊处理
    - match:
        service: user-center
      receiver: 'business-alerts'
      group_wait: 1m
      repeat_interval: 30m

# 接收器配置
receivers:
  # 默认接收器
  - name: 'default-receiver'
    email_configs:
      - to: '<EMAIL>'
        subject: '【监控告警】{{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警详情: {{ .Annotations.description }}
          告警级别: {{ .Labels.severity }}
          服务名称: {{ .Labels.service }}
          开始时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ if .EndsAt }}结束时间: {{ .EndsAt.Format "2006-01-02 15:04:05" }}{{ end }}
          {{ end }}
        html: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>监控告警</title>
          </head>
          <body>
              <h2>用户中心监控告警</h2>
              <table border="1" style="border-collapse: collapse;">
                  <tr>
                      <th>告警名称</th>
                      <th>告警详情</th>
                      <th>告警级别</th>
                      <th>服务名称</th>
                      <th>开始时间</th>
                  </tr>
                  {{ range .Alerts }}
                  <tr>
                      <td>{{ .Annotations.summary }}</td>
                      <td>{{ .Annotations.description }}</td>
                      <td>{{ .Labels.severity }}</td>
                      <td>{{ .Labels.service }}</td>
                      <td>{{ .StartsAt.Format "2006-01-02 15:04:05" }}</td>
                  </tr>
                  {{ end }}
              </table>
          </body>
          </html>

  # 关键告警接收器
  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '🚨【紧急告警】{{ .GroupLabels.alertname }}'
        body: |
          ⚠️ 紧急告警通知 ⚠️
          
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          详情: {{ .Annotations.description }}
          服务: {{ .Labels.service }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          
          请立即处理！
          {{ end }}
    webhook_configs:
      - url: 'http://your-webhook-url/critical'
        send_resolved: true
        http_config:
          basic_auth:
            username: 'webhook-user'
            password: 'webhook-password'
    # 企业微信通知（可选）
    wechat_configs:
      - corp_id: 'your-corp-id'
        to_party: '1'
        agent_id: 'your-agent-id'
        api_secret: 'your-api-secret'
        message: |
          🚨 紧急告警
          {{ range .Alerts }}
          {{ .Annotations.summary }}
          {{ .Annotations.description }}
          {{ end }}

  # 警告级别接收器
  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '⚠️【警告告警】{{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          详情: {{ .Annotations.description }}
          服务: {{ .Labels.service }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
    webhook_configs:
      - url: 'http://your-webhook-url/warning'
        send_resolved: true

  # 信息级别接收器
  - name: 'info-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: 'ℹ️【信息告警】{{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          信息: {{ .Annotations.summary }}
          详情: {{ .Annotations.description }}
          服务: {{ .Labels.service }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}

  # 业务告警接收器
  - name: 'business-alerts'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '📊【业务告警】{{ .GroupLabels.alertname }}'
        body: |
          业务监控告警通知
          
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          详情: {{ .Annotations.description }}
          服务: {{ .Labels.service }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          
          建议检查业务指标和用户行为。
          {{ end }}
    webhook_configs:
      - url: 'http://your-webhook-url/business'
        send_resolved: true

# 抑制规则
inhibit_rules:
  # 应用停机时抑制其他告警
  - source_match:
      alertname: ApplicationDown
    target_match:
      service: user-center
    equal: ['service']
    
  # 数据库连接问题时抑制相关告警
  - source_match:
      alertname: DatabaseConnectionIssue
    target_match_re:
      alertname: SlowDatabaseQueries|HighAPIResponseTime
    equal: ['service']
    
  # Redis连接问题时抑制缓存相关告警
  - source_match:
      alertname: RedisConnectionIssue
    target_match:
      alertname: LowCacheHitRate
    equal: ['service']
