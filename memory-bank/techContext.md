# 技术上下文 - 用户中心服务

## 技术栈

### 核心框架
- **Spring Boot**: 3.2.0 (主框架)
- **Spring Security**: 用户认证和授权
- **Spring AOP**: 切面编程，实现审计日志等横切关注点
- **Spring Data Redis**: Redis数据访问

### 数据访问层
- **MyBatis Plus**: 3.5.11 (ORM框架)
  - `mybatis-plus-spring-boot3-starter`: Spring Boot 3集成
  - `mybatis-plus-jsqlparser`: SQL解析器支持
- **MySQL Connector**: 8.0.32 (数据库驱动)

### 服务治理
- **Spring Cloud**: 2023.0.0
- **Spring Cloud Alibaba**: 2022.0.0.0
- **Nacos**: 2.1.2 (服务发现与配置中心)

### 缓存与会话
- **Redis**: 数据缓存和会话存储
- **Spring Data Redis**: Redis操作封装

### 监控与运维
- **Spring Boot Actuator**: 健康检查和监控端点
- **Micrometer Prometheus**: 指标收集和监控

### 文档与调试
- **Knife4j**: 4.5.0 (OpenAPI 3 / Swagger文档)
- **SpringDoc**: API文档生成

### 工具库
- **Lombok**: 代码简化
- **HuTool**: 5.8.29 (Java工具类库)
- **FastJSON2**: 2.0.52 (JSON处理)
- **JWT**: 0.11.5 (JsonWebToken实现)

### 验证与校验
- **Spring Boot Validation**: 数据校验
- **Hibernate Validator**: JSR-303实现

## 开发环境

### 运行环境要求
- **JDK**: 17+ (必需)
- **Maven**: 3.6+ (构建工具)
- **MySQL**: 8.0+ (主数据库)
- **Redis**: 6.0+ (缓存)
- **Nacos**: 2.1+ (服务发现)

### 开发工具
- **IDE**: IntelliJ IDEA / Eclipse / VS Code
- **数据库工具**: Navicat / DataGrip / phpMyAdmin
- **API测试**: Postman / Knife4j Swagger UI
- **版本控制**: Git

### 容器化支持
- **Docker**: 容器化部署
- **Kubernetes**: 容器编排(可选)

## 项目结构

### Maven模块组织
```
user-center/
├── pom.xml                                    # 主POM文件
├── src/main/java/com/tinyzk/user/center/      # Java源码
│   ├── UserCenterApplication.java             # 启动类
│   ├── common/                                # 通用组件
│   │   ├── annotation/                        # 自定义注解
│   │   ├── aop/                              # AOP切面
│   │   ├── enums/                            # 枚举类
│   │   ├── event/                            # 事件处理
│   │   ├── exception/                        # 异常处理
│   │   └── filter/                           # 过滤器
│   ├── config/                               # 配置类
│   │   ├── AsyncConfig.java                  # 异步配置
│   │   ├── SecurityConfig.java               # 安全配置
│   │   └── RedisConfig.java                  # Redis配置
│   ├── controller/                           # 控制器层
│   ├── service/                              # 业务逻辑层
│   ├── mapper/                               # 数据访问层
│   ├── entity/                               # 实体类
│   ├── dto/                                  # 数据传输对象
│   ├── vo/                                   # 视图对象
│   └── util/                                 # 工具类
├── src/main/resources/                       # 资源文件
│   ├── application.yml                       # 主配置文件
│   ├── application-*.yml                     # 环境配置
│   └── mapper/                               # MyBatis映射文件
├── src/test/                                 # 测试代码
└── target/                                   # 构建输出
```

### 包命名规范
- **Controller**: `com.tinyzk.user.center.controller`
- **Service**: `com.tinyzk.user.center.service`
- **Service Impl**: `com.tinyzk.user.center.service.impl`
- **Mapper**: `com.tinyzk.user.center.mapper`
- **Entity**: `com.tinyzk.user.center.entity`
- **DTO**: `com.tinyzk.user.center.dto`
- **VO**: `com.tinyzk.user.center.vo`

## 配置管理

### 环境配置
- **本地开发**: `application-local.yml`
- **测试环境**: `application-test.yml`
- **生产环境**: `application-prod.yml`

### 配置中心
- **Nacos Config**: 集中配置管理
- **动态配置**: 支持配置热更新
- **配置分组**: 按环境和功能分组管理

### 关键配置项
```yaml
# 数据库配置
spring:
  datasource:
    url: ***************************************
    username: ${DB_USER:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver

# Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0

# Nacos配置
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_ADDR:localhost:8848}
      config:
        server-addr: ${NACOS_ADDR:localhost:8848}
        file-extension: yml
```

## 数据库设计

### 核心表结构
1. **用户基础表** (`user_base`)
   - 用户ID、昵称、状态等基础信息

2. **用户认证表** (`user_auth`)
   - 登录凭证、密码、认证类型

3. **用户资料表** (`user_profile`)
   - 个人详细信息、联系方式

4. **用户经历表**
   - `user_education`: 教育经历
   - `user_work_history`: 工作经历
   - `user_training`: 培训经历

5. **客户端管理表**
   - `oauth_client_details`: OAuth客户端信息
   - `user_external_mapping`: 外部系统用户映射

6. **审计日志表** (`user_audit_log`)
   - 用户操作审计记录

### 数据库优化策略
- **索引优化**: 基于查询模式创建复合索引
- **分页查询**: 使用MyBatis Plus分页插件
- **连接池**: HikariCP高性能连接池
- **读写分离**: 支持主从库分离(可选)

## 安全设计

### 认证机制
- **JWT Token**: 无状态令牌认证
- **刷新令牌**: 长期有效的刷新机制
- **多因子认证**: 支持邮箱、手机验证

### 数据安全
- **加密存储**: AES加密敏感数据
- **密码安全**: BCrypt哈希算法
- **传输安全**: HTTPS强制加密

### 权限控制
- **RBAC模型**: 基于角色的访问控制
- **客户端权限**: OAuth2客户端权限范围
- **数据权限**: 基于客户端的数据访问控制

## 性能优化

### 缓存策略
- **Redis缓存**: 用户信息、会话数据
- **本地缓存**: JVM级别的热点数据缓存
- **缓存更新**: 写入时更新，TTL过期策略

### 异步处理
- **线程池配置**: 自定义线程池处理异步任务
- **事件驱动**: Spring Event异步处理非关键操作
- **消息队列**: RocketMQ处理大量异步任务(可选)

### 数据库性能
- **连接池优化**: HikariCP配置调优
- **查询优化**: 避免N+1查询，使用批量操作
- **索引策略**: 基于实际查询模式优化索引

## 监控与运维

### 健康检查
- **Actuator端点**: `/actuator/health`
- **自定义检查**: 数据库、Redis连接状态
- **依赖服务**: Nacos、外部服务健康状态

### 指标监控
- **Prometheus集成**: `/actuator/prometheus`
- **业务指标**: 用户注册数、登录成功率等
- **技术指标**: 响应时间、错误率、吞吐量

### 日志管理
- **日志级别**: 生产环境INFO级别
- **日志格式**: 结构化JSON格式
- **日志收集**: 支持ELK Stack集成

## 部署架构

### 容器化部署
```dockerfile
FROM openjdk:17-jre-slim
COPY target/user-center.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 环境变量
- `APP_PROFILE`: 运行环境(local/test/prod)
- `JAVA_OPTS`: JVM参数配置
- `DB_*`: 数据库连接参数
- `REDIS_*`: Redis连接参数
- `NACOS_ADDR`: Nacos服务地址

### 扩展性设计
- **水平扩展**: 无状态设计，支持多实例部署
- **负载均衡**: Nginx/Kong API网关
- **服务发现**: Nacos自动注册和发现

## 开发规范

### 代码规范
- **Java代码**: 遵循阿里巴巴Java开发规范
- **数据库**: 遵循MySQL命名规范
- **API设计**: RESTful API设计规范

### 测试策略
- **单元测试**: JUnit 5 + Mockito
- **集成测试**: Spring Boot Test
- **API测试**: RestAssured
- **测试覆盖率**: 目标80%+

### 版本管理
- **Git Flow**: 特性分支开发模式
- **版本号**: 语义化版本(Semantic Versioning)
- **发布策略**: 蓝绿部署或滚动更新

## 技术债务与风险

### 已知限制
1. **单体架构**: 当前为单体应用，未来可能需要微服务拆分
2. **数据库单点**: 目前单库设计，大数据量时需要分库分表
3. **缓存依赖**: 强依赖Redis，需要高可用方案

### 技术风险
1. **Spring Boot 3.x**: 新版本可能存在兼容性问题
2. **JDK 17**: 需要确保部署环境支持
3. **依赖冲突**: 多个Spring Cloud组件版本协调

### 改进计划
1. **微服务化**: 按业务域拆分服务
2. **数据库优化**: 实施读写分离、分库分表
3. **监控完善**: 接入APM工具，完善链路追踪 