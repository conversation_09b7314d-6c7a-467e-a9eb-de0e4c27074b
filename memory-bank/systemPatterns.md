# 系统模式 - 用户中心服务

## 系统架构

### 整体架构模式
采用**分层架构模式**，结合**领域驱动设计(DDD)**思想：

```
┌─────────────────────────────────────────┐
│           用户界面层 (UI Layer)          │
│    Controller / REST API / Swagger      │
├─────────────────────────────────────────┤
│           应用服务层 (Service)           │
│   业务逻辑 / 事务管理 / 数据转换          │
├─────────────────────────────────────────┤
│           领域模型层 (Domain)            │
│     Entity / VO / DTO / Enum            │
├─────────────────────────────────────────┤
│          基础设施层 (Infrastructure)     │
│   Mapper / Cache / Config / Util        │
├─────────────────────────────────────────┤
│           数据存储层 (Data)              │
│      MySQL / Redis / Nacos             │
└─────────────────────────────────────────┘
```

### 核心组件架构

#### 1. 认证授权模块
```
┌─────────────────┐    ┌─────────────────┐
│  AuthController │────│  AuthService    │
└─────────────────┘    └─────────────────┘
         │                       │
         │                       ▼
         │              ┌─────────────────┐
         │              │  JwtTokenUtil   │
         │              └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│ SecurityConfig  │    │  UserAuthMapper │
└─────────────────┘    └─────────────────┘
```

#### 2. 用户资料模块
```
┌──────────────────┐    ┌──────────────────┐
│ UserProfileCtrl  │────│ UserProfileSvc   │
└──────────────────┘    └──────────────────┘
         │                        │
         │                        ▼
         │               ┌──────────────────┐
         │               │   CacheManager   │
         │               └──────────────────┘
         │                        │
         ▼                        ▼
┌──────────────────┐    ┌──────────────────┐
│   ProfileDTO     │    │ UserProfileMapper│
└──────────────────┘    └──────────────────┘
```

#### 3. 客户端权限模块
```
┌─────────────────┐    ┌─────────────────┐
│ ClientFilter    │────│ ClientService   │
└─────────────────┘    └─────────────────┘
         │                       │
         │                       ▼
         │              ┌─────────────────┐
         │              │ PermissionAOP   │
         │              └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│ ClientContext   │    │ ClientMapper    │
└─────────────────┘    └─────────────────┘
```

## 设计模式

### 1. 依赖注入模式 (Dependency Injection)
**应用场景**: 所有服务层和数据访问层
```java
@Service
public class UserProfileServiceImpl implements UserProfileService {
    @Autowired
    private UserProfileMapper userProfileMapper;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
}
```

### 2. 策略模式 (Strategy Pattern)
**应用场景**: 多种认证方式处理
```java
public interface AuthenticationStrategy {
    boolean authenticate(String identifier, String credential);
}

@Component
public class EmailAuthStrategy implements AuthenticationStrategy {
    // 邮箱认证逻辑
}

@Component  
public class PhoneAuthStrategy implements AuthenticationStrategy {
    // 手机认证逻辑
}
```

### 3. 工厂模式 (Factory Pattern)
**应用场景**: 创建不同类型的用户对象
```java
@Component
public class UserFactory {
    public UserBase createUser(UserRegistrationRequest request) {
        return UserBase.builder()
            .nickname(request.getNickname())
            .status(UserStatus.ACTIVE)
            .createTime(LocalDateTime.now())
            .build();
    }
}
```

### 4. 观察者模式 (Observer Pattern)
**应用场景**: 用户操作事件处理
```java
@EventListener
@Async
public void handleUserRegistrationEvent(UserRegistrationEvent event) {
    // 发送欢迎邮件
    // 记录用户行为
    // 更新统计信息
}
```

### 5. 模板方法模式 (Template Method)
**应用场景**: 数据验证和处理流程
```java
public abstract class BaseService<T> {
    public final ResponseResult<T> process(Object request) {
        validate(request);
        T result = doProcess(request);
        audit(request, result);
        return ResponseResult.success(result);
    }
    
    protected abstract T doProcess(Object request);
    protected abstract void validate(Object request);
    protected void audit(Object request, T result) {
        // 默认审计逻辑
    }
}
```

### 6. 装饰器模式 (Decorator Pattern)
**应用场景**: 缓存装饰器
```java
@Component
public class CachedUserProfileService implements UserProfileService {
    @Autowired
    private UserProfileService userProfileService;
    
    @Override
    @Cacheable(value = "user_profile", key = "#userId")
    public UserProfileVO getUserProfile(Long userId) {
        return userProfileService.getUserProfile(userId);
    }
}
```

## 关键技术决策

### 1. 数据访问层选择: MyBatis Plus
**决策理由**:
- 提供强大的CRUD操作，减少重复代码
- 支持代码生成，提高开发效率
- 灵活的SQL编写，支持复杂查询
- 良好的分页和性能优化支持

**实现模式**:
```java
public interface UserProfileMapper extends BaseMapper<UserProfile> {
    
    @Select("SELECT * FROM user_profile WHERE user_id = #{userId} AND status = 1")
    UserProfile selectByUserId(@Param("userId") Long userId);
    
    IPage<UserProfile> selectPageByCondition(Page<UserProfile> page, 
                                           @Param("condition") UserProfileQuery condition);
}
```

### 2. 缓存策略: Redis + 注解驱动
**决策理由**:
- 减少数据库访问压力
- 提升用户体验和响应速度
- 支持分布式缓存共享
- 简化缓存操作代码

**实现模式**:
```java
@Service
public class UserProfileServiceImpl implements UserProfileService {
    
    @Cacheable(value = "user_profile", key = "#userId", unless = "#result == null")
    public UserProfileVO getUserProfile(Long userId) {
        // 查询数据库
    }
    
    @CacheEvict(value = "user_profile", key = "#userId")
    public void updateUserProfile(Long userId, UpdateProfileDTO dto) {
        // 更新数据库
    }
}
```

### 3. 安全认证: JWT + Spring Security
**决策理由**:
- 无状态认证，支持分布式部署
- 标准化的安全框架，安全性高
- 灵活的权限控制机制
- 良好的生态系统支持

**实现模式**:
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http
            .csrf().disable()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .authorizeHttpRequests(auth -> auth
                .requestMatchers("/api/auth/**").permitAll()
                .anyRequest().authenticated()
            )
            .addFilterBefore(jwtAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class)
            .build();
    }
}
```

### 4. 异常处理: 全局异常拦截
**决策理由**:
- 统一错误响应格式
- 减少重复的异常处理代码
- 集中化日志记录
- 提供友好的错误信息

**实现模式**:
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public ResponseResult<Void> handleBusinessException(BusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        return ResponseResult.error(e.getCode(), e.getMessage());
    }
    
    @ExceptionHandler(ValidationException.class)
    public ResponseResult<Void> handleValidationException(ValidationException e) {
        log.warn("参数校验异常: {}", e.getMessage());
        return ResponseResult.error(ErrorCode.INVALID_PARAMETER, e.getMessage());
    }
}
```

### 5. 审计日志: AOP + 事件驱动
**决策理由**:
- 非侵入式的日志记录
- 异步处理，不影响主业务性能
- 统一的审计数据格式
- 便于后续分析和监控

**实现模式**:
```java
@Aspect
@Component
public class AuditLogAspect {
    
    @Around("@annotation(auditLog)")
    public Object audit(ProceedingJoinPoint joinPoint, AuditLog auditLog) throws Throwable {
        AuditLogEvent event = createAuditEvent(joinPoint, auditLog);
        
        try {
            Object result = joinPoint.proceed();
            event.setStatus(AuditStatus.SUCCESS);
            return result;
        } catch (Exception e) {
            event.setStatus(AuditStatus.FAILED);
            event.setErrorMessage(e.getMessage());
            throw e;
        } finally {
            applicationEventPublisher.publishEvent(event);
        }
    }
}
```

## 数据一致性策略

### 1. 缓存一致性
**模式**: Cache-Aside + Write-Through
```java
@Transactional
public void updateUserProfile(Long userId, UpdateProfileDTO dto) {
    // 1. 更新数据库
    userProfileMapper.updateById(userProfile);
    
    // 2. 删除缓存
    cacheManager.evict("user_profile", userId);
    
    // 3. 预热缓存(可选)
    getUserProfile(userId);
}
```

### 2. 分布式锁
**应用场景**: 防止并发更新冲突
```java
@Service
public class UserProfileServiceImpl {
    
    public void updateUserProfile(Long userId, UpdateProfileDTO dto) {
        String lockKey = "user:profile:lock:" + userId;
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            if (lock.tryLock(5, 10, TimeUnit.SECONDS)) {
                // 执行更新操作
                doUpdateUserProfile(userId, dto);
            } else {
                throw new BusinessException("系统繁忙，请稍后重试");
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
```

### 3. 事务管理
**模式**: 声明式事务 + 编程式事务
```java
@Service
@Transactional(rollbackFor = Exception.class)
public class UserRegistrationService {
    
    public UserBase registerUser(UserRegistrationRequest request) {
        // 1. 创建用户基础信息
        UserBase userBase = userBaseService.createUser(request);
        
        // 2. 创建认证信息
        userAuthService.createAuth(userBase.getId(), request);
        
        // 3. 创建默认资料
        userProfileService.createDefaultProfile(userBase.getId());
        
        return userBase;
    }
}
```

## 性能优化模式

### 1. 批量操作
```java
@Service
public class BatchOperationService {
    
    @Transactional
    public void batchUpdateUsers(List<UserUpdateRequest> requests) {
        List<UserProfile> profiles = requests.stream()
            .map(this::convertToProfile)
            .collect(Collectors.toList());
            
        // MyBatis Plus批量更新
        userProfileService.updateBatchById(profiles);
        
        // 批量清除缓存
        List<String> cacheKeys = requests.stream()
            .map(req -> "user_profile:" + req.getUserId())
            .collect(Collectors.toList());
        cacheManager.evict(cacheKeys);
    }
}
```

### 2. 异步处理
```java
@Service
public class AsyncProcessingService {
    
    @Async("taskExecutor")
    public CompletableFuture<Void> processUserEvent(UserEvent event) {
        // 异步处理用户事件
        return CompletableFuture.completedFuture(null);
    }
    
    @EventListener
    @Async
    public void handleUserRegistration(UserRegistrationEvent event) {
        // 异步发送欢迎邮件
        emailService.sendWelcomeEmail(event.getUserId());
        
        // 异步更新统计信息
        statisticsService.incrementUserCount();
    }
}
```

### 3. 延迟加载
```java
@Service
public class LazyLoadingService {
    
    public UserDetailVO getUserDetail(Long userId) {
        UserDetailVO detail = new UserDetailVO();
        
        // 基础信息立即加载
        detail.setBasicInfo(userBaseService.getUserBasic(userId));
        
        // 扩展信息延迟加载
        detail.setProfileLoader(() -> userProfileService.getUserProfile(userId));
        detail.setEducationLoader(() -> userEducationService.getUserEducations(userId));
        
        return detail;
    }
}
```

## 扩展性设计

### 1. 插件化架构
```java
public interface UserProfileProcessor {
    boolean supports(String profileType);
    void process(UserProfile profile);
}

@Service
public class UserProfileProcessorManager {
    @Autowired
    private List<UserProfileProcessor> processors;
    
    public void processProfile(UserProfile profile) {
        processors.stream()
            .filter(processor -> processor.supports(profile.getType()))
            .forEach(processor -> processor.process(profile));
    }
}
```

### 2. 配置驱动
```java
@ConfigurationProperties(prefix = "user.center")
@Data
public class UserCenterProperties {
    private Authentication authentication = new Authentication();
    private Cache cache = new Cache();
    private Security security = new Security();
    
    @Data
    public static class Authentication {
        private int tokenExpireMinutes = 60;
        private int refreshTokenExpireDays = 30;
    }
}
```

### 3. 事件驱动架构
```java
@Component
public class UserEventPublisher {
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    public void publishUserRegistered(Long userId) {
        eventPublisher.publishEvent(new UserRegisteredEvent(userId));
    }
    
    public void publishProfileUpdated(Long userId, String field) {
        eventPublisher.publishEvent(new ProfileUpdatedEvent(userId, field));
    }
}
```

## 监控与可观测性

### 1. 指标监控
```java
@Service
public class MetricsService {
    private final MeterRegistry meterRegistry;
    private final Counter userRegistrationCounter;
    private final Timer userLoginTimer;
    
    public MetricsService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.userRegistrationCounter = Counter.builder("user.registration.count")
            .description("用户注册计数")
            .register(meterRegistry);
        this.userLoginTimer = Timer.builder("user.login.duration")
            .description("用户登录耗时")
            .register(meterRegistry);
    }
}
```

### 2. 链路追踪
```java
@Service
public class TracingService {
    
    @NewSpan("user-profile-query")
    public UserProfileVO getUserProfile(@SpanTag("userId") Long userId) {
        return userProfileService.getUserProfile(userId);
    }
}
```

### 3. 健康检查
```java
@Component
public class UserCenterHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        return Health.up()
            .withDetail("database", checkDatabase())
            .withDetail("redis", checkRedis())
            .withDetail("nacos", checkNacos())
            .build();
    }
} 