<!DOCTYPE html>
<html lang="zh-CN" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户中心项目变更日志 - Roc</title>
    <script src="https://cdn.tailwindcss.com/3.4.3"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        /* 基本样式和自定义 */
        body {
            @apply bg-gray-50 text-gray-800 dark:bg-gray-900 dark:text-gray-200 transition-colors duration-300;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        /* 滚动条美化 (可选) */
        ::-webkit-scrollbar {
            width: 6px;
        }
        ::-webkit-scrollbar-track {
            @apply bg-gray-200 dark:bg-gray-700;
        }
        ::-webkit-scrollbar-thumb {
            @apply bg-blue-500 dark:bg-blue-600 rounded;
        }
        ::-webkit-scrollbar-thumb:hover {
            @apply bg-blue-600 dark:bg-blue-700;
        }

        /* 微交互：卡片悬停 */
        .changelog-card {
            @apply transition-all duration-300 ease-in-out;
        }
        .changelog-card:hover {
            @apply shadow-lg ring-1 ring-blue-500/30 dark:ring-blue-400/40 transform scale-[1.01];
        }

        /* 微交互：按钮悬停 */
        .theme-toggle-btn {
            @apply transition-all duration-200 ease-in-out;
        }
        .theme-toggle-btn:hover {
            @apply scale-110 text-blue-600 dark:text-blue-400;
        }

        /* 微交互：内容淡入 (使用JS控制添加 'loaded' 类) */
        .fade-in-section {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .fade-in-section.loaded {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body class="min-h-screen flex flex-col">

    <div class="flex-grow container mx-auto px-4 py-8 md:py-16">

        <header class="text-center mb-12 md:mb-16 fade-in-section">
            <h1 class="text-3xl md:text-5xl font-bold text-gray-900 dark:text-white mb-3">用户中心项目变更日志</h1>
            <p class="text-lg text-gray-600 dark:text-gray-400">记录项目迭代的重要里程碑和更新。</p>
        </header>

        <button id="theme-toggle" aria-label="切换深色/浅色模式" class="fixed top-4 right-4 p-2 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 shadow-md theme-toggle-btn z-50">
            <i class="fas fa-sun text-xl hidden dark:inline"></i>
            <i class="fas fa-moon text-xl inline dark:hidden"></i>
        </button>

        <section class="changelog-entry fade-in-section" style="transition-delay: 150ms;">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden changelog-card mb-12 p-6 md:p-8">
                <div class="flex justify-between items-center mb-6 border-b border-gray-200 dark:border-gray-700 pb-4">
                    <h2 class="text-2xl md:text-3xl font-semibold text-blue-600 dark:text-blue-400">[1.0.2]</h2>
                    <span class="text-sm md:text-base text-gray-500 dark:text-gray-400">2025-05-15</span>
                </div>

                <div class="space-y-6">
                    <div>
                        <h3 class="text-xl font-medium text-green-600 dark:text-green-400 mb-3 flex items-center">
                            <i class="fas fa-plus-circle mr-2"></i>新增 (Added)
                        </h3>
                        <ul class="list-disc list-outside pl-5 space-y-2 text-gray-700 dark:text-gray-300">
                            <li>
                                <strong>用户管理:</strong>
                                <ul class="list-['-_'] list-outside pl-6 mt-1 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                    <li>添加用户管理相关功能和服务</li>
                                </ul>
                            </li>
                            <li>
                                <strong>数据同步:</strong>
                                <ul class="list-['-_'] list-outside pl-6 mt-1 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                    <li>添加flink数据同步任务</li>
                                    <li>任务1: 快结荐用户，sync-giguser-2-userbase</li>
                                    <li>任务2: 1号职场用户，sync-tzkuser-2-userbase 80w+</li>
                                    <li>任务3: 外部用户映射，user-mapping</li>
                                </ul>
                            </li>
                        </ul>
                    </div>

                    <div>
                        <h3 class="text-xl font-medium text-yellow-600 dark:text-yellow-400 mb-3 flex items-center">
                            <i class="fas fa-exchange-alt mr-2"></i>变更 (Changed)
                        </h3>
                        <ul class="list-disc list-outside pl-5 space-y-2 text-gray-700 dark:text-gray-300">
                            <li>
                                <strong>用户资料:</strong>
                                <ul class="list-['-_'] list-outside pl-6 mt-1 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                    <li>优化用户资料查询接口</li>
                                </ul>
                            </li>
                            <li>
                                <strong>文档:</strong>
                                <ul class="list-['-_'] list-outside pl-6 mt-1 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                    <li>更新任务列表并添加用户管理功能PRD文档</li>
                                </ul>
                            </li>
                        </ul>
                    </div>

                    <div>
                        <h3 class="text-xl font-medium text-red-600 dark:text-red-400 mb-3 flex items-center">
                            <i class="fas fa-wrench mr-2"></i>修复 (Fixed)
                        </h3>
                        <p class="text-gray-700 dark:text-gray-300 pl-5">无</p>
                    </div>
                </div>
            </div>
        </section>


        <section class="changelog-entry fade-in-section" style="transition-delay: 150ms;">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden changelog-card mb-12 p-6 md:p-8">
                <div class="flex justify-between items-center mb-6 border-b border-gray-200 dark:border-gray-700 pb-4">
                    <h2 class="text-2xl md:text-3xl font-semibold text-blue-600 dark:text-blue-400">[1.0.1]</h2>
                    <span class="text-sm md:text-base text-gray-500 dark:text-gray-400">2025-05-07</span>
                </div>

                <div class="space-y-6">
                    <div>
                        <h3 class="text-xl font-medium text-green-600 dark:text-green-400 mb-3 flex items-center">
                            <i class="fas fa-plus-circle mr-2"></i>新增 (Added)
                        </h3>
                        <ul class="list-disc list-outside pl-5 space-y-2 text-gray-700 dark:text-gray-300">
                            <li>
                                <strong>用户管理功能:</strong>
                                <ul class="list-['-_'] list-outside pl-6 mt-1 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                    <li>添加用户管理相关功能和服务</li>
                                </ul>
                            </li>
                            <li>
                                <strong>用户个人资料功能:</strong>
                                <ul class="list-['-_'] list-outside pl-6 mt-1 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                    <li>添加用户个人资料查询与更新功能</li>
                                </ul>
                            </li>
                            <li>
                                <strong>用户经历管理功能:</strong>
                                <ul class="list-['-_'] list-outside pl-6 mt-1 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                    <li>添加用户工作经历管理功能</li>
                                    <li>新增用户项目经历相关功能模块</li>
                                    <li>新增用户培训经历相关功能模块</li>
                                    <li>新增用户兼职经历相关功能模块</li>
                                </ul>
                            </li>
                            <li>
                                <strong>用户联系方式功能:</strong>
                                <ul class="list-['-_'] list-outside pl-6 mt-1 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                    <li>新增用户联系方式管理功能</li>
                                </ul>
                            </li>
                        </ul>
                    </div>

                    <div>
                        <h3 class="text-xl font-medium text-yellow-600 dark:text-yellow-400 mb-3 flex items-center">
                            <i class="fas fa-exchange-alt mr-2"></i>变更 (Changed)
                        </h3>
                        <ul class="list-disc list-outside pl-5 space-y-2 text-gray-700 dark:text-gray-300">
                            <li>
                                <strong>用户资料优化:</strong>
                                <ul class="list-['-_'] list-outside pl-6 mt-1 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                    <li>移除工作地点字段并优化用户资料查询</li>
                                </ul>
                            </li>
                            <li>
                                <strong>文档更新:</strong>
                                <ul class="list-['-_'] list-outside pl-6 mt-1 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                    <li>更新任务列表并添加用户管理功能PRD文档</li>
                                </ul>
                            </li>
                        </ul>
                    </div>

                    <div>
                        <h3 class="text-xl font-medium text-red-600 dark:text-red-400 mb-3 flex items-center">
                            <i class="fas fa-wrench mr-2"></i>修复 (Fixed)
                        </h3>
                        <ul class="list-disc list-outside pl-5 space-y-2 text-gray-700 dark:text-gray-300">
                            <li>
                                <strong>Bug修复:</strong>
                                <ul class="list-['-_'] list-outside pl-6 mt-1 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                    <li>修复用户注册时邮箱验证的问题</li>
                                    <li>修复实名认证偶发失败的问题</li>
                                    <li>修复用户登录日志记录不完整的问题</li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section class="changelog-entry fade-in-section" style="transition-delay: 150ms;">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden changelog-card mb-12 p-6 md:p-8">
                <div class="flex justify-between items-center mb-6 border-b border-gray-200 dark:border-gray-700 pb-4">
                    <h2 class="text-2xl md:text-3xl font-semibold text-blue-600 dark:text-blue-400">[1.0.0]</h2>
                    <span class="text-sm md:text-base text-gray-500 dark:text-gray-400">2025-04-25</span>
                </div>

                <div class="space-y-6">
                    <div>
                        <h3 class="text-xl font-medium text-green-600 dark:text-green-400 mb-3 flex items-center">
                            <i class="fas fa-plus-circle mr-2"></i>新增 (Added)
                        </h3>
                        <ul class="list-disc list-outside pl-5 space-y-2 text-gray-700 dark:text-gray-300">
                            <li>
                                <strong>项目初始化与基础框架搭建:</strong>
                                <ul class="list-['-_'] list-outside pl-6 mt-1 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                    <li>创建Spring Boot基础项目结构</li>
                                    <li>配置Maven依赖（Spring Boot, Mybatis Plus, Spring Security等）</li>
                                    <li>配置Nacos服务发现与配置中心</li>
                                    <li>配置Redis和MySQL连接</li>
                                    <li>实现对称加密工具类</li>
                                    <li>配置Spring Security基础框架</li>
                                    <li>实现BCrypt密码加密策略</li>
                                </ul>
                            </li>
                             <li>
                                <strong>数据库设计与实现:</strong>
                                <ul class="list-['-_'] list-outside pl-6 mt-1 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                     <li>创建用户相关数据库表结构（user_base, user_auth, user_profile等）</li>
                                     <li>实现各实体类与Mapper接口</li>
                                 </ul>
                             </li>
                             <li>
                                <strong>用户基础功能:</strong>
                                 <ul class="list-['-_'] list-outside pl-6 mt-1 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                     <li>实现用户登录功能（支持多种方式登录）</li>
                                     <li>实现用户注册功能</li>
                                     <li>实现登录/注册日志记录</li>
                                 </ul>
                            </li>
                             <li>
                                <strong>实名认证功能:</strong>
                                <ul class="list-['-_'] list-outside pl-6 mt-1 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                     <li>实现实名认证服务</li>
                                     <li>设计实名认证API接口</li>
                                     <li>实现实名认证业务逻辑</li>
                                     <li>实现实名认证日志记录</li>
                                 </ul>
                            </li>
                        </ul>
                    </div>

                    <div>
                        <h3 class="text-xl font-medium text-yellow-600 dark:text-yellow-400 mb-3 flex items-center">
                            <i class="fas fa-exchange-alt mr-2"></i>变更 (Changed)
                        </h3>
                         <ul class="list-disc list-outside pl-5 space-y-2 text-gray-700 dark:text-gray-300">
                             <li>
                                <strong>安全配置优化:</strong>
                                <ul class="list-['-_'] list-outside pl-6 mt-1 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                     <li>禁用CSRF保护</li>
                                     <li>禁用HTTP Basic认证</li>
                                     <li>禁用表单登录</li>
                                     <li>配置无状态Session</li>
                                     <li>开放Swagger和Actuator端点</li>
                                 </ul>
                            </li>
                         </ul>
                    </div>

                    <div>
                        <h3 class="text-xl font-medium text-red-600 dark:text-red-400 mb-3 flex items-center">
                            <i class="fas fa-wrench mr-2"></i>修复 (Fixed)
                        </h3>
                        <p class="text-gray-700 dark:text-gray-300 pl-5">初始版本暂无修复内容。</p>
                    </div>
                </div>
            </div>
        </section>

        </div>

    <footer class="text-center py-6 border-t border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 fade-in-section" style="transition-delay: 300ms;">
        <p class="text-sm text-gray-600 dark:text-gray-400">
            作者: Roc
        </p>
        <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">
            &copy; 2025 Roc Inc. 保留所有权利。
        </p>
    </footer>

    <script>
        // 深色/浅色模式切换逻辑
        const themeToggleBtn = document.getElementById('theme-toggle');
        const htmlElement = document.documentElement;

        // 检查本地存储或系统偏好来设置初始主题
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)');
        let currentTheme = localStorage.getItem('theme') || (prefersDark.matches ? 'dark' : 'light');

        function applyTheme(theme) {
            if (theme === 'dark') {
                htmlElement.classList.add('dark');
            } else {
                htmlElement.classList.remove('dark');
            }
            localStorage.setItem('theme', theme); // 保存用户选择
        }

        // 初始化主题
        applyTheme(currentTheme);

        // 切换按钮事件监听
        themeToggleBtn.addEventListener('click', () => {
            currentTheme = htmlElement.classList.contains('dark') ? 'light' : 'dark';
            applyTheme(currentTheme);
        });

        // 监听系统主题变化
        prefersDark.addEventListener('change', (e) => {
            // 仅当用户未手动设置主题时才跟随系统
             if (!localStorage.getItem('theme')) {
                applyTheme(e.matches ? 'dark' : 'light');
             }
        });

        // 内容淡入动画 (简单实现)
        document.addEventListener('DOMContentLoaded', () => {
            const fadeElements = document.querySelectorAll('.fade-in-section');
            fadeElements.forEach(el => {
                // 使用 Intersection Observer 可以实现更精确的按需加载动画，这里为了简单起见，直接加载
                setTimeout(() => {
                    el.classList.add('loaded');
                }, parseInt(el.style.transitionDelay || '0')); // 尊重延迟
            });
        });

        // 图片懒加载 (如果需要，可以使用 Intersection Observer)
        // 这里未包含图片，所以未实现懒加载

    </script>

</body>
</html>