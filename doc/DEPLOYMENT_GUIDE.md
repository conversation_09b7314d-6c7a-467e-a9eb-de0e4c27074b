# 用户中心监控系统部署指南

## 概述

本指南将帮助您快速部署用户中心的完整监控和可观测性系统，包括：

- 🔍 **Prometheus** - 指标收集和存储
- 📊 **Grafana** - 数据可视化和仪表板
- 🚨 **AlertManager** - 智能告警管理
- 🔗 **Zipkin** - 分布式链路追踪
- 📈 **自定义业务指标** - 用户行为和系统性能监控
- 🏥 **健康检查** - 多层次系统健康监控

## 系统要求

### 硬件要求
- **CPU**: 最少2核，推荐4核
- **内存**: 最少4GB，推荐8GB
- **磁盘**: 最少10GB可用空间，推荐50GB
- **网络**: 稳定的网络连接

### 软件要求
- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+) 或 macOS
- **Docker**: 版本 20.10+
- **Docker Compose**: 版本 1.29+
- **Java**: JDK 17+ (用于运行用户中心应用)
- **Maven**: 3.6+ (用于构建应用)

### 端口要求
确保以下端口未被占用：
- `3000` - Grafana Web界面
- `9090` - Prometheus Web界面
- `9093` - AlertManager Web界面
- `9411` - Zipkin Web界面
- `9100` - Node Exporter (可选)
- `8080` - 用户中心应用
- `18080` - 用户中心管理端口

## 快速部署

### 方式一：一键部署脚本

```bash
# 1. 进入项目目录
cd /path/to/user_center

# 2. 给脚本执行权限
chmod +x monitoring-scripts/setup_monitoring.sh

# 3. 执行一键部署
./monitoring-scripts/setup_monitoring.sh
```

### 方式二：手动部署

#### 步骤1：准备配置文件

```bash
# 检查配置文件是否存在
ls -la monitoring-configs/
```

应该包含以下文件：
- `prometheus.yml` - Prometheus配置
- `alert_rules.yml` - 告警规则
- `alertmanager.yml` - 告警管理配置
- `docker-compose-monitoring.yml` - Docker编排文件

#### 步骤2：启动监控服务

```bash
# 进入配置目录
cd monitoring-configs

# 启动所有监控服务
docker-compose -f docker-compose-monitoring.yml up -d

# 查看服务状态
docker-compose -f docker-compose-monitoring.yml ps
```

#### 步骤3：启动用户中心应用

```bash
# 返回项目根目录
cd ..

# 编译应用
mvn clean compile

# 启动应用
mvn spring-boot:run
```

## 验证部署

### 1. 检查服务状态

```bash
# 检查Docker容器状态
docker ps

# 检查应用健康状态
curl http://localhost:18080/actuator/health

# 检查业务指标
curl http://localhost:8080/api/monitoring/metrics/overview
```

### 2. 访问监控界面

| 服务 | URL | 默认账号 |
|------|-----|----------|
| Grafana | http://localhost:3000 | admin/admin123 |
| Prometheus | http://localhost:9090 | 无需认证 |
| AlertManager | http://localhost:9093 | 无需认证 |
| Zipkin | http://localhost:9411 | 无需认证 |

### 3. 验证指标收集

在Prometheus界面 (http://localhost:9090) 中查询以下指标：

```promql
# 用户注册指标
user_registration_total

# 缓存命中率
cache_hit_rate

# HTTP请求指标
http_server_requests_seconds_count

# JVM内存使用
jvm_memory_used_bytes
```

## 配置详解

### Prometheus配置

主要配置文件：`monitoring-configs/prometheus.yml`

```yaml
# 关键配置项
global:
  scrape_interval: 15s        # 抓取间隔
  evaluation_interval: 15s    # 规则评估间隔

scrape_configs:
  - job_name: 'user-center'
    static_configs:
      - targets: ['localhost:18080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 10s
```

### Grafana配置

- **数据源**: 自动配置Prometheus数据源
- **仪表板**: 预置用户中心专用仪表板
- **用户管理**: 默认管理员账号 admin/admin123

### AlertManager配置

主要配置文件：`monitoring-configs/alertmanager.yml`

```yaml
# 告警路由配置
route:
  group_by: ['alertname', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 12h

# 接收器配置
receivers:
  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨【紧急告警】{{ .GroupLabels.alertname }}'
```

## 自定义配置

### 1. 修改告警规则

编辑 `monitoring-configs/alert_rules.yml`：

```yaml
groups:
  - name: custom-alerts
    rules:
      - alert: CustomBusinessAlert
        expr: your_custom_metric > threshold
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "自定义业务告警"
          description: "业务指标异常"
```

### 2. 添加自定义仪表板

1. 在Grafana中创建仪表板
2. 导出JSON配置
3. 保存到 `monitoring-configs/grafana/dashboards/`

### 3. 配置邮件告警

修改 `monitoring-configs/alertmanager.yml`：

```yaml
global:
  smtp_smarthost: 'your-smtp-server:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: 'your-username'
  smtp_auth_password: 'your-password'
```

## 运维管理

### 日常维护脚本

```bash
# 健康检查
./monitoring-scripts/health_check.sh

# 指标收集
./monitoring-scripts/metrics_collector.sh -r

# 查看服务状态
./monitoring-scripts/setup_monitoring.sh --status

# 重启监控服务
./monitoring-scripts/setup_monitoring.sh --restart
```

### 定时任务配置

系统会自动配置以下定时任务：

```cron
# 每5分钟执行健康检查
*/5 * * * * /path/to/monitoring-scripts/health_check.sh

# 每10分钟收集指标
*/10 * * * * /path/to/monitoring-scripts/metrics_collector.sh

# 每小时生成报告
0 * * * * /path/to/monitoring-scripts/metrics_collector.sh -r

# 每天清理过期文件
0 2 * * * /path/to/monitoring-scripts/metrics_collector.sh -c
```

### 日志管理

监控系统日志位置：

```bash
# Docker容器日志
docker-compose -f monitoring-configs/docker-compose-monitoring.yml logs

# 应用日志
tail -f logs/info.log
tail -f logs/error.log

# 健康检查日志
tail -f /var/log/user-center-health.log

# 指标收集日志
ls -la /var/log/user-center-metrics/
```

## 故障排查

### 常见问题

#### 1. 服务无法启动

```bash
# 检查端口占用
netstat -tlnp | grep -E "(3000|9090|9093|9411)"

# 检查Docker状态
docker ps -a
docker-compose -f monitoring-configs/docker-compose-monitoring.yml logs
```

#### 2. 指标数据缺失

```bash
# 检查应用健康状态
curl http://localhost:18080/actuator/health

# 检查Prometheus目标状态
curl http://localhost:9090/api/v1/targets

# 检查应用指标端点
curl http://localhost:18080/actuator/prometheus | grep user_
```

#### 3. 告警不工作

```bash
# 检查AlertManager配置
curl http://localhost:9093/api/v1/status

# 检查告警规则
curl http://localhost:9090/api/v1/rules

# 测试邮件配置
docker exec -it user-center-alertmanager amtool config routes test
```

### 性能优化

#### 1. Prometheus优化

```yaml
# prometheus.yml
global:
  scrape_interval: 30s      # 增加抓取间隔
  
storage:
  tsdb:
    retention.time: 15d     # 减少数据保留时间
    retention.size: 5GB     # 限制存储大小
```

#### 2. Grafana优化

```bash
# 增加Grafana内存限制
docker-compose.yml:
  grafana:
    environment:
      - GF_SERVER_ROOT_URL=http://localhost:3000
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    deploy:
      resources:
        limits:
          memory: 512M
```

## 安全配置

### 1. 网络安全

```yaml
# docker-compose-monitoring.yml
networks:
  monitoring:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### 2. 访问控制

```nginx
# nginx.conf
server {
    listen 80;
    
    location /grafana/ {
        auth_basic "Monitoring Access";
        auth_basic_user_file /etc/nginx/.htpasswd;
        proxy_pass http://grafana:3000/;
    }
}
```

### 3. 数据加密

```yaml
# alertmanager.yml
global:
  smtp_require_tls: true
  smtp_tls_config:
    insecure_skip_verify: false
```

## 扩展功能

### 1. 集成企业微信告警

```yaml
# alertmanager.yml
receivers:
  - name: 'wechat-alerts'
    wechat_configs:
      - corp_id: 'your-corp-id'
        to_party: '1'
        agent_id: 'your-agent-id'
        api_secret: 'your-api-secret'
```

### 2. 添加更多监控目标

```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'mysql-exporter'
    static_configs:
      - targets: ['localhost:9104']
      
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['localhost:9121']
```

### 3. 集成日志监控

```yaml
# 添加Loki和Promtail
version: '3.8'
services:
  loki:
    image: grafana/loki:latest
    ports:
      - "3100:3100"
      
  promtail:
    image: grafana/promtail:latest
    volumes:
      - ./logs:/var/log
```

## 备份和恢复

### 备份监控数据

```bash
# 备份Prometheus数据
docker exec user-center-prometheus tar czf /tmp/prometheus-backup.tar.gz /prometheus

# 备份Grafana配置
docker exec user-center-grafana tar czf /tmp/grafana-backup.tar.gz /var/lib/grafana
```

### 恢复监控数据

```bash
# 恢复Prometheus数据
docker cp backup/prometheus-backup.tar.gz user-center-prometheus:/tmp/
docker exec user-center-prometheus tar xzf /tmp/prometheus-backup.tar.gz -C /

# 恢复Grafana配置
docker cp backup/grafana-backup.tar.gz user-center-grafana:/tmp/
docker exec user-center-grafana tar xzf /tmp/grafana-backup.tar.gz -C /
```

## 总结

通过本部署指南，您可以：

1. ✅ 快速部署完整的监控系统
2. ✅ 实现业务指标的实时监控
3. ✅ 配置智能告警和通知
4. ✅ 建立分布式链路追踪
5. ✅ 实现系统健康检查
6. ✅ 进行日常运维管理

如需技术支持，请参考故障排查部分或联系技术团队。
