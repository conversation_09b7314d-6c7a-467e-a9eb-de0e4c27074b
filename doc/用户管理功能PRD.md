# 产品需求文档：用户管理功能

## 1. 概述

本需求文档旨在详细描述用户中心的用户管理功能。该功能模块主要面向管理员，提供对平台用户的查看、搜索、状态管理（如启用/禁用）等操作，以确保平台用户数据的有效管理和维护。

## 2. 项目背景与目标

### 2.1 项目背景
随着用户中心承载的业务和用户量的增长，需要一个集中、高效的用户管理后台功能，以便运营和管理人员能够方便地对用户账户进行必要的维护和管理操作。

### 2.2 项目目标
- **高效管理**：提供便捷的用户列表查询、筛选和用户详情查看功能。
- **状态控制**：支持对用户账户的启用和禁用操作。
- **操作审计**：所有管理员对用户的操作行为应被记录，确保可追溯。
- **接口支持**：为其他内部系统或前端管理界面提供标准化的API接口。

## 3. 功能需求

### 3.1 用户列表与查询
- **分页展示**：管理员可以分页查看系统中的所有用户列表。
- **列表字段**：列表中应至少包含用户ID、用户名（或昵称）、注册邮箱、手机号（部分打码）、注册时间、最后登录时间、账户状态（如：正常、禁用）。
- **条件筛选**：
    - 支持按用户ID、用户名、邮箱、手机号进行精确或模糊搜索。
    - 支持按账户状态（正常、禁用）筛选。
    - 支持按注册时间范围筛选。
- **排序功能**：支持按注册时间、最后登录时间等字段进行升序或降序排序。

### 3.2 用户详情查看
- 管理员点击用户列表中的某个用户，可以查看该用户的详细信息。
- 详细信息应包括所有基础资料、认证信息、关联的第三方账户信息、登录历史、操作日志等（根据实际已有的用户属性）。

### 3.3 用户状态管理
- **禁用用户**：
    - 管理员可以选择一个或多个用户进行禁用操作。
    - 禁用时，管理员需要填写禁用原因（可选，但建议记录）。
    - 用户被禁用后，不能登录系统，相关业务功能受限。
- **启用用户**：
    - 管理员可以选择一个或多个已被禁用的用户进行启用操作。
    - 用户被启用后，恢复正常登录和使用权限。

### 3.4 操作日志
- 所有管理员对用户管理功能的操作（如查询、禁用、启用用户）都应记录详细的操作日志。
- 日志内容应包括：操作人、操作时间、操作类型、操作对象（用户ID）、操作结果、备注信息（如禁用原因）等。

## 4. 非功能需求

### 4.1 性能需求
- 用户列表查询在百万级用户数据量下，响应时间应在3秒以内。
- 用户状态变更操作响应时间应在1秒以内。
- 获取用户详情API在返回完整数据时，应在合理时间内响应（例如，95%的请求在1秒内完成），具体指标需根据实际数据量和业务容忍度设定。需要重点关注关联查询的性能优化。

### 4.2 安全需求
- **权限控制**：只有授权的管理员才能访问用户管理功能。
- **数据保护**：敏感信息（如手机号、邮箱）在展示时应进行脱敏处理。
- **操作审计**：确保所有操作可追溯，防止未授权操作和数据泄露。
- API接口需进行安全认证（如基于JWT或OAuth2）。

### 4.3 可用性需求
- 管理界面应简洁易用，操作流程清晰。
- 提供明确的错误提示和操作指引。

### 4.4 可扩展性需求
- 系统设计应考虑未来可能增加的用户属性和管理功能。
- API接口设计应具有良好的兼容性和扩展性。

## 5. 技术栈与架构（建议）

### 5.1 技术栈
- **后端**：Java (版本参考项目已有版本，如 Java 17), Spring Boot, Spring Security, MyBatis/JPA
- **数据库**：参考项目已有数据库 (如 PostgreSQL, MySQL)
- **前端（管理界面）**：React/Vue/Angular (或项目已有技术栈)
- **API文档**：Swagger/OpenAPI

### 5.2 架构
- **API设计**：遵循 RESTful API 设计原则。
- **模块化**：用户管理功能应作为用户中心的一个独立模块，与其他模块低耦合。
- **服务化**：核心业务逻辑封装在Service层。

## 6. 数据库设计
参考项目已有数据库设计，确保用户管理功能的数据结构与其他模块保持一致。
相关文件： - `/Users/<USER>/Documents/user_center/src/main/java/com/tinyzk/user/center/entity/*`

## 7. API 端点设计 (V1)

本API设计基于对 `/Users/<USER>/Documents/user_center/src/main/java/com/tinyzk/user/center/entity/` 目录下实体类的分析，旨在确保API能够全面反映和管理用户数据。

### 7.1 获取用户列表
- **Endpoint**: `GET /api/v1/admin/users`
- **描述**: 分页获取用户列表，支持多种条件筛选和排序。
- **请求参数**:
    - `pageNumber` (int, optional, default: 1): 页码
    - `pageSize` (int, optional, default: 20): 每页数量
    - `userId` (long, optional): 用户ID (精确匹配 `UserBase.userId`)
    - `nickname` (string, optional): 用户昵称 (模糊匹配 `UserProfile.nickname`)
    - `identifier` (string, optional): 登录标识 (如邮箱、手机号，模糊匹配 `UserAuth.identifier`)
    - `status` (int, optional): 用户状态 (精确匹配 `UserBase.status`, e.g., 1-正常, 2-禁用)
    - `realNameVerified` (boolean, optional): 实名认证状态 (精确匹配 `UserBase.realNameVerified`)
    - `registeredAfter` (datetime, optional): 注册时间晚于 (`UserBase.createdAt`)
    - `registeredBefore` (datetime, optional): 注册时间早于 (`UserBase.createdAt`)
    - `lastLoginAfter` (datetime, optional): 最后登录时间晚于 (`UserAuth.lastLoginAt`)
    - `lastLoginBefore` (datetime, optional): 最后登录时间早于 (`UserAuth.lastLoginAt`)
    - `sortBy` (string, optional, default: 'createdAt'): 排序字段 (e.g., 'userId', 'createdAt', 'lastLoginAt', 'nickname')
    - `sortOrder` (string, optional, default: 'desc'): 排序顺序 ('asc', 'desc')
- **成功响应 (200 OK)**:
  ```json
  {
    "data": [
      {
        "userId": 1,
        "nickname": "user1",
        "identifiers": [
          {"type": "EMAIL", "value": "<EMAIL>"},
          {"type": "PHONE", "value": "138****1234"}
        ],
        "status": 1, // 1:正常, 2:禁用, 0:注销
        "realNameVerified": true,
        "createdAt": "2023-01-15T10:00:00Z",
        "lastLoginAt": "2023-10-01T12:00:00Z",
        "avatarUrl": "https://example.com/avatar.jpg"
      }
      // ... more users
    ],
    "pageNumber": 1,
    "pageSize": 20,
    "total": 150,
    "totalPages": 8
  }
  ```
- **备注**: `identifiers` 字段聚合了用户的主要登录凭证信息。列表展示的手机号、邮箱等应脱敏。

### 7.2 获取用户详情
- **Endpoint**: `GET /api/v1/admin/users/{userId}`
- **描述**: 获取指定用户的完整详细信息。
- **路径参数**: 
    - `userId` (long): 用户ID (`UserBase.userId`)
- **成功响应 (200 OK)**:
  ```json
  {
    "userBase": {
      "userId": 1,
      "realNameVerified": true,
      "realName": "张三", // 注意：敏感信息，按需返回或脱敏
      "idCardNumber": "310********1234", // 注意：极度敏感信息，强烈建议不直接暴露，或严格加密/脱敏
      "status": 1, // 1:正常, 2:禁用, 0:注销/逻辑删除, 3:已合并
      "createdAt": "2023-01-15T10:00:00Z",
      "updatedAt": "2023-09-01T11:00:00Z",
      "deletedAt": null
    },
    "userProfile": {
      "profileId": 101,
      "nickname": "user1",
      "avatarUrl": "https://example.com/avatar.jpg",
      "gender": 1, // 0:未知, 1:男, 2:女
      "birthday": "1990-01-01",
      "nationality": "中国",
      "ethnicity": "汉族",
      "specialStatus": null,
      "politicalStatus": 1, // 1-中共党员...
      "maritalStatus": 2, // 1-未婚, 2-已婚...
      "fertilityStatus": 2, // 1-未育, 2-已育...
      "healthStatus": 1, // 1-健康...
      "regionCode": "310000",
      "regionName": "上海市",
      "address": "某某区某某街道123号",
      "bio": "资深软件工程师",
      "createdAt": "2023-01-15T10:05:00Z",
      "updatedAt": "2023-09-01T11:05:00Z"
    },
    "userAuths": [
      {
        "authId": 201,
        "identityType": "EMAIL",
        "identifier": "<EMAIL>",
        "verified": 1,
        "lastLoginAt": "2023-10-01T12:00:00Z",
        "createdAt": "2023-01-15T09:50:00Z"
      },
      {
        "authId": 202,
        "identityType": "PHONE",
        "identifier": "13800138000",
        "verified": 1,
        "lastLoginAt": "2023-09-28T10:00:00Z",
        "createdAt": "2023-02-10T14:00:00Z"
      }
    ],
    "userContactMethods": [
      {
        "contactId": 301,
        "contactType": 4, // 4-微信
        "contactValue": "wxid_abcdefg",
        "label": "个人微信",
        "isVerified": true,
        "verifiedAt": "2023-03-01T10:00:00Z"
      }
    ],
    "userEducationHistories": [
      {
        "eduId": 401,
        "schoolName": "某某大学",
        "degree": "本科",
        "degreeLevel": 1,
        "major": "计算机科学与技术",
        "startDate": "2014-09-01",
        "endDate": "2018-06-30",
        "description": "成绩优秀，获得多次奖学金。",
        "clubExperience": "曾任计算机协会会长"
      }
    ],
    "userWorkHistories": [
      {
        "workId": 501,
        "companyName": "ABC科技有限公司",
        "position": "软件工程师",
        "department": "研发部",
        "startDate": "2018-07-01",
        "endDate": "2022-06-30",
        "description": "负责XX系统的后端开发...",
        "achievements": "独立完成XX模块，性能提升20%",
        "companyLogo": "https://example.com/logo_abc.png",
        "companyIndustry": "互联网"
      }
    ],
    "userProjectHistories": [
      {
        "projectId": 601,
        "projectName": "XX电商平台",
        "role": "后端开发负责人",
        "startDate": "2020-01-01",
        "endDate": "2020-12-31",
        "description": "负责电商平台的架构设计和核心模块开发...",
        "projectUrl": "https://example.com/projectX",
        "associatedOrganization": "ABC科技有限公司"
      }
    ],
    "userPartTimes": [
      {
        "partTimeId": 701,
        "partTimeName": "在线辅导老师",
        "partTimeType": 1, // 1-咨询
        "partTimeProvider": "XYZ教育平台",
        "startDate": "2022-07-01",
        "endDate": null, //进行中
        "description": "提供数学在线辅导服务"
      }
    ],
    "userTrainings": [
      {
        "trainingId": 801,
        "trainingName": "高级Java工程师培训",
        "trainingType": "线上培训",
        "trainingProvider": "DEF培训机构",
        "startDate": "2022-08-01",
        "endDate": "2022-10-31",
        "description": "系统学习了Spring Cloud全家桶及微服务架构"
      }
    ],
    "userRealNameAuth": {
        "authRecordId": 901,
        "submittedRealName": "张三", // 注意敏感信息
        "submittedIdCardNumber": "310********1234", // 注意敏感信息
        "authStatus": 1, // 1:已认证
        "authTime": "2023-01-20T15:00:00Z",
        "authChannel": "MANUAL_REVIEW"
    },
    "userExternalMappings": [
        {
            "mappingId": 1001,
            "externalSystemClientId": "some_other_system_client_id",
            "externalUserId": "external_user_abc_123",
            "metadata": "{\"status\": \"active\"}",
            "createdAt": "2023-05-01T10:00:00Z"
        }
    ]
  }
  ```
- **失败响应 (404 Not Found)**: 如果用户不存在。

- **性能考量点**：包括按需加载、异步处理、数据库优化、缓存策略以及GraphQL的潜在应用
### 7.3 创建新用户 (Admin)
- **Endpoint**: `POST /api/v1/admin/users`
- **描述**: 管理员创建新用户账户。
- **请求体**: 
  ```json
  {
    "identityType": "EMAIL", // 或 "PHONE", "USERNAME"
    "identifier": "<EMAIL>", // 邮箱地址、手机号或用户名
    "credential": "initialPassword123", // 初始密码，需哈希处理
    "nickname": "New User", // 可选, UserProfile.nickname
    "status": 1, // 可选, UserBase.status, 默认为1 (正常)
    "sendInvitation": false // 可选, 是否发送邀请邮件/短信 (业务逻辑)
  }
  ```
- **成功响应 (201 Created)**: 返回新创建用户的核心信息。
  ```json
  {
    "userId": 123,
    "nickname": "New User",
    "identifier": "<EMAIL>",
    "identityType": "EMAIL",
    "status": 1,
    "createdAt": "2024-03-15T10:00:00Z"
  }
  ```
- **失败响应 (400 Bad Request)**: 如果请求参数无效（如邮箱格式错误、标识符已存在）。
- **备注**: 创建用户时，至少会涉及 `UserBase`, `UserAuth`, 和 `UserProfile` (至少创建一条空的或带昵称的记录) 表的写入。

### 7.4 更新用户信息 (Admin)
- **Endpoint**: `PUT /api/v1/admin/users/{userId}`
- **描述**: 管理员更新指定用户的基本信息和部分资料。
- **路径参数**: 
    - `userId` (long): 用户ID (`UserBase.userId`)
- **请求体**: 允许部分更新，只传递需要修改的字段。
  ```json
  {
    "userBase": { // 可选, 更新 UserBase 表相关字段
      "status": 1, // 例如: 1-正常, 2-禁用
      "realNameVerified": true // 注意：实名认证状态的变更通常有严格流程
    },
    "userProfile": { // 可选, 更新 UserProfile 表相关字段
      "nickname": "Updated Nickname",
      "avatarUrl": "https://example.com/new_avatar.jpg",
      "gender": 1,
      "birthday": "1991-02-02",
      "bio": "An updated bio."
      // ... 其他UserProfile中允许管理员修改的字段
    }
    // 注意: 用户的认证信息 (UserAuth)、联系方式 (UserContactMethods)、
    // 工作/教育/项目等经历通常有独立的管理接口，或由用户自行管理。
    // 管理员接口通常只更新核心状态和基本资料。
  }
  ```
- **成功响应 (200 OK)**: 返回更新后的用户核心信息或完整的用户详情（同GET用户详情接口）。
- **失败响应 (404 Not Found)**: 如果用户不存在。
- **失败响应 (400 Bad Request)**: 如果请求参数无效。

### 7.5 禁用用户
- **Endpoint**: `PUT /api/v1/admin/users/{userId}/disable`
- **描述**: 禁用指定用户账户。实际操作是更新 `UserBase.status` 为禁用状态 (例如 2)。
- **路径参数**: 
    - `userId` (long): 用户ID (`UserBase.userId`)
- **请求体 (可选)**:
  ```json
  {
    "reason": "违反社区规定" // 禁用原因，可记录到 UserAuditLog.operationDetail
  }
  ```
- **成功响应 (200 OK)**:
  ```json
  {
    "message": "User disabled successfully.",
    "userId": 123,
    "newStatus": 2 // 返回新的状态
  }
  ```
- **失败响应 (404 Not Found)**: 如果用户不存在。
- **失败响应 (400 Bad Request)**: 如果用户已禁用或请求无效。

### 7.6 启用用户
- **Endpoint**: `PUT /api/v1/admin/users/{userId}/enable`
- **描述**: 启用指定用户账户。实际操作是更新 `UserBase.status` 为正常状态 (例如 1)。
- **路径参数**: 
    - `userId` (long): 用户ID (`UserBase.userId`)
- **成功响应 (200 OK)**:
  ```json
  {
    "message": "User enabled successfully.",
    "userId": 123,
    "newStatus": 1 // 返回新的状态
  }
  ```
- **失败响应 (404 Not Found)**: 如果用户不存在。
- **失败响应 (400 Bad Request)**: 如果用户已启用或请求无效。


## 8. 测试策略

- **单元测试**：针对 Service 层和关键工具类编写 JUnit 测试，确保逻辑正确性，覆盖率 > 80%。
- **集成测试**：针对 API 端点进行测试，验证请求处理、参数校验、业务逻辑和数据库交互的正确性。
- **接口测试**：使用 Postman 或类似工具对所有 API 端点进行全面测试，包括正常场景、异常场景和边界条件。
- **手动测试（UI层面）**：如果开发了管理界面，需进行手动测试，确保用户体验和功能完整性。

## 9. 编码规范
- 遵循项目已有的编码规范（例如《阿里巴巴 Java 开发手册》）。
- 类名、方法名、变量名采用清晰、一致的命名约定（如驼峰命名法）。
- 公共方法和复杂逻辑需添加 Javadoc 注释。
- 代码应保持简洁、可读、易维护。

## 10. 风险与挑战
- **数据量**：如果用户基数非常大，查询性能可能成为瓶颈，需要优化数据库查询和索引。
- **权限细化**：未来可能需要更细粒度的权限控制（如不同管理员管理不同用户群体），需在设计时预留扩展点。
- **与其他模块的集成**：用户状态的变更可能需要通知其他依赖用户状态的模块，需要考虑消息通知机制或事件驱动架构。

## 11. 未来展望
- 批量操作（批量禁用/启用）。
- 用户标签管理。
- 更丰富的用户行为分析和报表。