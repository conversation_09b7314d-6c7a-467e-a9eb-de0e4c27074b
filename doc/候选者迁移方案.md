让新系统 `user_base` 表按照其自身规则生成 `user_id`，然后使用 `user_external_mapping` 表来记录新旧 ID 之间的映射关系。

```sql
CREATE TABLE `user_external_mapping` (
  `mapping_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '映射记录主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '系统A (用户中心) 的用户ID (逻辑外键)',
  `external_system_client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '外部系统客户端ID (如此处用 ''LEGACY_SYSTEM'')',
  `external_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户在外部系统中的唯一标识符 (此处是旧 candidate.id)',
  `metadata` json DEFAULT NULL COMMENT '附加信息 (例如: 映射状态, 关联时间等)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  PRIMARY KEY (`mapping_id`),
  UNIQUE KEY `uk_external_identity` (`external_system_client_id`, `external_user_id`), -- 确保旧ID在旧系统来源中唯一
  KEY `idx_user_id` (`user_id`),
  KEY `idx_external_system_client_id` (`external_system_client_id`),
  KEY `idx_external_user_id` (`external_user_id`) -- 方便用旧ID查找新ID
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户与外部系统身份映射关系表';
```

**这种方法是更优的选择，因为它：**

1.  **避免了 ID 冲突:** 如果新旧系统 ID 范围或类型不同，可以避免冲突。
2.  **保持新系统 ID 纯净:** 新系统的 ID 生成规则（自增、UUID 等）不受旧数据干扰。
3.  **明确了数据来源:** `user_external_mapping` 表清晰地记录了哪些用户数据是从旧系统迁移过来的。

**迁移步骤：**

1.  **迁移 `candidate` 到 `user_base` 并填充 `user_external_mapping`:** 这是最关键的一步，通常**难以用一条简单的 `INSERT ... SELECT ...` SQL 完成**，因为需要在插入 `user_base` 后立刻获取新生成的 `user_id`，并用它和旧的 `candidate_id` 一起插入到 `user_external_mapping`。
    * **推荐方式:** 使用**Java代码**来处理。脚本会：
        * 查询旧 `candidate` 表的记录。
        * 为每条记录准备数据并 `INSERT` 到新 `user_base` 表（不指定 `user_id`，让其自动生成）。
        * 获取刚刚插入操作产生的 `new_user_id`。
        * 将 `new_user_id` 和对应的 `old_candidate_id` 插入到 `user_external_mapping` 表，同时指定一个代表旧系统的 `external_system_client_id` (例如 `'LEGACY_SYSTEM'`)。

2.  **迁移其他关联数据:** 一旦 `user_base` 和 `user_external_mapping` 被正确填充，迁移其他表（`user_profile`, `user_auth`, `user_contact_methods`, `user_education_history`, `user_work_history`, `user_project_history`）就可以通过 `JOIN user_external_mapping` 表来实现，将旧的 `candidate_id` 替换为新的 `user_id`。


**迁移脚本模板 (假设第 1 步已完成，`user_external_mapping` 已填充):**

```sql
-- --------------------------------------------
-- 迁移脚本 1: (此步强烈建议使用脚本或存储过程完成)
--  1. 从 candidate 插入数据到 user_base (让 user_id 自动生成)
--  2. 获取新生成的 user_id
--  3. 将 <新 user_id, 'LEGACY_SYSTEM', 旧 candidate.id> 插入到 user_external_mapping
--  -- 纯 SQL 示例 (仅插入 user_base, 映射需额外处理)
-- INSERT INTO user_base (status, created_at, updated_at, deleted_at, ...)
-- SELECT /* Map status */, COALESCE(STR_TO_DATE(c.dateAdded, '%Y-%m-%d %H:%i:%s'), NOW()), ... FROM candidate c WHERE ...;
-- -- 之后需要有逻辑填充 user_external_mapping
-- --------------------------------------------


-- --------------------------------------------
-- 迁移脚本 2: candidate -> user_profile (使用映射表)
-- 迁移用户基础资料
-- --------------------------------------------
INSERT INTO user_profile (
    user_id,        -- 从映射表获取新 user_id
    nickname,
    gender,
    birthday,
    bio,
    created_at,     -- 时间戳可以基于旧数据或映射时间
    updated_at,
    deleted_at      -- 从新的 user_base 获取删除状态
)
SELECT
    map.user_id,    -- 使用映射表中的新 user_id
    COALESCE(c.chineseName, c.englishName),
    -- TODO: 替换 gender 映射逻辑
    CASE c.gender WHEN 1 THEN 1 WHEN 0 THEN 2 ELSE 0 END,
    c.dateOfBirth,
    c.information,
    map.created_at, -- 使用映射记录的创建时间作为基准
    map.updated_at, -- 使用映射记录的更新时间作为基准
    ub.deleted_at   -- 获取新 user_base 记录的删除状态
FROM
    candidate c
JOIN
    user_external_mapping map ON c.id = map.external_user_id AND map.external_system_client_id = 'LEGACY_SYSTEM'
LEFT JOIN
    user_base ub ON map.user_id = ub.user_id -- 关联新的 user_base 获取 deleted_at
WHERE
    (c.is_deleted IS NULL OR c.is_deleted = 0); -- 可以只迁移未删除的，或者根据 ub.deleted_at

-- --------------------------------------------
-- 迁移脚本 3: candidate (主要手机/邮箱) -> user_auth (使用映射表)
-- --------------------------------------------
INSERT INTO user_auth (user_id, identity_type, identifier, verified, created_at, updated_at, deleted_at)
SELECT
    map.user_id, -- 使用新 user_id
    'PHONE',
    c.mobile,
    1,
    map.created_at,
    map.updated_at,
    ub.deleted_at
FROM
    candidate c
JOIN
    user_external_mapping map ON c.id = map.external_user_id AND map.external_system_client_id = 'LEGACY_SYSTEM'
LEFT JOIN
    user_base ub ON map.user_id = ub.user_id
WHERE
    c.mobile IS NOT NULL AND c.mobile != '' AND (c.is_deleted IS NULL OR c.is_deleted = 0);

-- (对 EMAIL 重复类似操作)
INSERT INTO user_auth (user_id, identity_type, identifier, verified, created_at, updated_at, deleted_at)
SELECT
    map.user_id, -- 使用新 user_id
    'EMAIL',
    c.email,
    1,
    map.created_at,
    map.updated_at,
    ub.deleted_at
FROM
    candidate c
JOIN
    user_external_mapping map ON c.id = map.external_user_id AND map.external_system_client_id = 'LEGACY_SYSTEM'
LEFT JOIN
    user_base ub ON map.user_id = ub.user_id
WHERE
    c.email IS NOT NULL AND c.email != '' AND (c.is_deleted IS NULL OR c.is_deleted = 0);


-- --------------------------------------------
-- 迁移脚本 4: candidate (其他联系方式) -> user_contact_methods (使用映射表)
-- --------------------------------------------
INSERT INTO user_contact_methods (user_id, contact_type, contact_value, is_verified, created_at, updated_at, deleted_at)
SELECT map.user_id, 'PHONE_SECONDARY', c.mobile1, 0, map.created_at, map.updated_at, ub.deleted_at FROM candidate c JOIN user_external_mapping map ON c.id = map.external_user_id AND map.external_system_client_id = 'LEGACY_SYSTEM' LEFT JOIN user_base ub ON map.user_id = ub.user_id WHERE c.mobile1 IS NOT NULL AND c.mobile1 != '' AND (c.is_deleted IS NULL OR c.is_deleted = 0);
-- (对 mobile2, email1, email2, linkedin 重复类似操作)
-- ...


-- --------------------------------------------
-- 迁移脚本 5: candidateeducation -> user_education_history (使用映射表)
-- --------------------------------------------
INSERT INTO user_education_history (
    user_id, school_name, degree, major, start_date, end_date, description, visibility, created_at, updated_at, deleted_at
)
SELECT
    map.user_id, -- 使用新 user_id
    ce.school, ce.degree, ce.major,
    STR_TO_DATE(ce.start, '%Y-%m'), STR_TO_DATE(ce.end, '%Y-%m'),
    NULL, 'PRIVATE', NOW(), NOW(), ub.deleted_at
FROM
    candidateeducation ce
JOIN
    user_external_mapping map ON ce.candidate_id = map.external_user_id AND map.external_system_client_id = 'LEGACY_SYSTEM'
LEFT JOIN
    user_base ub ON map.user_id = ub.user_id;


-- --------------------------------------------
-- 迁移脚本 6: candidateexperience -> user_work_history (使用映射表)
-- --------------------------------------------
INSERT INTO user_work_history (
    user_id, company_name, position, start_date, end_date, description, location, visibility, created_at, updated_at, deleted_at
)
SELECT
    map.user_id, -- 使用新 user_id
    COALESCE(cl.name, 'Unknown Company'), cx.title,
    STR_TO_DATE(cx.start, '%Y-%m'), STR_TO_DATE(cx.end, '%Y-%m'),
    cx.description, NULL, 'PRIVATE', NOW(), NOW(), ub.deleted_at
FROM
    candidateexperience cx
JOIN
    user_external_mapping map ON cx.candidate_id = map.external_user_id AND map.external_system_client_id = 'LEGACY_SYSTEM'
LEFT JOIN
    client cl ON cx.client_id = cl.id
LEFT JOIN
    user_base ub ON map.user_id = ub.user_id;


-- --------------------------------------------
-- 迁移脚本 7: candidateproject -> user_project_history (使用映射表)
-- --------------------------------------------
INSERT INTO user_project_history (
    user_id, project_name, role, start_date, end_date, description, project_url, associated_organization, visibility, created_at, updated_at, deleted_at
)
SELECT
    map.user_id, -- 使用新 user_id
    cp.name, cp.title,
    STR_TO_DATE(cp.start, '%Y-%m'), STR_TO_DATE(cp.end, '%Y-%m'),
    cp.description, NULL, NULL, 'PRIVATE', NOW(), NOW(), ub.deleted_at
FROM
    candidateproject cp
JOIN
    user_external_mapping map ON cp.candidate_id = map.external_user_id AND map.external_system_client_id = 'LEGACY_SYSTEM'
LEFT JOIN
    user_base ub ON map.user_id = ub.user_id;

```

**核心变化:**

* 所有 `INSERT INTO new_table ...` 语句现在都包含一个 `JOIN user_external_mapping map ON old_table.candidate_id = map.external_user_id AND map.external_system_client_id = 'LEGACY_SYSTEM'` 来获取新系统的 `user_id` (`map.user_id`)。
* 你需要确保 `user_external_mapping` 表已经正确、完整地填充了新旧 ID 的对应关系。
* 时间戳 (`created_at`, `updated_at`) 和删除状态 (`deleted_at`) 现在可以考虑从新 `user_base` 表或 `user_external_mapping` 表获取，以保持一致性。脚本中做了相应调整（例如使用 `map.created_at` 或 `ub.deleted_at`）。

**强烈建议:**

由于步骤 1（填充 `user_base` 和 `user_external_mapping`）的复杂性，**强烈建议使用你熟悉的后端语言（如 Java）编写一个迁移程序**来执行整个迁移过程。这样可以更好地控制事务、处理错误、记录日志，并精确地管理 ID 的生成和映射。纯 SQL 脚本在这里作为逻辑说明和参考。