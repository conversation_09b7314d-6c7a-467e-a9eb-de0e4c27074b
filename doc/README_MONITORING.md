# 用户中心监控和可观测性系统

## 🎯 项目概述

本项目为用户中心Spring Boot应用提供了企业级的监控和可观测性解决方案，包含完整的指标收集、健康检查、链路追踪和智能告警功能。

## ✨ 核心特性

### 🔍 自定义业务指标
- **用户行为监控**: 注册、登录、资料更新等关键业务指标
- **系统性能监控**: 缓存命中率、数据库查询时间、API响应时间
- **实时指标**: 活跃会话数、待处理操作数、系统资源使用率

### 🏥 多层次健康检查
- **数据库健康检查**: MySQL连接状态、查询性能、连接数统计
- **Redis健康检查**: 连接状态、读写测试、性能监控
- **业务逻辑检查**: 关键业务表访问性、缓存预热状态

### 🔗 分布式链路追踪
- **现代化技术栈**: Micrometer Tracing + OpenTelemetry
- **全链路追踪**: HTTP请求、数据库操作、缓存操作、业务方法
- **性能分析**: 请求耗时分析、瓶颈识别、依赖关系可视化

### 🚨 智能告警系统
- **多级告警**: 关键、警告、信息三级告警机制
- **多渠道通知**: 邮件、Webhook、企业微信（可选）
- **告警抑制**: 智能告警抑制规则，避免告警风暴

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户中心应用    │────│   Prometheus    │────│    Grafana      │
│                 │    │   (指标收集)     │    │   (数据可视化)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │  AlertManager   │              │
         └──────────────│   (告警管理)     │──────────────┘
                        └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │     Zipkin      │
                        │   (链路追踪)     │
                        └─────────────────┘
```

## 📁 项目结构

```
user_center/
├── src/main/java/com/tinyzk/user/center/
│   ├── service/BusinessMetricsService.java      # 业务指标服务
│   ├── config/CustomHealthIndicator.java       # 自定义健康检查
│   ├── config/TracingConfiguration.java        # 链路追踪配置
│   ├── aspect/MetricsAspect.java               # 指标收集切面
│   ├── config/MonitoringConfiguration.java     # 监控配置
│   └── controller/MonitoringController.java    # 监控API控制器
├── monitoring-configs/                          # 监控配置文件
│   ├── prometheus.yml                          # Prometheus配置
│   ├── alert_rules.yml                         # 告警规则
│   ├── alertmanager.yml                        # 告警管理配置
│   └── docker-compose-monitoring.yml           # Docker编排文件
├── monitoring-scripts/                         # 运维脚本
│   ├── setup_monitoring.sh                    # 一键部署脚本
│   ├── health_check.sh                        # 健康检查脚本
│   └── metrics_collector.sh                   # 指标收集脚本
└── docs/                                       # 文档
    ├── MONITORING_GUIDE.md                    # 使用指南
    ├── DEPLOYMENT_GUIDE.md                    # 部署指南
    └── MONITORING_OBSERVABILITY.md            # 实施文档
```

## 🚀 快速开始

### 1. 一键部署监控系统

```bash
# 给脚本执行权限
chmod +x monitoring-scripts/setup_monitoring.sh

# 执行一键部署
./monitoring-scripts/setup_monitoring.sh
```

### 2. 启动用户中心应用

```bash
# 编译应用
mvn clean compile

# 启动应用
mvn spring-boot:run
```

### 3. 验证部署

```bash
# 检查应用健康状态
curl http://localhost:18080/actuator/health

# 查看业务指标概览
curl http://localhost:8080/api/monitoring/metrics/overview
```

## 🌐 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| **Grafana仪表板** | http://localhost:3000 | 数据可视化 (admin/admin123) |
| **Prometheus监控** | http://localhost:9090 | 指标查询和管理 |
| **AlertManager** | http://localhost:9093 | 告警管理 |
| **Zipkin追踪** | http://localhost:9411 | 链路追踪分析 |
| **用户中心API** | http://localhost:8080 | 业务API |
| **监控API** | http://localhost:8080/api/monitoring | 监控数据API |
| **健康检查** | http://localhost:18080/actuator/health | 应用健康状态 |

## 📊 核心指标

### 业务指标
- `user.registration.total` - 用户注册总数
- `user.login.total` - 用户登录成功总数
- `user.login.failure.total` - 用户登录失败总数
- `user.profile.update.total` - 用户资料更新总数
- `user.sessions.active` - 活跃用户会话数

### 性能指标
- `cache.hit.rate` - 缓存命中率
- `database.query.duration` - 数据库查询时间
- `http.server.requests.seconds` - HTTP请求响应时间
- `jvm.memory.used.bytes` - JVM内存使用量

### 系统指标
- `database.connections.active` - 活跃数据库连接数
- `redis.connections.active` - Redis连接状态
- `operations.pending` - 待处理操作数

## 🔧 运维工具

### 健康检查脚本
```bash
# 执行完整健康检查
./monitoring-scripts/health_check.sh

# 生成健康报告
./monitoring-scripts/health_check.sh -r

# 设置告警通知
./monitoring-scripts/health_check.sh -w http://webhook-url
```

### 指标收集脚本
```bash
# 收集业务指标
./monitoring-scripts/metrics_collector.sh

# 生成HTML报告
./monitoring-scripts/metrics_collector.sh -r

# 收集所有指标并清理过期文件
./monitoring-scripts/metrics_collector.sh -p -r -c
```

### 监控管理脚本
```bash
# 查看服务状态
./monitoring-scripts/setup_monitoring.sh --status

# 重启监控服务
./monitoring-scripts/setup_monitoring.sh --restart

# 停止监控服务
./monitoring-scripts/setup_monitoring.sh --stop
```

## 📈 监控仪表板

### 用户中心专用仪表板
- **用户注册趋势**: 实时注册速率和累计注册数
- **登录成功率**: 登录成功率和失败原因分析
- **缓存性能**: 缓存命中率和响应时间
- **API性能**: 请求量、响应时间、错误率
- **系统资源**: CPU、内存、磁盘使用情况

### 告警规则
- **应用不可用**: 应用停止响应超过1分钟
- **登录失败率过高**: 5分钟内失败率超过10%
- **缓存命中率低**: 命中率低于80%持续5分钟
- **数据库连接异常**: 无活跃数据库连接
- **API响应时间过长**: P95响应时间超过2秒

## 🛠️ 自定义配置

### 添加自定义指标
```java
@Service
public class CustomMetricsService {
    
    @Autowired
    private BusinessMetricsService businessMetricsService;
    
    public void recordCustomOperation() {
        // 记录自定义业务操作
        businessMetricsService.recordUserProfileUpdate("custom_operation");
    }
}
```

### 添加自定义告警规则
```yaml
# monitoring-configs/alert_rules.yml
- alert: CustomBusinessAlert
  expr: custom_metric > 100
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "自定义业务告警"
    description: "自定义指标超过阈值"
```

### 配置邮件告警
```yaml
# monitoring-configs/alertmanager.yml
global:
  smtp_smarthost: 'smtp.yourcompany.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: 'your-username'
  smtp_auth_password: 'your-password'
```

## 📚 文档资源

- **[使用指南](MONITORING_GUIDE.md)** - 详细的使用说明和API文档
- **[部署指南](DEPLOYMENT_GUIDE.md)** - 完整的部署和配置指南
- **[实施文档](MONITORING_OBSERVABILITY.md)** - 技术实施细节和架构说明

## 🔍 故障排查

### 常见问题
1. **服务无法启动**: 检查端口占用和Docker状态
2. **指标数据缺失**: 验证应用健康状态和Prometheus配置
3. **告警不工作**: 检查AlertManager配置和邮件设置
4. **链路追踪无数据**: 确认Zipkin服务状态和采样配置

### 日志查看
```bash
# 应用日志
tail -f logs/info.log

# Docker服务日志
docker-compose -f monitoring-configs/docker-compose-monitoring.yml logs

# 健康检查日志
tail -f /var/log/user-center-health.log
```

## 🤝 技术支持

如果您在使用过程中遇到问题：

1. **查看文档**: 首先参考相关文档和故障排查部分
2. **检查日志**: 查看应用和服务日志获取错误信息
3. **验证配置**: 确认配置文件的正确性
4. **测试连接**: 验证各组件之间的网络连接

## 📄 许可证

本项目采用 MIT 许可证，详情请参见 LICENSE 文件。

---

**🎉 恭喜！您已经成功为用户中心部署了企业级的监控和可观测性系统！**

通过这套系统，您可以：
- ✅ 实时监控业务指标和系统性能
- ✅ 快速发现和定位问题
- ✅ 建立完善的告警机制
- ✅ 进行数据驱动的决策
- ✅ 提升系统可靠性和用户体验
