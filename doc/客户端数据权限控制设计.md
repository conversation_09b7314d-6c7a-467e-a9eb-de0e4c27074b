# 客户端数据权限控制设计

## 概述

本设计实现了基于客户端身份的数据权限控制系统，支持**用户端模式**和**管理端模式**两种权限验证方式，确保每个客户端只能访问属于自己系统的用户数据。通过`X-Client-ID`请求头识别客户端身份，配合用户外部映射表实现数据隔离。

## 权限验证模式

### 1. 用户端模式 (USER Mode)
- **适用场景**: 普通用户操作自己的数据
- **UserId来源**: 从当前登录用户的token中获取
- **验证逻辑**: 验证当前客户端是否有权限访问该用户的数据
- **接口路径**: `/api/v1/me/*`
- **使用注解**: `@DataPermission(mode = DataPermission.Mode.USER)`

```java
@GetMapping
@DataPermission(mode = DataPermission.Mode.USER)
public Result<UserProfileVO> getCurrentUserProfile() {
    // 用户只能访问自己的数据，userId从token获取
    Long userId = AuthUtil.getCurrentUserId();
    return Result.success(userService.getProfile(userId));
}
```

### 2. 管理端模式 (ADMIN Mode)
- **适用场景**: 管理员操作任意用户的数据
- **UserId来源**: 从URL路径参数中获取目标用户ID
- **验证逻辑**: 仅验证客户端是否具有管理员权限
- **接口路径**: `/api/v1/admin/users/{userId}/*`
- **使用注解**: `@DataPermission(mode = DataPermission.Mode.ADMIN)`

```java
@GetMapping("/{userId}/profile")
@DataPermission(mode = DataPermission.Mode.ADMIN)
public Result<UserProfileVO> getUserProfile(@PathVariable Long userId) {
    // 管理员可以访问任意用户数据，仅验证管理员权限
    return Result.success(userService.getProfile(userId));
}
```

## 架构设计

### 1. 核心组件

#### 1.1 客户端识别过滤器 (ClientIdentificationFilter)
- 从请求头`X-Client-ID`获取客户端标识
- 验证客户端合法性
- 设置客户端上下文信息

#### 1.2 客户端上下文管理 (ClientContext)
- 线程本地存储客户端信息
- 提供客户端ID和详情的访问方法
- 自动清理防止内存泄漏

#### 1.3 数据权限验证切面 (DataPermissionAspect)
- 基于`@DataPermission`注解进行方法级权限控制
- 支持用户端和管理端两种验证模式
- 根据模式执行不同的权限验证逻辑

#### 1.4 用户外部映射服务 (UserExternalMappingService)
- 管理用户与外部系统的映射关系
- 提供权限验证和映射查询功能
- 支持映射关系的创建、更新、删除

### 2. 数据模型

#### 2.1 OAuth客户端表 (oauth_client_details)
```sql
CREATE TABLE `oauth_client_details` (
  `client_id` varchar(255) NOT NULL COMMENT '客户端唯一标识符',
  `client_name` varchar(255) NOT NULL COMMENT '客户端名称',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '客户端状态 (1: 正常, 0: 禁用)',
  `authorities` varchar(255) DEFAULT NULL COMMENT '客户端权限',
  `scope` varchar(255) DEFAULT NULL COMMENT '授权范围',
  -- ... 其他字段
  PRIMARY KEY (`client_id`)
);
```

#### 2.2 用户外部映射表 (user_external_mapping)
```sql
CREATE TABLE `user_external_mapping` (
  `mapping_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL COMMENT '用户中心用户ID',
  `external_system_client_id` varchar(255) NOT NULL COMMENT '外部系统客户端ID',
  `external_user_id` varchar(255) NOT NULL COMMENT '外部用户ID',
  `metadata` json DEFAULT NULL COMMENT '附加信息',
  PRIMARY KEY (`mapping_id`),
  UNIQUE KEY `uk_external_identity` (`external_system_client_id`, `external_user_id`)
);
```

## 使用指南

### 1. 客户端配置

#### 1.1 普通用户客户端
```sql
INSERT INTO oauth_client_details (client_id, client_name, status, authorities, scope) 
VALUES ('mobile-app', '移动端APP', 1, 'ROLE_USER', 'read,write');
```

#### 1.2 管理员客户端
```sql
INSERT INTO oauth_client_details (client_id, client_name, status, authorities, scope) 
VALUES ('admin-console', '管理后台', 1, 'ROLE_ADMIN', 'admin,read,write');
```

### 2. 用户映射关系

为用户创建与客户端的映射关系：

```java
@Autowired
private UserExternalMappingService mappingService;

// 创建用户与移动端的映射关系
mappingService.createMapping(userId, externalUserId, "mobile-app", metadata);
```

### 3. 接口权限控制

#### 3.1 用户端接口示例

```java
@RestController
@RequestMapping("/api/v1/me")
public class UserProfileController {
    
    @GetMapping("/profile")
    @DataPermission(mode = DataPermission.Mode.USER)
    public Result<UserProfileVO> getCurrentUserProfile() {
        // 用户只能查看自己的资料
        Long userId = AuthUtil.getCurrentUserId();
        return Result.success(userService.getProfile(userId));
    }
    
    @PutMapping("/profile")
    @DataPermission(mode = DataPermission.Mode.USER)
    public Result<UserProfileVO> updateCurrentUserProfile(@RequestBody UpdateProfileDTO dto) {
        // 用户只能修改自己的资料
        Long userId = AuthUtil.getCurrentUserId();
        return Result.success(userService.updateProfile(userId, dto));
    }
}
```

#### 3.2 管理端接口示例

```java
@RestController
@RequestMapping("/api/v1/admin/users")
public class AdminUserController {
    
    @GetMapping
    @DataPermission(mode = DataPermission.Mode.ADMIN)
    public Result<PageResponseVO<UserListVO>> getUserList(@ModelAttribute UserListRequestDTO requestDTO) {
        // 管理员可以查看用户列表
        PageResponseVO<UserListVO> pageResponse = adminUserService.getUserList(requestDTO);
        return Result.success(pageResponse);
    }
    
    @GetMapping("/{userId}")
    @DataPermission(mode = DataPermission.Mode.ADMIN)
    public Result<UserDetailVO> getUserDetail(@PathVariable Long userId) {
        // 管理员可以查看任意用户详情
        UserDetailVO userDetail = adminUserService.getUserDetail(userId);
        return Result.success(userDetail);
    }
    
    @PostMapping("/{userId}/disable")
    @DataPermission(mode = DataPermission.Mode.ADMIN)
    public Result<Void> disableUser(@PathVariable Long userId, @RequestBody DisableUserRequest request) {
        // 管理员可以禁用任意用户
        String reason = request != null ? request.getReason() : null;
        boolean success = adminUserService.disableUser(userId, reason);
        return Result.success();
    }
    
    @PostMapping("/{userId}/enable")
    @DataPermission(mode = DataPermission.Mode.ADMIN)
    public Result<Void> enableUser(@PathVariable Long userId) {
        // 管理员可以启用任意用户
        boolean success = adminUserService.enableUser(userId);
        return Result.success();
    }
}
```

### 4. 客户端请求

#### 4.1 普通用户请求
```http
GET /api/v1/me/profile HTTP/1.1
Host: http://localhost:8080
X-Client-ID: mobile-app
Authorization: Bearer ${user_token}
```

#### 4.2 管理员请求
```http
# 获取用户列表
GET /api/v1/admin/users HTTP/1.1
Host: http://localhost:8080
X-Client-ID: admin-console
Authorization: Bearer ${admin_token}

# 获取用户详情
GET /api/v1/admin/users/123 HTTP/1.1
Host: http://localhost:8080
X-Client-ID: admin-console
Authorization: Bearer ${admin_token}

# 禁用用户
POST /api/v1/admin/users/123/disable HTTP/1.1
Host: http://localhost:8080
X-Client-ID: admin-console
Authorization: Bearer ${admin_token}
Content-Type: application/json

{
  "reason": "违反用户协议"
}

# 启用用户
POST /api/v1/admin/users/123/enable HTTP/1.1
Host: http://localhost:8080
X-Client-ID: admin-console
Authorization: Bearer ${admin_token}
```

## 权限验证流程

### 1. 用户端模式流程
1. **请求到达**: 用户发送带有`X-Client-ID`和用户token的请求
2. **客户端识别**: `ClientIdentificationFilter`验证客户端身份
3. **用户认证**: 从token中获取当前用户ID
4. **权限验证**: 验证当前客户端是否有权限访问该用户数据
5. **业务处理**: 权限验证通过后执行业务逻辑

### 2. 管理端模式流程
1. **请求到达**: 管理员发送带有`X-Client-ID`和管理员token的请求
2. **客户端识别**: `ClientIdentificationFilter`验证客户端身份
3. **管理员验证**: 验证客户端是否具有管理员权限
4. **业务处理**: 权限验证通过后可操作任意用户数据

## 权限验证规则

### 1. 用户端客户端
- 只能访问与自己有映射关系的用户数据
- 通过`user_external_mapping`表验证权限
- 用户ID从当前登录用户token中获取

### 2. 管理端客户端
- 在`authorities`中包含`ROLE_ADMIN`或`scope`中包含`admin`
- 可以访问任意用户数据
- 不需要检查用户映射关系

### 3. 匿名客户端
- 没有提供或提供无效的`X-Client-ID`
- 无法访问任何需要权限的接口

## 安全考虑

### 1. 客户端认证
- 客户端ID必须在数据库中注册
- 支持客户端状态控制（启用/禁用）
- 可扩展支持客户端密钥验证

### 2. 数据隔离
- 用户端：严格的用户映射关系验证
- 管理端：基于客户端管理员权限验证
- 防止跨客户端未授权数据访问

### 3. 审计日志
- 记录所有数据访问操作
- 包含客户端身份和权限模式信息
- 支持安全审计和问题追踪

## 性能优化

### 1. 缓存策略
```java
@Cacheable(value = "clientDetails", key = "#clientId")
public OAuthClientDetails getClientDetails(String clientId) {
    // 缓存客户端详情
}

@Cacheable(value = "userMapping", key = "#userId + ':' + #clientId")
public boolean hasDataAccess(Long userId, String clientId) {
    // 缓存用户映射关系
}
```

### 2. 数据库优化
- 在映射表上创建合适的索引
- 使用读写分离减少主库压力
- 考虑分库分表策略

## 监控和运维

### 1. 监控指标
- 客户端请求统计（按模式分类）
- 权限验证成功/失败率
- 用户端vs管理端接口调用比例

### 2. 日志记录
- 客户端识别日志
- 权限验证日志（标注模式）
- 异常访问告警

### 3. 运维工具
- 客户端管理界面
- 用户映射关系管理
- 权限验证统计报表

## 故障排除

### 1. 常见问题
- **用户端**: 客户端ID不存在、用户映射关系缺失
- **管理端**: 客户端缺少管理员权限、用户认证失败
- **通用**: 权限配置错误、token过期

### 2. 调试方法
- 检查请求头中的`X-Client-ID`
- 验证数据库中的映射关系（用户端）
- 验证客户端管理员权限（管理端）
- 查看审计日志中的权限验证记录

### 3. 错误代码
- `403 FORBIDDEN`: 数据访问权限不足或需要管理员权限
- `401 UNAUTHORIZED`: 客户端身份验证失败或用户未登录
- `400 BAD_REQUEST`: 缺少必要的客户端标识 