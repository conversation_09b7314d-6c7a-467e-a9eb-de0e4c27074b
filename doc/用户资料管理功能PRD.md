# 产品需求文档：用户资料管理

## 1. 概述
本功能模块旨在为用户中心提供全面的用户资料管理能力，包括个人基本信息（user_profile）和教育经历（user_education_history）的创建、读取、更新和删除（CRUD）操作。系统需确保数据安全、易于扩展，并提供友好的API接口供客户端或其他服务调用。

## 2. 技术栈
*   **后端**：Java 17, Spring Boot, Spring Security, MyBatis-Plus (或 JPA)
*   **数据库**：PostgreSQL (或 MySQL)
*   **缓存**：Redis (可选，用于热点数据缓存)
*   **测试**：JUnit, Mockito, Postman
*   **部署**：Docker, Kubernetes (或 AWS ECS)

## 3. 架构设计
*   遵循 RESTful API 设计原则，采用 MVC (Model-View-Controller) 模式。
*   服务内部通过 Service 层封装业务逻辑，Mapper/Repository 层处理数据持久化。
*   考虑采用 DTO (Data Transfer Object) 模式进行数据传输，隔离领域模型和API接口。
*   认证与授权：复用现有用户中心的认证机制（如 JWT）。

## 4. 数据库模式

### 4.1. `user_profile` 表
存储用户的基本个人信息。

| 字段名         | 类型          | 约束/说明                                  |
| -------------- | ------------- | ------------------------------------------ |
| `id`           | `BIGINT`      | 主键, ASSIGN_ID (对应实体字段 `profileId`)   |
| `user_id`      | `BIGINT`      | 外键, 关联 `user_base.id`, 唯一            |
| `nickname`     | `VARCHAR(100)`| 昵称                                       |
| `avatar_url`   | `VARCHAR(512)`| 头像URL                                    |
| `gender`       | `TINYINT`     | 性别 (0: 未知, 1: 男, 2: 女)               |
| `birth_date`   | `DATE`        | 出生日期 (对应实体字段 `birthday`)           |
| `nationality`  | `VARCHAR(100)`| 国籍        |
| `ethnicity`    | `VARCHAR(100)`| 民族        |
| `special_status` | `TINYINT` | 特殊身份 (1-军人, 2-警察, 3-医生, 4-教师, 5-残疾人, 6-其他)         |
| `political_status` | `TINYINT` | 政治面貌 (1-中共党员, 2-中共预备党员, 3-共青团员, 4-群众)         |
| `marital_status`   | `TINYINT` | 婚姻状况 (1-未婚, 2-已婚, 3-离异, 4-丧偶)         |
| `fertility_status`    | `TINYINT` | 生育情况 (1-未育, 2-已育, 3-已育一孩, 4-已育两孩及以上)         |
| `health_status`    | `TINYINT` | 健康状况 (1-健康, 2-良好, 3-一般, 4-较差)         |
| `region_code`      | `VARCHAR(10)` | 地区编码 (对应实体字段 `regionCode`)         |
| `region_name`  | `VARCHAR(100)`| 地区名称 (对应实体字段 `regionName`)         |
| `address`      | `VARCHAR(255)`| 详细地址 (对应实体字段 `address`)         |
| `bio`          | `TEXT`        | 个人简介                                   |
| `created_at`   | `TIMESTAMP`   | 创建时间                                   |
| `updated_at`   | `TIMESTAMP`   | 更新时间                                   |
| `deleted_at`   | `TIMESTAMP`   | 逻辑删除时间 (对应实体字段 `deletedAt`)    |

*索引：* `idx_user_id` on `user_id`

### 4.2. `user_education_history` 表
存储用户的教育经历，一个用户可以有多条教育记录。

| 字段名           | 类型          | 约束/说明                                       |
| ---------------- | ------------- | ----------------------------------------------- |
| `id`             | `BIGINT`      | 主键, 自增 (对应实体字段 `eduId`)                 |
| `user_id`        | `BIGINT`      | 外键, 关联 `user_base.id`                       |
| `school_name`    | `VARCHAR(255)`| 学校名称                                        |
| `degree`         | `VARCHAR(100)`| 学位 (如: 学士, 硕士, 博士, 高中等)             |
| `degree_level`   | `TINYINT`     | 学位等级 (如: 1-本科, 2-硕士, 3-博士, 4-高中等)       |
| `major`          | `VARCHAR(100)`| 专业或研究领域                                  |
| `secondary_major`| `VARCHAR(100)`| 第二专业或研究领域 (可选)                       |
| `major_area`     | `VARCHAR(100)`| 专业方向 (可选)                                   |
| `major_gpa`      | `FLOAT`       | 专业GPA (可选)                                    |
| `start_date`     | `DATE`        | 入学日期 (年-月-日)                             |
| `end_date`       | `DATE`        | 毕业/离校日期 (年-月-日, NULL表示在读)          |
| `description`    | `TEXT`        | 描述/在校经历/荣誉等                            |
| `club_experience`| `TEXT`        | 社团经历                                        |
| `visibility`     | `VARCHAR(50)` | 可见性 (公开, 好友可见, 私密) - 实体类中定义    |
| `created_at`     | `TIMESTAMP`   | 创建时间                                        |
| `updated_at`     | `TIMESTAMP`   | 更新时间                                        |
| `deleted_at`     | `TIMESTAMP`   | 逻辑删除时间 (对应实体字段 `deletedAt`)         |

*索引：* `idx_user_id` on `user_id`

### 4.3. `user_project_history` 表
存储用户的项目经历，一个用户可以有多条项目记录。

| 字段名                 | 类型          | 约束/说明                                                     |
| ---------------------- | ------------- | ------------------------------------------------------------- |
| `id`                   | `BIGINT`      | 主键, 自增 (对应实体字段 `projectId`)                           |
| `user_id`              | `BIGINT`      | 外键, 关联 `user_base.id`                                     |
| `project_name`         | `VARCHAR(255)`| 项目名称                                                      |
| `role`                 | `VARCHAR(100)`| 用户在项目中的角色/职责 (如: 开发者, 项目经理, 设计师)        |
| `start_date`           | `DATE`        | 项目开始日期 (年-月-日)                                         |
| `end_date`             | `DATE`        | 项目结束日期 (年-月-日, NULL表示进行中)                         |
| `description`          | `TEXT`        | 项目描述 (目标, 用户贡献, 使用技术, 成果等)                   |
| `project_url`          | `VARCHAR(512)`| 项目链接 (如: 代码仓库, 演示地址, 官网)                       |
| `associated_organization` | `VARCHAR(255)`| 关联组织/公司 (可选, 用于说明项目归属) - 实体类中定义         |
| `visibility`           | `VARCHAR(50)` | 可见性 (公开, 好友可见, 私密) - 实体类中定义                |
| `created_at`           | `TIMESTAMP`   | 创建时间                                                      |
| `updated_at`           | `TIMESTAMP`   | 更新时间                                                      |
| `deleted_at`           | `TIMESTAMP`   | 逻辑删除时间 (对应实体字段 `deletedAt`)                     |

*索引：* `idx_user_id` on `user_id`

### 4.4. `user_work_history` 表
存储用户的工作经历，一个用户可以有多条工作记录。

| 字段名         | 类型          | 约束/说明                                       |
| -------------- | ------------- | ----------------------------------------------- |
| `id`           | `BIGINT`      | 主键, 自增 (对应实体字段 `workId`)                |
| `user_id`      | `BIGINT`      | 外键, 关联 `user_base.id`                       |
| `company_name` | `VARCHAR(255)`| 公司/组织名称                                   |
| `company_logo` | `VARCHAR(512)`| 公司/组织Logo URL (可选)                         |
| `company_url`  | `VARCHAR(512)`| 公司/组织官网 URL (可选)                           |
| `company_size` | `INT`         | 公司/组织规模 (如: 100-500人)                         |
| `company_industry` | `VARCHAR(100)`| 公司/组织行业 (如: 互联网, 金融, 教育, 医疗等) |
| `company_location` | `VARCHAR(100)`| 公司/组织地点 (如: 北京, 上海, 广州, 深圳等) |
| `position`     | `VARCHAR(100)`| 职位/头衔                                       |
| `department`   | `VARCHAR(100)`| 所属部门 - 实体类中定义                         |
| `start_date`   | `DATE`        | 入职日期 (年-月-日)                             |
| `end_date`     | `DATE`        | 离职日期 (年-月-日, NULL表示在职)               |
| `description`  | `TEXT`        | 工作职责描述                               |
| `achievements` | `TEXT`        | 工作业绩/成果                               |
| `reporting_to` | `VARCHAR(100)`| 汇报对象 (如: 张三)                           |
| `reason_for_leaving` | `TEXT`        | 离职原因 (如: 个人发展, 公司调整, 家庭原因等)                           |
| `salary_min`       | `DECIMAL(10,2)`         | 薪资最小值 (如: 10000)                           |
| `salary_max`       | `DECIMAL(10,2)`         | 薪资最大值 (如: 20000)                           |
| `city`         | `VARCHAR(100)`| 工作地点 (如: 城市) (对应实体字段 `location`)   |
| `certification_type` | `TINYINT` | 认证方式 (如: 1-工牌、2-劳动合同、3-社保、4-个税) - 实体类中定义    |
| `certification_status` | `TINYINT` | 认证状态 (如: 0-未认证、1-已认证) - 实体类中定义    |
| `visibility`   | `VARCHAR(50)` | 可见性 (公开, 好友可见, 私密) - 实体类中定义    |
| `created_at`   | `TIMESTAMP`   | 创建时间                                        |
| `updated_at`   | `TIMESTAMP`   | 更新时间                                        |
| `deleted_at`   | `TIMESTAMP`   | 逻辑删除时间 (对应实体字段 `deletedAt`)         |

*索引：* `idx_user_id` on `user_id`

### 4.5. `user_contact_methods` 表
存储用户的联系方式。

| 字段名         | 类型          | 约束/说明                                                                 |
| -------------- | ------------- | ------------------------------------------------------------------------- |
| `id`           | `BIGINT`      | 主键, 自增 (对应实体字段 `contactId`)                                       |
| `user_id`      | `BIGINT`      | 外键, 关联 `user_base.id`                                                 |
| `contact_type` | `TINYINT` | 联系方式类型 (如 1-备用邮箱, 2-工作电话, 3-紧急联系人, 4-微信, 5-QQ、6-即刻、7-小红书、8-微博、9-抖音、10-其他) - 实体类中定义 |
| `contact_value`| `VARCHAR(255)`| 联系方式的值 (邮箱地址, 电话号码, URL, ID等)                              |
| `label`        | `TINYINT`| 用户自定义标签 (如: 1-备用邮箱, 2-工作电话, 3-紧急联系人, 4-社交账号) - 实体类中定义                    |
| `visibility`   | `VARCHAR(50)` | 可见性 - 实体类中定义                                                     |
| `is_verified`  | `BOOLEAN`     | 是否已验证 (0: 未验证, 1: 已验证) - 实体类中定义                          |
| `verified_at`  | `TIMESTAMP`   | 验证时间 - 实体类中定义                                                   |
| `created_at`   | `TIMESTAMP`   | 创建时间                                                                  |
| `updated_at`   | `TIMESTAMP`   | 更新时间                                                                  |
| `deleted_at`   | `TIMESTAMP`   | 逻辑删除时间 (对应实体字段 `deletedAt`)                                   |

*索引：* `idx_user_id_type` on (`user_id`, `contact_type`)

### 4.6.  `user_training_history` 表
存储用户的培训经历，一个用户可以有多条培训记录。

| 字段名         | 类型          | 约束/说明                                                                 |
| -------------- | ------------- | ------------------------------------------------------------------------- |
| `id`           | `BIGINT`      | 主键, 自增 (对应实体字段 `trainingId`)                                       |
| `user_id`      | `BIGINT`      | 外键, 关联 `user_base.id`                                                 |
| `training_name`| `VARCHAR(255)`| 培训名称                                                                  |
| `training_type`| `VARCHAR(50)` | 培训类型 (如: 线上培训, 线下培训) - 实体类中定义                          |
| `training_provider`| `VARCHAR(255)`| 培训提供者 (如: 公司, 机构) - 实体类中定义                                  |
| `start_date`   | `DATE`        | 培训开始日期 (年-月-日)                                                 |
| `end_date`     | `DATE`        | 培训结束日期 (年-月-日, NULL表示进行中)                                 |
| `description`  | `TEXT`        | 培训描述 (如: 培训内容, 培训成果)                                       |
| `visibility`   | `VARCHAR(50)` | 可见性 - 实体类中定义                                                     |
| `created_at`   | `TIMESTAMP`   | 创建时间                                                                  |
| `updated_at`   | `TIMESTAMP`   | 更新时间                                                                  |
| `deleted_at`   | `TIMESTAMP`   | 逻辑删除时间 (对应实体字段 `deletedAt`)                                   |

*索引：* `idx_user_id` on `user_id`

### 4.7  `user_part_time_history` 表
存储用户的兼职经历，一个用户可以有多条兼职记录。

| 字段名         | 类型          | 约束/说明                                                                 |
| -------------- | ------------- | ------------------------------------------------------------------------- |
| `id`           | `BIGINT`      | 主键, 自增 (对应实体字段 `partTimeId`)                                       |
| `user_id`      | `BIGINT`      | 外键, 关联 `user_base.id`                                                 |
| `part_time_name`| `VARCHAR(255)`| 兼职名称                                                                  |
| `part_time_type`| `TINYINT` | 兼职类型 (如: 1-咨询, 2-设计, 3-开发, 4-运营, 5-销售, 6-客服, 7-服务员, 8-其他) - 实体类中定义                          | 
| `part_time_provider`| `VARCHAR(255)`| 兼职提供者 (如: 公司, 机构) - 实体类中定义                                  |
| `part_time_location`| `VARCHAR(100)`| 兼职地点 (如: 城市) - 实体类中定义                                                 |
| `part_time_salary`| `DECIMAL(10,2)`| 兼职薪资 (如: 10000)                                                  |
| `service_period`| `VARCHAR(50)` | 服务周期 (如: 1个月, 3个月, 6个月, 1年) - 实体类中定义                          |
| `start_date`   | `DATE`        | 兼职开始日期 (年-月-日)                                                 |
| `end_date`     | `DATE`        | 兼职结束日期 (年-月-日, NULL表示进行中)                                 |
| `description`  | `TEXT`        | 兼职描述 (如: 兼职内容, 兼职成果)                                       |
| `visibility`   | `VARCHAR(50)` | 可见性 - 实体类中定义                                                     |
| `created_at`   | `TIMESTAMP`   | 创建时间                                                                  |
| `updated_at`   | `TIMESTAMP`   | 更新时间                                                                  |
| `deleted_at`   | `TIMESTAMP`   | 逻辑删除时间 (对应实体字段 `deletedAt`)                                   |

*索引：* `idx_user_id` on `user_id`


## 5. API 端点设计

基路径: `/api/v1/users/{userId}`

### 5.1. 用户 Profile API

*   **`GET /api/v1/users/{userId}/profile`**
    *   描述：获取指定用户的个人资料。
    *   权限：用户本人 或 管理员。
    *   响应 (成功 - 200 OK): `{ "real_name": "...", "nickname": "...", ... }`
    *   响应 (失败 - 404 Not Found): 如果用户 Profile 不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权访问。

*   **`POST /api/v1/users/{userId}/profile`**
    *   描述：创建指定用户的个人资料 (幂等操作)。
    *   权限：用户本人。
    *   请求体: `{ "real_name": "...", "nickname": "...", ... }`
    *   响应 (成功 - 201 Created): 新创建的 Profile `{ "real_name": "...", ... }`
    *   响应 (失败 - 400 Bad Request): 如果请求体验证失败。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

*   **`PUT /api/v1/users/{userId}/profile`**
    *   描述：更新指定用户的个人资料 (幂等操作)。
    *   权限：用户本人。
    *   请求体: `{ "real_name": "...", "nickname": "...", ... }` (允许部分更新)
    *   响应 (成功 - 200 OK): 更新后的 Profile `{ "real_name": "...", ... }`
    *   响应 (成功 - 201 Created): 如果是首次创建 Profile。
    *   响应 (失败 - 400 Bad Request): 如果请求体验证失败。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

*   **`DELETE /api/v1/users/{userId}/profile`**
    *   描述：删除指定用户的个人资料。
    *   权限：用户本人。
    *   响应 (成功 - 204 No Content): 删除成功。
    *   响应 (失败 - 404 Not Found): 如果用户 Profile 不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

### 5.2. 用户教育经历 API

*   **`GET /api/v1/users/{userId}/educations`**
    *   描述：获取指定用户的所有教育经历列表。
    *   权限：用户本人 或 管理员。
    *   响应 (成功 - 200 OK): `[ { "id": 1, "school_name": "...", ... }, { ... } ]`
    *   响应 (失败 - 403 Forbidden): 如果无权访问。

*   **`POST /api/v1/users/{userId}/educations`**
    *   描述：为指定用户添加一条新的教育经历。
    *   权限：用户本人。
    *   请求体: `{ "school_name": "...", "degree": "...", ... }`
    *   响应 (成功 - 201 Created): 新创建的教育经历 `{ "id": ..., "school_name": "...", ... }`
    *   响应 (失败 - 400 Bad Request): 如果请求体验证失败。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

*   **`GET /api/v1/users/{userId}/educations/{educationId}`**
    *   描述：获取指定用户的某条特定教育经历。
    *   权限：用户本人 或 管理员。
    *   响应 (成功 - 200 OK): `{ "id": ..., "school_name": "...", ... }`
    *   响应 (失败 - 404 Not Found): 如果教育经历不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权访问。

*   **`PUT /api/v1/users/{userId}/educations/{educationId}`**
    *   描述：更新指定用户的某条特定教育经历。
    *   权限：用户本人。
    *   请求体: `{ "school_name": "...", "degree": "...", ... }` (允许部分更新)
    *   响应 (成功 - 200 OK): 更新后的教育经历 `{ "id": ..., "school_name": "...", ... }`
    *   响应 (失败 - 400 Bad Request): 如果请求体验证失败。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

*   **`DELETE /api/v1/users/{userId}/educations/{educationId}`**
    *   描述：删除指定用户的某条特定教育经历。
    *   权限：用户本人。
    *   响应 (成功 - 204 No Content): 删除成功。
    *   响应 (失败 - 404 Not Found): 如果教育经历不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

### 5.3. 用户项目经历 API

*   **`GET /api/v1/users/{userId}/projects`**
    *   描述：获取指定用户的所有项目经历列表。
    *   权限：用户本人 或 管理员。
    *   响应 (成功 - 200 OK): `[ { "id": 1, "project_name": "...", ... }, { ... } ]`
    *   响应 (失败 - 403 Forbidden): 如果无权访问。

*   **`POST /api/v1/users/{userId}/projects`**
    *   描述：为指定用户添加一条新的项目经历。
    *   权限：用户本人。
    *   请求体: `{ "project_name": "...", "role": "...", ... }`
    *   响应 (成功 - 201 Created): 新创建的项目经历 `{ "id": ..., "project_name": "...", ... }`
    *   响应 (失败 - 400 Bad Request): 如果请求体验证失败。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

*   **`GET /api/v1/users/{userId}/projects/{projectId}`**
    *   描述：获取指定用户的某条特定项目经历。
    *   权限：用户本人 或 管理员。
    *   响应 (成功 - 200 OK): `{ "id": ..., "project_name": "...", ... }`
    *   响应 (失败 - 404 Not Found): 如果项目经历不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权访问。

*   **`PUT /api/v1/users/{userId}/projects/{projectId}`**
    *   描述：更新指定用户的某条特定项目经历。
    *   权限：用户本人。
    *   请求体: `{ "project_name": "...", "role": "...", ... }` (允许部分更新)
    *   响应 (成功 - 200 OK): 更新后的项目经历 `{ "id": ..., "project_name": "...", ... }`
    *   响应 (失败 - 400 Bad Request): 如果请求体验证失败。
    *   响应 (失败 - 404 Not Found): 如果项目经历不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

*   **`DELETE /api/v1/users/{userId}/projects/{projectId}`**
    *   描述：删除指定用户的某条特定项目经历。
    *   权限：用户本人。
    *   响应 (成功 - 204 No Content): 删除成功。
    *   响应 (失败 - 404 Not Found): 如果项目经历不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

### 5.4. 用户工作经历 API

*   **`GET /api/v1/users/{userId}/works`**
    *   描述：获取指定用户的所有工作经历列表。
    *   权限：用户本人 或 管理员。
    *   响应 (成功 - 200 OK): `[ { "id": 1, "company_name": "...", ... }, { ... } ]`
    *   响应 (失败 - 403 Forbidden): 如果无权访问。

*   **`POST /api/v1/users/{userId}/works`**
    *   描述：为指定用户添加一条新的工作经历。
    *   权限：用户本人。
    *   请求体: `{ "company_name": "...", "position": "...", ... }`
    *   响应 (成功 - 201 Created): 新创建的工作经历 `{ "id": ..., "company_name": "...", ... }`
    *   响应 (失败 - 400 Bad Request): 如果请求体验证失败。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

*   **`GET /api/v1/users/{userId}/works/{workId}`**
    *   描述：获取指定用户的某条特定工作经历。
    *   权限：用户本人 或 管理员。
    *   响应 (成功 - 200 OK): `{ "id": ..., "company_name": "...", ... }`
    *   响应 (失败 - 404 Not Found): 如果工作经历不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权访问。

*   **`PUT /api/v1/users/{userId}/works/{workId}`**
    *   描述：更新指定用户的某条特定工作经历。
    *   权限：用户本人。
    *   请求体: `{ "company_name": "...", "position": "...", ... }` (允许部分更新)
    *   响应 (成功 - 200 OK): 更新后的工作经历 `{ "id": ..., "company_name": "...", ... }`
    *   响应 (失败 - 400 Bad Request): 如果请求体验证失败。
    *   响应 (失败 - 404 Not Found): 如果工作经历不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

*   **`DELETE /api/v1/users/{userId}/works/{workId}`**
    *   描述：删除指定用户的某条特定工作经历。
    *   权限：用户本人。
    *   响应 (成功 - 204 No Content): 删除成功。
    *   响应 (失败 - 404 Not Found): 如果工作经历不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

### 5.5. 用户联系方式 API

*   **`GET /api/v1/users/{userId}/contacts`**
    *   描述：获取指定用户的所有联系方式列表。
    *   权限：用户本人 或 管理员 (管理员可能只能看到公开的联系方式)。
    *   响应 (成功 - 200 OK): `[ { "id": 1, "contact_type": "...", "contact_value": "...", "is_public": true/false }, { ... } ]`
    *   响应 (失败 - 403 Forbidden): 如果无权访问。

*   **`POST /api/v1/users/{userId}/contacts`**
    *   描述：为指定用户添加一条新的联系方式。
    *   权限：用户本人。
    *   请求体: `{ "contact_type": "...", "contact_value": "...", "is_public": true/false, "is_primary": true/false }`
    *   响应 (成功 - 201 Created): 新创建的联系方式 `{ "id": ..., "contact_type": "...", ... }`
    *   响应 (失败 - 400 Bad Request): 如果请求体验证失败。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

*   **`GET /api/v1/users/{userId}/contacts/{contactId}`**
    *   描述：获取指定用户的某条特定联系方式。
    *   权限：用户本人 或 管理员 (需检查 is_public)。
    *   响应 (成功 - 200 OK): `{ "id": ..., "contact_type": "...", ... }`
    *   响应 (失败 - 404 Not Found): 如果联系方式不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权访问。

*   **`PUT /api/v1/users/{userId}/contacts/{contactId}`**
    *   描述：更新指定用户的某条特定联系方式。
    *   权限：用户本人。
    *   请求体: `{ "contact_value": "...", "is_public": true/false, "is_primary": true/false }` (允许部分更新)
    *   响应 (成功 - 200 OK): 更新后的联系方式 `{ "id": ..., "contact_type": "...", ... }`
    *   响应 (失败 - 400 Bad Request): 如果请求体验证失败。
    *   响应 (失败 - 404 Not Found): 如果联系方式不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

*   **`DELETE /api/v1/users/{userId}/contacts/{contactId}`**
    *   描述：删除指定用户的某条特定联系方式。
    *   权限：用户本人。
    *   响应 (成功 - 204 No Content): 删除成功。
    *   响应 (失败 - 404 Not Found): 如果联系方式不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

### 5.6 用户培训经历 API

*   **`GET /api/v1/users/{userId}/trainings`**
    *   描述：获取指定用户的所有培训经历列表。
    *   权限：用户本人 或 管理员。
    *   响应 (成功 - 200 OK): `[ { "id": 1, "training_name": "...", ... }, { ... } ]`
    *   响应 (失败 - 403 Forbidden): 如果无权访问。

*   **`POST /api/v1/users/{userId}/trainings`**
    *   描述：为指定用户添加一条新的培训经历。
    *   权限：用户本人。
    *   请求体: `{ "training_name": "...", "training_type": "...", ... }`
    *   响应 (成功 - 201 Created): 新创建的培训经历 `{ "id": ..., "training_name": "...", ... }`
    *   响应 (失败 - 400 Bad Request): 如果请求体验证失败。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

*   **`GET /api/v1/users/{userId}/trainings/{trainingId}`**
    *   描述：获取指定用户的某条特定培训经历。
    *   权限：用户本人 或 管理员。
    *   响应 (成功 - 200 OK): `{ "id": ..., "training_name": "...", ... }`
    *   响应 (失败 - 404 Not Found): 如果培训经历不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权访问。

*   **`PUT /api/v1/users/{userId}/trainings/{trainingId}`**
    *   描述：更新指定用户的某条特定培训经历。
    *   权限：用户本人。
    *   请求体: `{ "training_name": "...", "training_type": "...", ... }` (允许部分更新)
    *   响应 (成功 - 200 OK): 更新后的培训经历 `{ "id": ..., "training_name": "...", ... }`
    *   响应 (失败 - 400 Bad Request): 如果请求体验证失败。
    *   响应 (失败 - 404 Not Found): 如果培训经历不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

*   **`DELETE /api/v1/users/{userId}/trainings/{trainingId}`**
    *   描述：删除指定用户的某条特定培训经历。
    *   权限：用户本人。
    *   响应 (成功 - 204 No Content): 删除成功。
    *   响应 (失败 - 404 Not Found): 如果培训经历不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

### 5.7 用户兼职经历 API

*   **`GET /api/v1/users/{userId}/part_times`**
    *   描述：获取指定用户的所有兼职经历列表。
    *   权限：用户本人 或 管理员。
    *   响应 (成功 - 200 OK): `[ { "id": 1, "part_time_name": "...", ... }, { ... } ]`
    *   响应 (失败 - 403 Forbidden): 如果无权访问。

*   **`POST /api/v1/users/{userId}/part_times`**
    *   描述：为指定用户添加一条新的兼职经历。
    *   权限：用户本人。
    *   请求体: `{ "part_time_name": "...", "part_time_type": "...", ... }`
    *   响应 (成功 - 201 Created): 新创建的兼职经历 `{ "id": ..., "part_time_name": "...", ... }`
    *   响应 (失败 - 400 Bad Request): 如果请求体验证失败。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

*   **`GET /api/v1/users/{userId}/part_times/{partTimeId}`**
    *   描述：获取指定用户的某条特定兼职经历。
    *   权限：用户本人 或 管理员。
    *   响应 (成功 - 200 OK): `{ "id": ..., "part_time_name": "...", ... }`
    *   响应 (失败 - 404 Not Found): 如果兼职经历不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权访问。

*   **`PUT /api/v1/users/{userId}/part_times/{partTimeId}`**
    *   描述：更新指定用户的某条特定兼职经历。
    *   权限：用户本人。
    *   请求体: `{ "part_time_name": "...", "part_time_type": "...", ... }` (允许部分更新)
    *   响应 (成功 - 200 OK): 更新后的兼职经历 `{ "id": ..., "part_time_name": "...", ... }`
    *   响应 (失败 - 400 Bad Request): 如果请求体验证失败。
    *   响应 (失败 - 404 Not Found): 如果兼职经历不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

*   **`DELETE /api/v1/users/{userId}/part_times/{partTimeId}`**
    *   描述：删除指定用户的某条特定兼职经历。
    *   权限：用户本人。
    *   响应 (成功 - 204 No Content): 删除成功。
    *   响应 (失败 - 404 Not Found): 如果兼职经历不存在。
    *   响应 (失败 - 403 Forbidden): 如果无权操作。

## 6. 安全要求
*   **认证与授权**：所有 API 端点必须经过认证，并根据用户角色（本人、管理员）进行严格授权检查。
*   **输入验证**：对所有来自客户端的输入进行严格验证，防止 XSS、SQL 注入等攻击。使用 Bean Validation (JSR 380)。
*   **敏感数据处理**：
    *   考虑对 `real_name` 等潜在敏感信息在存储或传输时进行脱敏或加密处理（根据具体安全需求决定）。
    *   本期 **暂不强制** 对 Profile 和 Education 数据进行加密存储，但需预留可扩展性。
*   **HTTPS**：强制使用 HTTPS 协议。
*   **CORS**：根据需要配置 CORS 策略。

## 7. 编码规范
*   遵循《阿里巴巴 Java 开发手册》。
*   Service, Mapper, Entity, DTO 等类名使用单数形式 (e.g., `UserProfileService`)。
*   方法名使用驼峰命名法 (e.g., `getUserProfile`)。
*   为公共方法、类添加清晰的 Javadoc 注释。
*   保持代码简洁，单个方法和类的长度不宜过长。

## 8. 测试策略
*   **单元测试**：使用 JUnit 和 Mockito 对 Service 层和关键工具类进行单元测试，目标覆盖率 > 80%。
*   **集成测试**：
    *   使用 Spring Boot Test 测试 Service 层与 Mapper/Repository 层的集成。
    *   使用 MockMvc 或 RestAssured 测试 API 端点的功能和权限控制。
*   **手动测试**：使用 Postman 或类似工具对 API 进行手动验证和探索性测试。

## 9. 非功能性需求
*   **性能**：核心查询接口（如 GET Profile）响应时间应在 200ms 以内。
*   **可扩展性**：设计应易于未来添加更多用户资料字段或类型。
*   **可维护性**：代码结构清晰，注释完善，易于理解和修改。

## 10. 部署与上线
*   提供 Flyway 或 Liquibase 数据库迁移脚本用于创建 `user_profile` 和 `user_education_history` 表。
*   更新 Dockerfile 和部署配置。
*   制定上线计划和回滚预案。

## 11. 未来考虑
*   用户资料字段的动态配置。
*   更复杂的权限控制模型。
*   集成第三方服务（如 LinkedIn）导入教育/工作经历。
*   资料变更的审计日志。