# 用户中心ER图

```mermaid
erDiagram
    UserBase {
        Long userId PK "用户唯一主键ID"
        Boolean realNameVerified "实名认证状态(0:未认证,1:已认证)"
        String realName "真实姓名(认证后填充,脱敏存储或加密)"
        String idCardNumber "身份证号(认证后填充,必须加密存储)"
        Integer status "用户状态(1:正常,2:禁用,0:注销/逻辑删除,3:已合并)"
        LocalDateTime createdAt "记录创建时间"
        LocalDateTime updatedAt "记录最后更新时间"
        LocalDateTime deletedAt "记录删除时间(逻辑删除)"
    }
    %% UserBase: 用户基础信息实体类

    UserProfile {
        Long profileId PK "资料记录主键ID"
        Long userId FK "关联的用户基础ID (逻辑外键, 指向 UserBase.userId)"
        String nickname "用户昵称"
        String avatarUrl "头像URL"
        Integer gender "性别 (0: 未知, 1: 男, 2: 女)"
        LocalDate birthday "生日"
        String nationality "国籍"
        String ethnicity "民族"
        SpecialStatus specialStatus "特殊身份 (1-军人, 2-警察, 3-医生, 4-教师, 5-残疾人, 6-其他)"
        PoliticalStatus politicalStatus "政治面貌 (1-中共党员, 2-中共预备党员, 3-共青团员, 4-群众)"
        MaritalStatus maritalStatus "婚姻状况 (1-未婚, 2-已婚, 3-离异, 4-丧偶)"
        FertilityStatus fertilityStatus "生育情况 (1-未育, 2-已育, 3-已育一孩, 4-已育两孩及以上)"
        HealthStatus healthStatus "健康状况 (1-健康, 2-良好, 3-一般, 4-较差)"
        String regionCode "地区编码"
        String regionName "地区名称"
        String address "详细地址"
        String bio "个人简介"
        LocalDateTime createdAt "记录创建时间"
        LocalDateTime updatedAt "记录最后更新时间"
        LocalDateTime deletedAt "记录删除时间(逻辑删除)"
    }
    %% UserProfile: 用户个人资料实体类

    UserAuth {
        Long authId PK "认证记录主键ID"
        Long userId FK "关联的用户基础ID (逻辑外键, 指向 UserBase.userId)"
        String identityType "认证/登录类型 (WECHAT_MP, ALIPAY_MP, DOUYIN_MP, PHONE, EMAIL, USERNAME)"
        String identifier "凭证标识 (OpenID, UserID, 手机号, 用户名等)"
        String credential "凭证内容 (密码哈希, Token等, 可为空)"
        Integer verified "是否已验证 (1: 已验证, 0: 未验证)"
        LocalDateTime lastLoginAt "最后登录时间"
        LocalDateTime createdAt "记录创建时间"
        LocalDateTime updatedAt "记录最后更新时间"
        LocalDateTime deletedAt "记录删除时间(逻辑删除)"
    }
    %% UserAuth: 用户认证/登录方式实体类

    UserRealNameAuth {
        Long authRecordId PK "认证记录主键"
        Long userId FK "关联的用户基础ID (逻辑外键, 指向 UserBase.userId, 关联最终认证成功的user_id)"
        String submittedRealName "提交时使用的真实姓名 (应用层考虑加密)"
        String submittedIdCardNumber "提交时使用的身份证号 (应用层必须加密存储)"
        Integer authStatus "认证状态 (0:待审核 PENDING, 1:已认证 VERIFIED, 2:已驳回 REJECTED)"
        LocalDateTime authTime "认证完成时间"
        String authChannel "认证渠道/方式"
        String failReason "失败原因 (如果认证失败)"
        LocalDateTime createdAt "记录创建时间"
        LocalDateTime updatedAt "记录最后更新时间"
    }
    %% UserRealNameAuth: 用户实名认证记录实体类

    UserContactMethods {
        Long contactId PK "联系方式主键ID"
        Long userId FK "用户ID (逻辑外键, 关联 UserBase.userId)"
        Integer contactType "联系方式类型 (如 1-备用邮箱, 2-工作电话, 3-紧急联系人, 4-微信, 5-QQ、6-即刻、7-小红书、8-微博、9-抖音、10-其他)"
        String contactValue "联系方式的值 (邮箱地址, 电话号码, URL, ID等)"
        String label "用户自定义标签 (如: 备用邮箱, 工作电话)"
        String visibility "可见性"
        Boolean isVerified "是否已验证 (0: 未验证, 1: 已验证)"
        LocalDateTime verifiedAt "验证时间"
        LocalDateTime createdAt "记录创建时间"
        LocalDateTime updatedAt "记录更新时间"
        LocalDateTime deletedAt "逻辑删除时间"
    }
    %% UserContactMethods: 用户附加联系方式实体类

    UserEducationHistory {
        Long eduId PK "教育经历主键ID"
        Long userId FK "关联的用户ID (指向 UserBase.userId)"
        String schoolName "学校名称"
        String degree "学位"
        Integer degreeLevel "学位等级"
        String major "专业"
        String secondaryMajor "第二专业"
        String majorArea "专业方向"
        Float majorGpa "专业GPA"
        LocalDate startDate "入学日期"
        LocalDate endDate "毕业/离校日期(NULL表示在读)"
        String description "描述/在校经历/荣誉等"
        String clubExperience "社团经历"
        String visibility "可见性(公开,好友可见,私密)"
        LocalDateTime createdAt "记录创建时间"
        LocalDateTime updatedAt "记录最后更新时间"
        LocalDateTime deletedAt "记录删除时间(逻辑删除)"
    }
    %% UserEducationHistory: 用户教育经历实体类

    UserWorkHistory {
        Long workId PK "工作经历主键ID"
        Long userId FK "关联的用户ID (指向 UserBase.userId)"
        String companyName "公司/组织名称"
        String companyLogo "公司/组织Logo URL"
        String companyUrl "公司/组织官网 URL"
        Integer companySize "公司/组织规模"
        String companyIndustry "公司/组织行业"
        String companyLocation "公司/组织地点"
        String position "职位/头衔"
        String department "所属部门"
        LocalDate startDate "入职日期"
        LocalDate endDate "离职日期(NULL表示在职)"
        String description "工作职责描述"
        String achievements "工作业绩/成果"
        String reportingTo "汇报对象"
        String reasonForLeaving "离职原因"
        BigDecimal salaryMin "薪资最小值"
        BigDecimal salaryMax "薪资最大值"
        Integer certificationType "认证方式"
        Integer certificationStatus "认证状态"
        String visibility "可见性(公开,好友可见,私密)"
        LocalDateTime createdAt "记录创建时间"
        LocalDateTime updatedAt "记录最后更新时间"
        LocalDateTime deletedAt "记录删除时间(逻辑删除)"
    }
    %% UserWorkHistory: 用户工作经历实体类

    UserProjectHistory {
        Long projectId PK "项目经历主键ID"
        Long userId FK "关联的用户ID (指向 UserBase.userId)"
        String projectName "项目名称"
        String role "用户在项目中的角色/职责"
        LocalDate startDate "项目开始日期"
        LocalDate endDate "项目结束日期(NULL表示进行中)"
        String description "项目描述"
        String projectUrl "项目链接"
        String associatedOrganization "关联组织/公司"
        String visibility "可见性(公开,好友可见,私密)"
        LocalDateTime createdAt "记录创建时间"
        LocalDateTime updatedAt "记录最后更新时间"
        LocalDateTime deletedAt "记录删除时间(逻辑删除)"
    }
    %% UserProjectHistory: 用户项目经历实体类

    UserPartTime {
        Long partTimeId PK "主键ID"
        Long userId FK "用户ID (指向 UserBase.userId)"
        String partTimeName "兼职名称"
        Integer partTimeType "兼职类型 (1-咨询, 2-设计, 3-开发, 4-运营, 5-销售, 6-客服, 7-服务员, 8-其他)"
        String partTimeProvider "兼职提供者/机构"
        String partTimeLocation "兼职地点"
        BigDecimal partTimeSalary "兼职薪资"
        String servicePeriod "服务周期（如：1个月, 3个月, 6个月, 1年）"
        LocalDate startDate "开始日期"
        LocalDate endDate "结束日期（NULL表示进行中）"
        String description "兼职描述"
        String visibility "可见性"
        LocalDateTime createdAt "创建时间"
        LocalDateTime updatedAt "更新时间"
        LocalDateTime deletedAt "删除时间（逻辑删除）"
    }
    %% UserPartTime: 用户兼职经历实体类

    UserTraining {
        Long trainingId PK "主键ID"
        Long userId FK "用户ID (指向 UserBase.userId)"
        String trainingName "培训名称"
        String trainingType "培训类型（如：线上培训、线下培训）"
        String trainingProvider "培训提供者/机构"
        LocalDate startDate "开始日期"
        LocalDate endDate "结束日期（NULL表示进行中）"
        String description "培训描述"
        String visibility "可见性"
        LocalDateTime createdAt "创建时间"
        LocalDateTime updatedAt "更新时间"
        LocalDateTime deletedAt "删除时间（逻辑删除）"
    }
    %% UserTraining: 用户培训经历实体类

    UserAuditLog {
        Long logId PK "日志记录主键ID"
        Long userId FK "关联的用户基础ID (逻辑外键, 指向 UserBase.userId)"
        String operationType "操作类型 (LOGIN_SUCCESS, LOGIN_FAIL, REGISTER_SUCCESS, ACCOUNT_MERGE_START, ACCOUNT_MERGE_COMPLETE等)"
        String operationDetail "操作详情 (JSON格式, 包含操作的详细信息)"
        String ipAddress "操作IP地址"
        String userAgent "用户代理信息"
        LocalDateTime createdAt "记录创建时间"
    }
    %% UserAuditLog: 用户操作审计日志实体类

    OAuthClientDetails {
        String clientId PK "客户端唯一标识符"
        String clientSecret "客户端密钥 (应用层必须加密存储)"
        String clientName "客户端名称 (如: 官方网站, iOS应用)"
        String description "客户端描述"
        String scope "客户端授权范围 (逗号分隔, 如: read, write)"
        String authorizedGrantTypes "允许的授权类型 (逗号分隔, 如: authorization_code, client_credentials, password, implicit, refresh_token)"
        String webServerRedirectUri "授权码模式或隐式模式的回调URI (逗号分隔)"
        String authorities "客户端拥有的权限 (逗号分隔, Spring Security概念)"
        Integer accessTokenValidity "访问令牌有效期秒数"
        Integer refreshTokenValidity "刷新令牌有效期秒数"
        String additionalInformation "附加信息 (可存储JSON)"
        String autoapprove "自动授权范围 (逗号分隔, 或 \"true\" 表示全部)"
        Integer status "客户端状态 (1: 正常, 0: 禁用)"
        LocalDateTime createdAt "记录创建时间"
        LocalDateTime updatedAt "记录最后更新时间"
        LocalDateTime deletedAt "逻辑删除时间 (NULL表示未删除)"
    }
    %% OAuthClientDetails: OAuth2客户端详情实体类

    UserExternalMapping {
        Long mappingId PK "映射记录主键ID"
        Long userId FK "系统A (用户中心) 的用户ID (逻辑外键, 指向 UserBase.userId)"
        String externalSystemClientId FK "外部系统客户端ID (逻辑外键关联 OAuthClientDetails.clientId)"
        String externalUserId "用户在外部系统中的唯一标识符"
        String metadata "附加信息 (例如: 映射状态, 关联时间等)"
        LocalDateTime createdAt "记录创建时间"
        LocalDateTime updatedAt "记录最后更新时间"
    }
    %% UserExternalMapping: 用户与外部系统身份映射关系实体类

    UserBase ||--o{ UserProfile : "拥有个人资料"
    UserBase ||--o{ UserAuth : "拥有认证方式"
    UserBase ||--o{ UserRealNameAuth : "进行实名认证"
    UserBase ||--o{ UserContactMethods : "拥有联系方式"
    UserBase ||--o{ UserEducationHistory : "拥有教育经历"
    UserBase ||--o{ UserWorkHistory : "拥有工作经历"
    UserBase ||--o{ UserProjectHistory : "拥有项目经历"
    UserBase ||--o{ UserPartTime : "拥有兼职经历"
    UserBase ||--o{ UserTraining : "拥有培训经历"
    UserBase ||--o{ UserAuditLog : "产生审计日志"
    UserBase ||--o{ UserExternalMapping : "映射至外部系统"
    OAuthClientDetails ||--o{ UserExternalMapping : "作为外部系统"
```
