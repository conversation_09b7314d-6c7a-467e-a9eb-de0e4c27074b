# 客户端模式设计

# 背景

用户中心提供给各业务系统调用，要采用客户端模式

# 功能设计

## 识别客户端 

*   HTTP Header: 客户端在每个请求头中添加一个自定义 Header，如 X-Client-ID: web-frontend。后端通过 Filter 或 Interceptor 读取并验证这个 Header
    
*   审计记录记录 client\_id
    
*   映射外部系统用户ID
    

# 数据库设计

## 客户端信息表

```mysql
-- ----------------------------
-- OAuth2 客户端详情表
-- ----------------------------
CREATE TABLE `oauth_client_details` (
  `client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端唯一标识符',
  `client_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户端密钥 (应用层必须加密存储)',
  `client_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端名称 (如: 官方网站, iOS应用)',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '客户端描述',
  `scope` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户端授权范围 (逗号分隔, 如: read, write)',
  `authorized_grant_types` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '允许的授权类型 (逗号分隔, 如: authorization_code, client_credentials, password, implicit, refresh_token)',
  `web_server_redirect_uri` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '授权码模式或隐式模式的回调URI (逗号分隔)',
  `authorities` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户端拥有的权限 (逗号分隔, Spring Security概念)',
  `access_token_validity` int DEFAULT NULL COMMENT '访问令牌有效期秒数',
  `refresh_token_validity` int DEFAULT NULL COMMENT '刷新令牌有效期秒数',
  `additional_information` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '附加信息 (可存储JSON)',
  `autoapprove` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '自动授权范围 (逗号分隔, 或 "true" 表示全部)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '客户端状态 (1: 正常, 0: 禁用)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '逻辑删除时间 (NULL表示未删除)',
  PRIMARY KEY (`client_id`),
  KEY `idx_deleted_at` (`deleted_at`) COMMENT '逻辑删除索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OAuth2 客户端详情表，用于管理接入的客户端应用';
```

## 用户审计表

*   添加client\_id
    

```mysql
-- ----------------------------
-- 用户操作审计日志表 (更新)
-- ----------------------------
CREATE TABLE `user_audit_log` (
  `log_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志主键ID',
  `user_id` bigint unsigned DEFAULT NULL COMMENT '主要关联的用户ID (操作对象)',
  `actor_user_id` bigint unsigned DEFAULT NULL COMMENT '执行操作的用户ID (可能是用户自己, 可能是管理员)',
  `client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发起请求的客户端ID (逻辑外键关联 oauth_client_details)', -- 新增字段
  `action_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型 (LOGIN_SUCCESS, REAL_NAME_AUTH_SUBMIT, ACCOUNT_MERGE_AUTH_UPDATE, etc.)',
  `target_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作目标类型 (USER_AUTH, USER_PROFILE_FIELD, etc.)',
  `target_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作目标ID或标识符',
  `status` tinyint DEFAULT NULL COMMENT '操作结果状态 (1: 成功, 0: 失败)',
  `old_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '旧值 (例如, 合并前的user_id)',
  `new_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '新值 (例如, 合并后的user_id)',
  `details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '详细信息 (例如 IP, UserAgent, 失败原因, 合并来源user_id, JSON格式)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '日志记录时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_actor_user_id` (`actor_user_id`),
  KEY `idx_client_id` (`client_id`), -- 新增索引
  KEY `idx_action_type` (`action_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户操作审计日志表';
```

## 用户外部系统映射表

```mysql
-- ----------------------------
-- 用户外部系统映射表
-- ----------------------------
CREATE TABLE `user_external_mapping` (
  `mapping_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '映射记录主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '系统A (用户中心) 的用户ID (逻辑外键)',
  `external_system_client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '外部系统客户端ID (逻辑外键关联 oauth_client_details)',
  `external_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户在外部系统中的唯一标识符',
  `metadata` json DEFAULT NULL COMMENT '附加信息 (例如: 映射状态, 关联时间等)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  PRIMARY KEY (`mapping_id`),
  UNIQUE KEY `uk_external_identity` (`external_system_client_id`, `external_user_id`) COMMENT '同一外部系统下的外部用户ID唯一',
  KEY `idx_user_id` (`user_id`),
  KEY `idx_external_system_client_id` (`external_system_client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户与外部系统身份映射关系表';
```