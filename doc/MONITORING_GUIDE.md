# 用户中心监控和可观测性使用指南

## 目录
1. [快速开始](#快速开始)
2. [监控功能概览](#监控功能概览)
3. [健康检查使用](#健康检查使用)
4. [业务指标监控](#业务指标监控)
5. [链路追踪配置](#链路追踪配置)
6. [API接口说明](#api接口说明)
7. [集成第三方工具](#集成第三方工具)
8. [故障排查](#故障排查)
9. [最佳实践](#最佳实践)

## 快速开始

### 1. 启动应用
```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run
```

### 2. 验证监控功能
```bash
# 检查应用健康状态
curl http://localhost:18080/actuator/health

# 查看所有可用指标
curl http://localhost:18080/actuator/metrics

# 获取业务指标概览
curl http://localhost:8080/api/monitoring/metrics/overview
```

### 3. 访问监控端点
- **主应用端口**: 8080
- **管理端口**: 18080
- **Swagger文档**: http://localhost:8080/doc.html

## 监控功能概览

### 核心组件
- **BusinessMetricsService**: 业务指标收集服务
- **CustomHealthIndicator**: 自定义健康检查
- **MetricsAspect**: AOP指标收集切面
- **TracingConfiguration**: 链路追踪配置
- **MonitoringController**: 监控API控制器

### 监控层次
```
应用层监控
├── 业务指标 (用户操作、系统性能)
├── 健康检查 (数据库、Redis、业务逻辑)
├── 链路追踪 (请求流程、性能分析)
└── 基础设施 (JVM、HTTP、数据库连接)
```

## 健康检查使用

### 1. 基础健康检查
```bash
# 获取整体健康状态
curl http://localhost:18080/actuator/health

# 获取详细健康信息
curl http://localhost:8080/api/monitoring/health
```

### 2. 健康状态说明
- **UP**: 所有组件正常运行
- **DOWN**: 关键组件异常，服务不可用
- **DEGRADED**: 部分组件异常，服务降级运行
- **UNKNOWN**: 无法确定组件状态

### 3. 检查组件
- **database**: MySQL数据库连接和查询
- **redis**: Redis连接和读写操作
- **business**: 业务逻辑和配置检查

### 4. 健康检查响应示例
```json
{
  "status": "UP",
  "components": {
    "database": {
      "status": "UP",
      "details": {
        "database": "MySQL",
        "version": "8.0.32",
        "connection_count": 5,
        "uptime": "3600 seconds"
      }
    },
    "redis": {
      "status": "UP",
      "details": {
        "ping": "PONG",
        "read_write_test": "PASSED"
      }
    }
  }
}
```

## 业务指标监控

### 1. 指标分类

#### 用户相关指标
- `user.registration.total` - 用户注册总数
- `user.login.total` - 登录成功总数
- `user.login.failure.total` - 登录失败总数
- `user.profile.update.total` - 资料更新总数
- `user.sessions.active` - 活跃会话数
- `user.total.count` - 用户总数

#### 业务操作指标
- `user.project.operation.total` - 项目操作总数
- `user.work.history.operation.total` - 工作经历操作总数
- `user.education.operation.total` - 教育经历操作总数
- `operations.pending` - 待处理操作数

#### 系统性能指标
- `cache.hit.total` - 缓存命中总数
- `cache.miss.total` - 缓存未命中总数
- `cache.hit.rate` - 缓存命中率
- `database.query.duration` - 数据库查询时间
- `redis.operation.duration` - Redis操作时间
- `database.connections.active` - 活跃数据库连接数

### 2. 查看指标数据
```bash
# 获取指标概览
curl http://localhost:8080/api/monitoring/metrics/overview

# 获取特定指标详情
curl http://localhost:8080/api/monitoring/metrics/user.login.total

# 获取所有指标列表
curl http://localhost:8080/api/monitoring/metrics

# Prometheus格式指标
curl http://localhost:18080/actuator/prometheus
```

### 3. 指标响应示例
```json
{
  "code": 200,
  "data": {
    "user_metrics": {
      "total_registrations": 1250,
      "total_logins": 8934,
      "login_failures": 45,
      "profile_updates": 567
    },
    "performance_metrics": {
      "cache_hits": 15678,
      "cache_misses": 234,
      "cache_hit_rate": 98.5,
      "active_sessions": 23,
      "pending_operations": 2
    }
  }
}
```

## 链路追踪配置

### 1. 启动Zipkin服务器
```bash
# 使用Docker启动Zipkin
docker run -d -p 9411:9411 openzipkin/zipkin

# 访问Zipkin UI
open http://localhost:9411
```

### 2. 配置说明
```yaml
# application.yml
management:
  tracing:
    sampling:
      probability: 0.1  # 10%采样率，生产环境建议0.01-0.1
  zipkin:
    tracing:
      endpoint: http://localhost:9411/api/v2/spans
```

### 3. 追踪范围
- HTTP请求处理
- 数据库操作
- Redis操作
- 业务方法执行
- 缓存操作

### 4. 自定义追踪
```java
// 在业务方法上添加@Observed注解
@Observed(name = "user.profile.update", contextualName = "update-user-profile")
public void updateUserProfile(Long userId, UserProfileDTO profile) {
    // 业务逻辑
}
```

## API接口说明

### 1. 监控API端点

#### 系统健康状态
```http
GET /api/monitoring/health
```
返回系统各组件的详细健康状态。

#### 业务指标概览
```http
GET /api/monitoring/metrics/overview
```
返回关键业务指标的汇总信息。

#### 特定指标详情
```http
GET /api/monitoring/metrics/{metricName}
```
返回指定指标的详细信息，包括值、标签、描述等。

#### 所有指标列表
```http
GET /api/monitoring/metrics
```
返回系统中所有可用指标的列表，按类型分组。

#### 手动触发指标收集
```http
POST /api/monitoring/metrics/collect
```
手动触发一次指标收集操作。

### 2. Actuator端点（端口18080）

#### 健康检查
```http
GET /actuator/health
```

#### 指标数据
```http
GET /actuator/metrics
GET /actuator/metrics/{metricName}
```

#### Prometheus格式指标
```http
GET /actuator/prometheus
```

#### 应用信息
```http
GET /actuator/info
```

#### 环境信息
```http
GET /actuator/env
```

## 集成第三方工具

### 1. Prometheus集成

#### Prometheus配置文件
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'user-center'
    static_configs:
      - targets: ['localhost:18080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 10s
```

#### 启动Prometheus
```bash
# 下载并启动Prometheus
wget https://github.com/prometheus/prometheus/releases/download/v2.40.0/prometheus-2.40.0.linux-amd64.tar.gz
tar xvfz prometheus-*.tar.gz
cd prometheus-*
./prometheus --config.file=prometheus.yml
```

### 2. Grafana集成

#### 启动Grafana
```bash
# 使用Docker启动Grafana
docker run -d -p 3000:3000 grafana/grafana

# 默认登录: admin/admin
open http://localhost:3000
```

#### 添加数据源
1. 登录Grafana
2. 添加Prometheus数据源: http://localhost:9090
3. 导入仪表板模板

#### 推荐仪表板指标
- 用户注册趋势: `rate(user_registration_total[5m])`
- 登录成功率: `rate(user_login_total[5m]) / (rate(user_login_total[5m]) + rate(user_login_failure_total[5m]))`
- 缓存命中率: `cache_hit_rate`
- API响应时间: `http_server_requests_seconds`
- 数据库查询时间: `database_query_duration_seconds`

### 3. 告警配置

#### Prometheus告警规则
```yaml
# alert_rules.yml
groups:
  - name: user-center-alerts
    rules:
      - alert: HighLoginFailureRate
        expr: rate(user_login_failure_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High login failure rate detected"
          
      - alert: LowCacheHitRate
        expr: cache_hit_rate < 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Cache hit rate is below 80%"
          
      - alert: DatabaseConnectionIssue
        expr: database_connections_active == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "No active database connections"
```

## 故障排查

### 1. 常见问题

#### 监控端点无法访问
```bash
# 检查管理端口是否启动
netstat -tlnp | grep 18080

# 检查应用日志
tail -f logs/info.log | grep management
```

#### 指标数据为空
```bash
# 检查指标注册
curl http://localhost:18080/actuator/metrics | grep user

# 检查切面是否生效
curl http://localhost:8080/api/monitoring/metrics/overview
```

#### 链路追踪不工作
```bash
# 检查Zipkin连接
curl http://localhost:9411/health

# 检查追踪配置
curl http://localhost:18080/actuator/env | grep tracing
```

### 2. 日志分析
```bash
# 查看监控相关日志
grep -i "metrics\|health\|tracing" logs/info.log

# 查看错误日志
grep -i "error\|exception" logs/error.log
```

### 3. 性能调优
- 调整采样率: 生产环境建议1%-10%
- 优化指标标签: 避免高基数标签
- 配置指标过滤: 过滤不必要的指标

## 最佳实践

### 1. 监控策略
- **分层监控**: 基础设施 → 应用 → 业务
- **关键指标**: 专注于业务关键指标
- **告警设置**: 设置合理的告警阈值
- **定期回顾**: 定期回顾和优化监控配置

### 2. 性能考虑
- **采样率**: 根据流量调整追踪采样率
- **指标基数**: 控制标签的基数，避免内存问题
- **批量处理**: 使用批量方式收集指标
- **异步处理**: 指标收集不应影响业务性能

### 3. 安全考虑
- **端点保护**: 保护管理端点的访问
- **敏感信息**: 避免在指标中暴露敏感信息
- **网络隔离**: 监控流量与业务流量隔离

### 4. 运维建议
- **文档维护**: 保持监控文档的更新
- **团队培训**: 确保团队了解监控工具
- **应急预案**: 制定监控系统的应急预案
- **容量规划**: 根据监控数据进行容量规划

## 总结

本监控系统提供了全面的可观测性能力：
- ✅ 实时健康检查
- ✅ 详细业务指标
- ✅ 分布式链路追踪
- ✅ 丰富的API接口
- ✅ 第三方工具集成

通过合理使用这些功能，您可以：
- 及时发现和解决问题
- 优化系统性能
- 了解用户行为模式
- 做出数据驱动的决策

如有问题，请参考故障排查部分或查看应用日志。

## 附录

### A. 完整的Docker Compose配置

创建 `docker-compose-monitoring.yml` 文件：

```yaml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - ./alert_rules.yml:/etc/prometheus/alert_rules.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana-storage:/var/lib/grafana

  zipkin:
    image: openzipkin/zipkin:latest
    container_name: zipkin
    ports:
      - "9411:9411"

  alertmanager:
    image: prom/alertmanager:latest
    container_name: alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager.yml:/etc/alertmanager/alertmanager.yml

volumes:
  grafana-storage:
```

启动监控栈：
```bash
docker-compose -f docker-compose-monitoring.yml up -d
```

### B. Grafana仪表板JSON配置

创建用户中心专用仪表板：

```json
{
  "dashboard": {
    "title": "用户中心监控仪表板",
    "panels": [
      {
        "title": "用户注册趋势",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(user_registration_total[5m])",
            "legendFormat": "注册速率"
          }
        ]
      },
      {
        "title": "登录成功率",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(user_login_total[5m]) / (rate(user_login_total[5m]) + rate(user_login_failure_total[5m])) * 100",
            "legendFormat": "成功率 %"
          }
        ]
      },
      {
        "title": "缓存性能",
        "type": "graph",
        "targets": [
          {
            "expr": "cache_hit_rate",
            "legendFormat": "命中率 %"
          }
        ]
      }
    ]
  }
}
```

### C. 监控脚本示例

#### 健康检查脚本
```bash
#!/bin/bash
# health_check.sh

HEALTH_URL="http://localhost:18080/actuator/health"
WEBHOOK_URL="your-webhook-url"

response=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $response -ne 200 ]; then
    echo "Health check failed with status: $response"
    # 发送告警
    curl -X POST $WEBHOOK_URL \
         -H 'Content-Type: application/json' \
         -d '{"text":"用户中心健康检查失败，状态码: '$response'"}'
    exit 1
else
    echo "Health check passed"
    exit 0
fi
```

#### 指标收集脚本
```bash
#!/bin/bash
# collect_metrics.sh

METRICS_URL="http://localhost:8080/api/monitoring/metrics/overview"
OUTPUT_FILE="/tmp/user_center_metrics_$(date +%Y%m%d_%H%M%S).json"

curl -s $METRICS_URL > $OUTPUT_FILE

if [ $? -eq 0 ]; then
    echo "Metrics collected successfully: $OUTPUT_FILE"

    # 解析关键指标
    total_users=$(jq '.data.infrastructure_metrics.total_users' $OUTPUT_FILE)
    cache_hit_rate=$(jq '.data.performance_metrics.cache_hit_rate' $OUTPUT_FILE)

    echo "Total Users: $total_users"
    echo "Cache Hit Rate: $cache_hit_rate%"
else
    echo "Failed to collect metrics"
    exit 1
fi
```

### D. 告警配置模板

#### AlertManager配置
```yaml
# alertmanager.yml
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
  - name: 'web.hook'
    email_configs:
      - to: '<EMAIL>'
        subject: '用户中心告警: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          详情: {{ .Annotations.description }}
          时间: {{ .StartsAt }}
          {{ end }}
    webhook_configs:
      - url: 'http://your-webhook-url'
        send_resolved: true
```

### E. 性能基准测试

#### JMeter测试计划
```xml
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2">
  <hashTree>
    <TestPlan testname="用户中心性能测试">
      <elementProp name="TestPlan.arguments" elementType="Arguments" guiclass="ArgumentsPanel">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
      <stringProp name="TestPlan.user_define_classpath"></stringProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup testname="用户登录测试">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">100</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">10</stringProp>
        <stringProp name="ThreadGroup.ramp_time">60</stringProp>
      </ThreadGroup>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
```

### F. 监控检查清单

#### 日常检查项目
- [ ] 系统健康状态正常
- [ ] 关键业务指标在正常范围
- [ ] 缓存命中率 > 90%
- [ ] 数据库连接正常
- [ ] Redis连接正常
- [ ] 无异常告警
- [ ] 链路追踪数据正常
- [ ] 日志无ERROR级别错误

#### 周期性检查项目
- [ ] 监控数据存储空间充足
- [ ] 告警规则有效性验证
- [ ] 仪表板数据准确性
- [ ] 性能趋势分析
- [ ] 容量规划评估

### G. 常用查询语句

#### Prometheus查询
```promql
# 用户注册速率（每分钟）
rate(user_registration_total[1m]) * 60

# 登录成功率
rate(user_login_total[5m]) / (rate(user_login_total[5m]) + rate(user_login_failure_total[5m])) * 100

# API响应时间P95
histogram_quantile(0.95, rate(http_server_requests_seconds_bucket[5m]))

# 数据库查询平均时间
rate(database_query_duration_seconds_sum[5m]) / rate(database_query_duration_seconds_count[5m])

# 缓存命中率趋势
avg_over_time(cache_hit_rate[1h])
```

#### 日志查询
```bash
# 查看最近的错误日志
tail -n 100 logs/error.log

# 统计登录失败次数
grep "login.*failed" logs/info.log | wc -l

# 查看特定用户的操作日志
grep "userId=12345" logs/user-audit.log

# 分析API响应时间
grep "duration" logs/info.log | awk '{print $NF}' | sort -n
```

---

## 联系支持

如果您在使用过程中遇到问题：

1. **查看日志**: 首先检查应用日志文件
2. **检查配置**: 验证配置文件的正确性
3. **测试连接**: 确认各组件的网络连接
4. **参考文档**: 查阅本指南的故障排查部分

**技术支持**: 请提供详细的错误信息和环境配置信息。
