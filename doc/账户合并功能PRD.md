# 产品需求文档：用户中心 - 账户合并功能

## 1. 概述

本功能旨在实现当用户完成实名认证后，系统能够自动检测并关联（合并）同一自然人通过不同认证方式（如微信、支付宝等）注册的多个账户。目标是将这些分散的账户统一到一个主账户下，提供统一的用户视图和管理体验，同时确保数据的一致性和安全性。

## 2. 技术栈

- **后端：** Java 17 (或项目指定版本), Spring Boot, Spring Security, Mybatis Plus
- **数据库：** MySQL (或项目指定数据库)
- **缓存：** Redis (用于支持相关流程，如会话管理)
- **配置中心：** Nacos
- **日志：** Logback
- **测试：** JUnit, Postman
- **部署：** Docker, (待定云平台)

## 3. 架构

- **API风格：** RESTful API，遵循 MVC 模式。
- **认证：** 基于 JWT (或项目选型) 的用户会话管理。
- **设计原则：** 模块化设计，服务层负责核心业务逻辑，遵循单一职责原则。

## 4. 数据库模式

账户合并功能主要涉及以下数据表：

- **`user_base`**: 存储用户基础信息。
  - `id`: 用户主键。
  - `status`: 用户状态 (关键值: 1-正常, 3-已合并)。
  - `real_name_verified`: 实名认证状态 (0-未认证, 1-已认证)。
  - `real_name`: 真实姓名 (加密存储)。
  - `id_card_no`: 身份证号 (加密存储)。
  - `deleted_at`: 逻辑删除时间戳 (用于标记合并后的源账户)。
- **`user_auth`**: 存储用户认证方式信息。
  - `id`: 认证记录主键。
  - `user_id`: 关联 `user_base.id`。
  - `identity_type`: 认证类型 (如 WECHAT_MP, ALIPAY_MP)。
  - `identifier`: 认证标识 (如 OpenID, UserID)。
  - `deleted_at`: 逻辑删除时间戳。
- **`user_profile`**: 存储用户档案信息。
  - `user_id`: 关联 `user_base.id`。
  - (其他档案字段，如昵称、头像等)
- **`user_real_name_auth`**: 存储用户实名认证记录。
  - `id`: 认证记录主键。
  - `user_id`: 提交认证的用户 `user_base.id`。
  - `real_name`: 真实姓名。
  - `id_card_no`: 身份证号。
  - `status`: 认证状态 (如 PENDING, SUCCESS, FAILED)。
- **`user_audit_log`**: 记录用户操作审计日志。
  - `user_id`: 操作关联的用户 `user_base.id`。
  - `action_type`: 操作类型 (关键值: `REAL_NAME_AUTH_SUCCESS`, `ACCOUNT_MERGE_START`, `ACCOUNT_MERGE_AUTH_TRANSFER`, `ACCOUNT_MERGE_PROFILE_UPDATE`, `ACCOUNT_MERGE_STATUS_CHANGE`)。
  - `old_value`: 操作前的值 (如合并前的 `user_id`)。
  - `new_value`: 操作后的值 (如合并后的 `user_id`)。
  - `details`: 操作详情 (JSON格式，包含合并来源 `user_id` 等)。

## 5. API 端点 (示例)

账户合并通常由实名认证成功后触发，可能没有直接的合并API，而是包含在实名认证流程中。

- **`POST /api/users/real-name-auth/submit`** (或类似实名认证提交接口)
  - **请求体 (DTO):** `RealNameAuthSubmitDTO { String realName, String idCardNo, ... }`
  - **响应体 (VO):** `RealNameAuthResultVO { boolean success, String message, UserInfoVO userInfo }` (userInfo 可能包含合并后的主账户信息)
  - **内部逻辑:** 接收实名信息，调用第三方认证接口，认证成功后执行账户合并检查与处理逻辑。

## 6. 核心逻辑 (账户合并流程)

1.  **触发:** 用户 A (当前操作账户) 提交实名认证信息并通过验证 (`user_real_name_auth` 记录状态为 SUCCESS)。
2.  **查找:** 系统使用用户 A 认证通过的真实姓名和身份证号 (加密后) 查询 `user_base` 表，查找是否存在其他 `status = 1` (正常) 且 `real_name_verified = 1` 的用户 B。
    - **查询条件:** `SELECT id FROM user_base WHERE real_name = ? AND id_card_no = ? AND status = 1 AND real_name_verified = 1 AND id != ? AND deleted_at IS NULL` (查询除用户A之外的其他同实名用户)。
3.  **判断与执行:**
    *   **情况一: 未找到用户 B (或找到但非正常状态)**
        *   更新用户 A 的 `user_base` 记录：设置 `real_name_verified = 1`, `real_name`, `id_card_no`。
        *   记录 `user_audit_log` (操作类型: `REAL_NAME_AUTH_SUCCESS`)。
    *   **情况二: 找到一个或多个符合条件的用户 B (选择一个作为目标账户，例如创建时间最早的)**
        *   **开启数据库事务。**
        *   记录 `user_audit_log` (操作类型: `ACCOUNT_MERGE_START`, `user_id`: 用户A ID, `details`: { targetUserId: 用户B ID })。
        *   **转移认证方式:** 将用户 A 的所有有效 `user_auth` 记录 (`deleted_at IS NULL`) 的 `user_id` 更新为用户 B 的 ID。
            *   `UPDATE user_auth SET user_id = ? WHERE user_id = ? AND deleted_at IS NULL`
            *   记录 `user_audit_log` (操作类型: `ACCOUNT_MERGE_AUTH_TRANSFER`, `user_id`: 用户A ID, `details`: { sourceAuthIds: [...], targetUserId: 用户B ID })。
        *   **处理 Profile (策略: 保留目标账户B的信息)**: 此处不合并 Profile，仅记录日志。
            *   记录 `user_audit_log` (操作类型: `ACCOUNT_MERGE_PROFILE_UPDATE`, `user_id`: 用户A ID, `details`: { strategy: 'retain_target', sourceProfile: {...}, targetUserId: 用户B ID })。
        *   **标记源账户:** 更新用户 A 的 `user_base` 记录：设置 `status = 3` (已合并), `deleted_at = NOW()`。
            *   `UPDATE user_base SET status = 3, deleted_at = NOW() WHERE id = ?`
            *   记录 `user_audit_log` (操作类型: `ACCOUNT_MERGE_STATUS_CHANGE`, `user_id`: 用户A ID, `old_value`: 1, `new_value`: 3, `details`: { mergedIntoUserId: 用户B ID })。
        *   **更新目标账户实名状态 (如果需要):** 确保用户 B 的 `real_name_verified = 1`。
        *   **提交事务。**

## 7. 安全要求

- **数据加密:** 敏感信息（真实姓名、身份证号）在存储和传输时必须加密。
- **输入验证:** 对 API 请求参数进行严格验证，防止注入攻击。
- **事务管理:** 账户合并操作必须在数据库事务中执行，保证原子性。
- **权限控制:** 确保只有用户本人才能发起实名认证。
- **日志记录:** 详细记录合并过程的关键步骤和数据变更。
- **HTTPS:** 所有 API 通信强制使用 HTTPS。

## 8. 编码规范

- 遵循《阿里巴巴 Java 开发手册》(或项目指定规范)。
- Service/DAO 接口与实现分离，实现类使用 `Impl` 后缀。
- 方法、变量命名采用驼峰式 (`lowerCamelCase`)。
- 类名采用 `UpperCamelCase`。
- 常量全大写，下划线分隔。
- 添加必要的 Javadoc 注释。

## 9. 测试策略

- **单元测试 (JUnit):** 覆盖 Service 层核心合并逻辑，模拟各种场景（无合并、单账户合并、多账户合并冲突处理等），覆盖率 > 80%。
- **集成测试:** 测试实名认证 API 调用后，数据库状态是否按预期变更（包括 `user_base`, `user_auth`, `user_audit_log`）。
- **手动测试 (Postman):** 模拟用户操作流程，验证不同认证方式注册的账户在实名认证后是否能成功合并，以及合并后的登录情况。