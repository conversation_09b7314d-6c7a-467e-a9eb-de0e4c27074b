# 用户中心项目实现计划

以下是用户中心项目的详细实现计划，已拆分为可执行的任务：

## 阶段一：项目初始化与基础框架搭建

### Task 1.1: 创建项目基础结构
- 使用 Spring Initializr 创建基础项目
- 配置 Maven 依赖（Spring Boot, Mybatis Plus, Spring Security 等）
- 配置项目结构（controller, service, mapper, entity, dto, vo 等）
- 配置 logback 日志

### Task 1.2: 配置基础环境
- 配置 Nacos 服务发现与配置中心
- 配置 Redis 连接
- 配置 MySQL 连接

### Task 1.3: 配置安全框架
- 实现对称加密工具类
- 配置 Spring Security 基础框架
- 实现密码加密策略（BCrypt）

## 阶段二：数据库设计与实现

### Task 2.1: 创建数据库表结构
- 创建 `user_base` 表
- 创建 `user_auth` 表
- 创建 `user_profile` 表
- 创建 `user_real_name_auth` 表
- 创建 `user_audit_log` 表

### Task 2.2: 实现实体类与映射
- 实现 `UserBase` 实体类
- 实现 `UserAuth` 实体类
- 实现 `UserProfile` 实体类
- 实现 `UserRealNameAuth` 实体类
- 实现 `UserAuditLog` 实体类

### Task 2.3: 实现基础 Mapper 接口
- 实现 `UserBaseMapper` 接口
- 实现 `UserAuthMapper` 接口
- 实现 `UserProfileMapper` 接口
- 实现 `UserRealNameAuthMapper` 接口
- 实现 `UserAuditLogMapper` 接口

## 阶段三：用户基础功能实现

### Task 3.1: 实现登录功能
- 定义登录 DTO 和 VO
- 实现 `UserAuthService` 接口与实现类
- 实现登录逻辑
- 实现登录接口
- 实现登录日志记录

### Task 3.2: 实现注册功能
- 定义注册 DTO 和 VO
- 扩展 `UserAuthService` 实现注册逻辑
- 实现注册接口
- 实现注册日志记录

## 阶段四：实名认证功能实现

### Task 4.1: 实现实名认证服务
- 定义 RealNameAuthDTO 和 RealNameAuthVO
- 设计 API 接口
  - POST /api/auth/real-name
    - 请求参数：RealNameAuthDTO { userId, realName, idNumber }
    - 响应参数：RealNameAuthVO { userId, realName, idNumber, verifiedAt, status }
  - GET /api/auth/real-name/{userId}
    - 响应参数：RealNameAuthVO
- 实现 UserRealNameAuthService 接口与实现类
- 实现实名认证业务逻辑（接收客户端实名信息并写入 user_real_name_auth 表）
- 实现实名认证接口
- 实现实名认证日志记录（使用 @UserAudit 注解并发布 UserAuditEvent）

## 阶段五：账户合并功能实现

### Task 5.1: 实现账户合并服务
- 定义账户合并相关 DTO 和 VO
- 扩展 `UserRealNameAuthService` 实现账户合并逻辑
- 实现账户合并接口
- 实现账户合并日志记录

## 阶段六：个人资料管理功能实现

### Task 6.1: 实现个人资料查询功能
- 定义个人资料查询 VO
- 实现 `UserProfileService` 接口与实现类
- 实现个人资料查询接口

### Task 6.2: 实现个人资料修改功能
- 定义个人资料修改 DTO
- 扩展 `UserProfileService` 实现个人资料修改逻辑
- 实现个人资料修改接口
- 实现个人资料修改日志记录

## 阶段七：接口测试

### Task 7.1: 编写单元测试
- 为 Service 层编写单元测试
- 为 Controller 层编写单元测试

### Task 7.2: 编写集成测试
- 编写接口集成测试
- 编写数据库集成测试

## 具体实施步骤

1. 首先完成阶段一和阶段二，搭建好基础框架和数据库结构
2. 然后按照功能优先级实现阶段三至阶段六的功能
3. 最后完成阶段七的文档和测试工作

每个任务完成后应进行代码审查和简单测试，确保功能正常运行后再进入下一个任务。

这个计划是渐进式的，每个阶段都建立在前一个阶段的基础上，可以确保项目有序推进，同时也便于跟踪进度和调整计划。