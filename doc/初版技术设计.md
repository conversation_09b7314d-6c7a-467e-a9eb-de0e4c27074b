# 初版技术设计

产品文档：无

# 功能设计

## 认证管理 (登录、注册、登出)

*   **功能描述:** 处理用户的身份验证，允许用户通过不同方式（微信、支付宝、抖音小程序等）进入系统。
    
*   **涉及表格:** `user_auth`, `user_base`, `user_profile` (首次注册), `user_audit_log`
    
*   **核心逻辑:**
    
    *   **登录:**
        
        1.  用户通过第三方平台（如微信小程序）授权，后端获取到 `identifier` (如 OpenID) 和 `identity_type` ('WECHAT\_MP')。（业务侧处理，后期添加）
            
        2.  查询 `user_auth` 表：`SELECT user_id FROM user_auth WHERE identity_type = ? AND identifier = ? AND deleted_at IS NULL`。
            
        3.  **如果找到** `**user_id**`**:** 查询 `user_base` 确认用户状态 (`status = 1` 且 `deleted_at IS NULL`)。状态正常则登录成功，更新 `user_auth` 的 `last_login_at`，记录 `user_audit_log` (LOGIN\_SUCCESS)，生成用户会话/Token。
            
        4.  **如果未找到:** 说明是新用户（或此登录方式首次使用），进入隐式注册流程。
            
    *   **注册 (首次登录隐式触发):**
        
        1.  （承接登录未找到记录）开启数据库事务。
            
        2.  创建 `user_base` 记录 (设置初始 `status=1`, `real_name_verified=0`)，获取 `user_id`。
            
        3.  创建 `user_auth` 记录，关联 `user_id`、`identity_type`、`identifier`。
            
        4.  （可选）创建 `user_profile` 记录，可尝试从第三方平台获取初始昵称、头像。
            
        5.  提交事务。
            
        6.  记录 `user_audit_log` (REGISTER\_SUCCESS, LOGIN\_SUCCESS)，生成用户会话/Token。
            
    *   **登出:**
        
        1.  后端清除用户的会话/Token 信息。（业务侧处理，后期添加）
            
        2.  记录 `user_audit_log` (LOGOUT\_SUCCESS)。
            

## 账户关联与管理 (实名认证触发的合并)

*   **功能描述:** 在用户完成实名认证（业务侧处理，后期添加）后，将同一自然人通过不同方式注册的多个账户关联（合并）到同一个主账户上。
    
*   **涉及表格:** `user_base`, `user_auth`, `user_real_name_auth`, `user_audit_log`
    
*   **核心逻辑:** (在实名认证成功后触发)
    
    1.  当用户A (`current_user_id`) 实名认证成功，使用其身份证号 (`id_card_number_encrypted`) 查询 `user_base` 表，查找是否**已存在**另一个已实名 (`real_name_verified=1`) 且身份证号相同，并且状态正常 (`status=1`, `deleted_at IS NULL`) 的用户B (`existing_user_id`)。
        
    2.  **如果找到用户B:** 执行账户合并。
        
        *   记录 `user_audit_log` (ACCOUNT\_MERGE\_START)。
            
        *   开启数据库事务。
            
        *   将用户A 的所有活动 `user_auth` 记录的 `user_id` 更新为 `existing_user_id`。为每次更新记录 `user_audit_log` (ACCOUNT\_MERGE\_AUTH\_UPDATE)。
            
        *   处理 `user_profile` 的合并（策略可选：保留目标账户信息、提示用户选择、合并部分信息等）。记录 `user_audit_log`。
            
        *   将用户A 的 `user_base` 记录标记为已合并 (`status = 3`, `deleted_at = NOW()`)。记录 `user_audit_log` (ACCOUNT\_MERGE\_STATUS\_CHANGE)。
            
        *   提交事务。
            
        *   记录 `user_audit_log` (ACCOUNT\_MERGE\_COMPLETE)。
            
        *   将当前用户的会话/Token 切换到 `existing_user_id`。（业务侧处理，后期添加）
            
    3.  **如果未找到用户B:** 说明这是该自然人首次在本平台实名认证。
        
        *   更新当前用户A (`current_user_id`) 的 `user_base` 记录，设置 `real_name_verified=1`, `real_name`, `id_card_number`。
            

## 个人资料管理

*   **功能描述:** 允许用户查看和修改自己的昵称、头像、性别、地区等非敏感信息。
    
*   **涉及表格:** `user_profile`, `user_base` (可能需要读取 `user_id`), `user_audit_log`
    
*   **核心逻辑:**
    
    *   **查看:** 根据当前登录用户的 `user_id`，查询 `user_profile` 表 (`WHERE user_id = ? AND deleted_at IS NULL`) 获取信息展示。
        
    *   **修改:** 用户提交修改后的信息，后端验证通过后，`UPDATE user_profile SET ... WHERE user_id = ? AND deleted_at IS NULL`。记录 `user_audit_log` (PROFILE\_UPDATE)，包含修改前后的值（可选）。
        

## 账户安全与绑定管理

*   **功能描述:** 展示用户当前绑定的所有登录方式，允许用户解绑某个登录方式（不能解绑最后一个）。
    
*   **涉及表格:** `user_auth`, `user_audit_log`
    
*   **核心逻辑:**
    
    *   **查看绑定:** 查询 `user_auth` 表中属于当前 `user_id` 且 `deleted_at IS NULL` 的所有记录，展示 `identity_type` (如“微信小程序”、“支付宝小程序”) 和部分 `identifier` (脱敏后)。
        
    *   **解绑:**
        
        1.  用户选择要解绑的 `auth_id` 或 `identity_type`。
            
        2.  检查该用户是否还存在其他活动的 (`deleted_at IS NULL`) `user_auth` 记录。如果是最后一个，禁止解绑（除非是注销流程）。
            
        3.  逻辑删除选定的 `user_auth` 记录：`UPDATE user_auth SET deleted_at = NOW() WHERE auth_id = ? AND user_id = ? AND deleted_at IS NULL`。
            
        4.  记录 `user_audit_log` (AUTH\_METHOD\_REMOVE)。
            

## 实名认证流程

*   **功能描述:** 提供实名认证入口，记录认证过程和结果，并触发可能的账户合并。
    
*   **涉及表格:** `user_base`, `user_real_name_auth`, `user_auth`, `user_audit_log`
    
*   **核心逻辑:**
    
    1.  用户在前端提交姓名、身份证号。
        
    2.  后端检查 `user_base` 是否已认证。
        
    3.  记录 `user_audit_log` (REAL\_NAME\_AUTH\_SUBMIT)。创建 `user_real_name_auth` 记录 (状态：处理中)。
        
    4.  调用第三方接口进行认证。（业务侧处理，后期添加）
        
    5.  接收认证结果（成功/失败）。（业务侧处理，后期添加）
        
    6.  更新 `user_real_name_auth` 记录的状态、时间、原因等。
        
    7.  **如果成功:**
        
        *   记录 `user_audit_log` (REAL\_NAME\_AUTH\_SUCCESS)。
            
        *   执行\*\*第2点（账户关联与管理）\*\*中的逻辑，检查是否需要合并账户，并进行相应操作（合并或更新当前账户的实名状态）。
            
    8.  **如果失败:**
        
        *   记录 `user_audit_log` (REAL\_NAME\_AUTH\_FAIL)。通知用户失败原因。
            

## 账户注销 (逻辑删除)

*   **功能描述:** 允许用户申请注销账户，将相关数据标记为已删除。
    
*   **涉及表格:** `user_base`, `user_auth`, `user_profile`, `user_audit_log`
    
*   **核心逻辑:**
    
    1.  用户发起注销请求（可能需要二次确认或密码验证）。
        
    2.  开启数据库事务。
        
    3.  逻辑删除 `user_base` 记录：`UPDATE user_base SET deleted_at = NOW(), status = 0 WHERE user_id = ? AND deleted_at IS NULL`。
        
    4.  逻辑删除该 `user_id` 关联的所有 `user_auth` 记录：`UPDATE user_auth SET deleted_at = NOW() WHERE user_id = ? AND deleted_at IS NULL`。
        
    5.  逻辑删除该 `user_id` 关联的 `user_profile` 记录：`UPDATE user_profile SET deleted_at = NOW() WHERE user_id = ? AND deleted_at IS NULL`。
        
    6.  提交事务。
        
    7.  清除用户会话/Token。（业务侧处理，后期添加）
        
    8.  记录 `user_audit_log` (ACCOUNT\_DELETE\_REQUEST 或 ACCOUNT\_DELETED)。
        

# 数据库设计

##  `**user_base**` (用户基础表)

*   **目的:** 存储用户的核心唯一标识和实名认证状态。该表的一条记录代表一个经过识别或认证的独立个体。
    
*   **字段:**
    
    *   `user_id` (BIGINT, PK, Auto Increment or UUID): 用户唯一主键 ID。系统内用户的唯一标识。
        
    *   `real_name_verified` (TINYINT(1), Default: 0): 实名认证状态 (0: 未认证, 1: 已认证)。
        
    *   `real_name` (VARCHAR(100), Nullable, Index): 真实姓名 (认证后填充，脱敏存储或加密)。
        
    *   `id_card_number` (VARCHAR(255), Nullable, Unique, Index): 身份证号 (认证后填充，**必须加密存储**，建立索引方便查找是否已存在)。
        
    *   `status` (TINYINT, Default: 1): 用户状态 (1: 正常, 2: 禁用, 0: 注销/逻辑删除等)。
        
    *   `created_at` (TIMESTAMP / DATETIME): 记录创建时间。
        
    *   `updated_at` (TIMESTAMP / DATETIME): 记录最后更新时间。
        
*   **说明:**
    
    *   此表非常简洁，只包含识别用户身份和状态的核心信息。
        
    *   `id_card_number` 是关联不同登录方式记录为同一个人的关键。设置为 Unique 可以防止重复认证。**务必加密存储**。
        
    *   初始状态下（未实名认证），`real_name` 和 `id_card_number` 可以为 NULL。
        

##  `**user_auth**` (用户认证/登录方式表)

*   **目的:** 管理用户的多种登录方式和凭证。
    
*   **字段:**
    
    *   `auth_id` (BIGINT, PK, Auto Increment or UUID): 认证记录主键 ID。
        
    *   `user_id` (BIGINT, FK, Index): 关联到 `user_base` 表的 `user_id`。
        
    *   `identity_type` (VARCHAR(50), Index): 认证/登录类型。例如：'WECHAT\_MP' (微信小程序), 'ALIPAY\_MP' (支付宝小程序), 'DOUYIN\_MP' (抖音小程序), 'PHONE', 'EMAIL', 'USERNAME' 等。
        
    *   `identifier` (VARCHAR(255), Index): 凭证标识。根据 `identity_type` 不同而不同：
        
        *   微信小程序: OpenID
            
        *   支付宝小程序: User ID
            
        *   抖音小程序: OpenID
            
        *   手机号: 手机号码
            
        *   用户名: 用户名字符串
            
    *   `credential` (VARCHAR(255) / TEXT, Nullable): 凭证/密钥。例如：密码（**必须 HASH 存储**）、第三方平台的 access\_token (可加密存储)、union\_id (如果需要跨应用打通) 等。对于小程序登录，这里可能存储 session\_key 或其他关联信息。
        
    *   `verified` (TINYINT(1), Default: 0): 该认证方式是否已验证 (例如手机号、邮箱是否验证过)。
        
    *   `last_login_at` (TIMESTAMP / DATETIME, Nullable): 最后使用此方式登录的时间。
        
    *   `created_at` (TIMESTAMP / DATETIME): 记录创建时间。
        
    *   `updated_at` (TIMESTAMP / DATETIME): 记录最后更新时间。
        
*   **说明:**
    
    *   `user_id` + `identity_type` + `identifier` 理论上应该是唯一的，可以建立联合唯一索引 `uk_user_identity` (`identity_type`, `identifier`) 来确保一个外部 ID 只被一个用户绑定（但在合并场景下处理要小心）。或者只索引 `identity_type` 和 `identifier` 用于快速查找。
        
    *   该表解决了“一个人有多条用户登录记录”的问题。初始状态下，每个小程序登录都可能先创建一条 `user_auth` 记录，并关联到一个临时的或独立的 `user_base` 记录。
        

##  `**user_profile**` (用户资料扩展表)

*   **目的:** 存储用户的非核心、可修改的资料信息。
    
*   **字段:**
    
    *   `profile_id` (BIGINT, PK, Auto Increment or UUID): 资料记录主键 ID (或者直接用 `user_id` 做主键/外键)。
        
    *   `user_id` (BIGINT, FK, Unique, Index): 关联到 `user_base` 表的 `user_id`。设置为 Unique 确保一个用户只有一份 Profile。
        
    *   `nickname` (VARCHAR(100), Nullable): 用户昵称。
        
    *   `avatar_url` (VARCHAR(512), Nullable): 头像链接。
        
    *   `gender` (TINYINT, Nullable): 性别 (0: 未知, 1: 男, 2: 女)。
        
    *   `birthday` (DATE, Nullable): 生日。
        
    *   `province` (VARCHAR(50), Nullable): 省份。
        
    *   `city` (VARCHAR(50), Nullable): 城市。
        
    *   `bio` (VARCHAR(255), Nullable): 个人简介/签名。
        
    *   `created_at` (TIMESTAMP / DATETIME): 记录创建时间。
        
    *   `updated_at` (TIMESTAMP / DATETIME): 记录最后更新时间。
        
*   **说明:**
    
    *   保持 `user_base` 表的简洁性。所有这些信息都与核心身份识别无关，适合放在扩展表。
        

##  `**user_real_name_auth**` (用户实名认证记录表)

*   **目的:** 记录用户提交实名认证的尝试和结果，方便追踪和审计。
    
*   **字段:**
    
    *   `auth_record_id` (BIGINT, PK, Auto Increment or UUID): 认证记录主键。
        
    *   `user_id` (BIGINT, FK, Index): 关联到 `user_base` 表的 `user_id` (关联到最终认证成功的那个 `user_id`)。
        
    *   `submitted_real_name` (VARCHAR(100)): 提交时使用的真实姓名（可考虑加密）。
        
    *   `submitted_id_card_number` (VARCHAR(255)): 提交时使用的身份证号（**必须加密存储**）。
        
    *   `auth_channel` (VARCHAR(50), Nullable): 认证渠道 (例如：'PROVIDER\_A', 'GOVERNMENT\_API')。
        
    *   `auth_status` (TINYINT): 认证状态 (例如：0: 待认证, 1: 认证成功, 2: 认证失败)。
        
    *   `auth_time` (TIMESTAMP / DATETIME, Nullable): 认证完成时间。
        
    *   `fail_reason` (TEXT, Nullable): 认证失败原因。
        
    *   `transaction_id` (VARCHAR(255), Nullable): 第三方认证渠道的流水号。
        
    *   `created_at` (TIMESTAMP / DATETIME): 记录创建时间。
        
    *   `updated_at` (TIMESTAMP / DATETIME): 记录最后更新时间。
        
*   **说明:**
    
    *   存储每次认证尝试，而不是只存最终结果，便于排查问题。
        

## `**user_audit_log**`（用户审计表）:

*   目的：记录用户信息所有操作，日后审计用
    
*   字段：
    
    *   log\_id日志主键ID
        
    *   user\_id主要关联的用户ID (操作对象)
        
    *   ...
        
*   说明：
    

1.  包含了操作主体 (`actor_user_id`) 和操作对象 (`user_id`)。
    
2.  `action_type` 用于区分具体操作。对于账户合并，可以记录多个步骤：
    
    1.  `ACCOUNT_MERGE_START` (details 包含 from\_user\_id, to\_user\_id)
        
    2.  `ACCOUNT_MERGE_AUTH_UPDATE` (target\_type='USER\_AUTH', target\_id=auth\_id, old\_value=from\_user\_id, new\_value=to\_user\_id) - **为每一条被更新的 user\_auth 记录都记一条日志**。
        
    3.  `ACCOUNT_MERGE_PROFILE_UPDATE` (如果合并了 profile)
        
    4.  `ACCOUNT_MERGE_STATUS_CHANGE` (target\_type='USER\_BASE', target\_id=from\_user\_id, old\_value=1, new\_value=3)
        
    5.  `ACCOUNT_MERGE_COMPLETE` (details 包含 from\_user\_id, to\_user\_id)
        
3.  `old_value`, `new_value`, `details` 提供了详细的上下文信息。`details` 字段建议使用 JSON 格式存储结构化信息。
    

```mysql
-- ----------------------------
-- 用户基础表 (核心身份信息)
-- ----------------------------
CREATE TABLE `user_base` (
  `user_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户唯一主键ID',
  `real_name_verified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '实名认证状态 (0: 未认证, 1: 已认证)',
  `real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '真实姓名 (认证后填充, 应用层需加密/脱敏)',
  `id_card_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号 (认证后填充, 应用层必须加密存储)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '用户状态 (1: 正常, 2: 禁用, 0: 已注销/逻辑删除前状态, 3: 已合并)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '逻辑删除时间 (NULL表示未删除)',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_id_card_number` (`id_card_number`) COMMENT '身份证号唯一索引 (加密后存储也要保证唯一性)',
  KEY `idx_real_name` (`real_name`) COMMENT '真实姓名索引 (加密后可能意义不大, 视查询需求)',
  KEY `idx_deleted_at` (`deleted_at`) COMMENT '逻辑删除索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户基础表 (核心身份信息)';

-- ----------------------------
-- 用户认证/登录方式表
-- ----------------------------
CREATE TABLE `user_auth` (
  `auth_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '认证记录主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '关联的用户基础ID (逻辑外键)',
  `identity_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '认证/登录类型 (WECHAT_MP, ALIPAY_MP, DOUYIN_MP, PHONE, EMAIL, USERNAME)',
  `identifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '凭证标识 (OpenID, UserID, 手机号, 用户名等)',
  `credential` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '凭证/密钥 (密码需Hash存储, Token可加密)',
  `verified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '该认证方式是否已验证 (如手机号、邮箱)',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后使用此方式登录的时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '逻辑删除时间 (NULL表示未删除)',
  PRIMARY KEY (`auth_id`),
  UNIQUE KEY `uk_identity` (`identity_type`, `identifier`, `deleted_at`) COMMENT '同一类型下的凭证标识唯一 (考虑逻辑删除)', -- 将deleted_at加入唯一键确保可重复解绑再绑定
  KEY `idx_user_id` (`user_id`) COMMENT '用于关联user_base的索引',
  KEY `idx_deleted_at` (`deleted_at`) COMMENT '逻辑删除索引'
  -- 注意: uk_identity的调整, 允许多个记录有相同的 identity_type 和 identifier, 但最多只有一个 deleted_at IS NULL。
  -- 或者，如果业务不允许同一个外部ID解绑后重新绑定到 *不同* 用户，uk_identity 可以不包含 deleted_at，但应用层逻辑要处理冲突。
  -- 这里采用包含 deleted_at 的方式，更灵活，允许解绑后重新绑定。
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户认证/登录方式表';

-- ----------------------------
-- 用户资料扩展表
-- ----------------------------
CREATE TABLE `user_profile` (
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID (主键, 逻辑外键关联user_base)',
  `nickname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户昵称',
  `avatar_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像链接',
  `gender` tinyint DEFAULT NULL COMMENT '性别 (0: 未知, 1: 男, 2: 女)',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '城市',
  `bio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '个人简介/签名',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '逻辑删除时间 (NULL表示未删除)',
  PRIMARY KEY (`user_id`),
  KEY `idx_deleted_at` (`deleted_at`) COMMENT '逻辑删除索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户资料扩展表';

-- ----------------------------
-- 用户实名认证记录表 (保持不变, 通常不逻辑删除)
-- ----------------------------
CREATE TABLE `user_real_name_auth` (
  `auth_record_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '认证记录主键',
  `user_id` bigint unsigned NOT NULL COMMENT '关联的用户基础ID (逻辑外键, 关联最终认证成功的user_id)',
  `submitted_real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提交时使用的真实姓名 (应用层考虑加密)',
  `submitted_id_card_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提交时使用的身份证号 (应用层必须加密存储)',
  `auth_channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '认证渠道',
  `auth_status` tinyint NOT NULL COMMENT '认证状态 (0: 待认证/处理中, 1: 成功, 2: 失败)',
  `auth_time` timestamp NULL DEFAULT NULL COMMENT '认证完成时间',
  `fail_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '认证失败原因',
  `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '第三方认证渠道的流水号',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  PRIMARY KEY (`auth_record_id`),
  KEY `idx_user_id` (`user_id`) COMMENT '用于关联user_base的索引',
  KEY `idx_auth_status` (`auth_status`),
  KEY `idx_auth_time` (`auth_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户实名认证记录表';


-- ----------------------------
-- 用户操作审计日志表 (保持不变, 不逻辑删除)
-- ----------------------------
CREATE TABLE `user_audit_log` (
  `log_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志主键ID',
  `user_id` bigint unsigned DEFAULT NULL COMMENT '主要关联的用户ID (操作对象)',
  `actor_user_id` bigint unsigned DEFAULT NULL COMMENT '执行操作的用户ID (可能是用户自己, 可能是管理员)',
  `action_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型 (LOGIN_SUCCESS, REAL_NAME_AUTH_SUBMIT, ACCOUNT_MERGE_AUTH_UPDATE, etc.)',
  `target_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作目标类型 (USER_AUTH, USER_PROFILE_FIELD, etc.)',
  `target_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作目标ID或标识符',
  `status` tinyint DEFAULT NULL COMMENT '操作结果状态 (1: 成功, 0: 失败)',
  `old_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '旧值 (例如, 合并前的user_id)',
  `new_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '新值 (例如, 合并后的user_id)',
  `details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '详细信息 (例如 IP, UserAgent, 失败原因, 合并来源user_id, JSON格式)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '日志记录时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_actor_user_id` (`actor_user_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户操作审计日志表';
```