# 简历解析API功能实现文档

## 概述

本文档描述了用户中心系统中新增的简历解析API功能的完整实现方案。该功能允许用户上传简历文件（支持doc、docx、pdf格式），调用第三方简历解析服务进行解析，并将解析结果转换为系统内部数据结构存储到数据库中。

## 功能特性

- ✅ 支持多种文件格式（doc、docx、pdf）
- ✅ 文件大小和类型验证
- ✅ 第三方简历解析服务集成
- ✅ 智能数据转换和映射
- ✅ 重复数据检测和处理
- ✅ 完整的异常处理机制
- ✅ 解析记录追踪
- ✅ 可配置的解析选项
- ✅ 重试机制
- ✅ 完整的单元测试和集成测试

## 数据库设计

### 新增表结构

#### 1. 用户技能表 (user_skills)
存储用户的技能信息，支持IT技能、业务技能、语言技能等分类。

#### 2. 用户证书表 (user_certificates)
存储用户的证书和认证信息。

#### 3. 用户语言能力表 (user_languages)
存储用户的语言能力信息，包括听说读写各项水平。

#### 4. 用户获奖记录表 (user_awards)
存储用户的获奖和荣誉信息。

#### 5. 简历解析记录表 (resume_parse_records)
记录每次简历解析的详细信息，包括解析状态、结果、错误信息等。

### 现有表扩展
利用现有的用户相关表存储基本信息：
- `user_profile` - 基本个人信息
- `user_contact_methods` - 联系方式
- `user_education_history` - 教育经历
- `user_work_history` - 工作经历
- `user_project_history` - 项目经历
- `user_training_history` - 培训经历

## API接口设计

### 1. 上传并解析简历
```
POST /api/v1/me/resume/parse
Content-Type: multipart/form-data

参数：
- file: 简历文件 (必需)
- overwriteExisting: 是否覆盖现有数据 (可选，默认false)
- parseBasicInfo: 是否解析基本信息 (可选，默认true)
- parseContactInfo: 是否解析联系方式 (可选，默认true)
- parseEducation: 是否解析教育经历 (可选，默认true)
- parseWorkExperience: 是否解析工作经历 (可选，默认true)
- parseProjectExperience: 是否解析项目经历 (可选，默认true)
- parseSkills: 是否解析技能信息 (可选，默认true)
- parseTraining: 是否解析培训经历 (可选，默认true)
- parseLanguages: 是否解析语言能力 (可选，默认true)
- parseCertificates: 是否解析证书信息 (可选，默认true)
- parseAwards: 是否解析获奖记录 (可选，默认true)
```

### 2. 获取解析记录列表
```
GET /api/v1/me/resume/parse-records
```

### 3. 获取特定解析记录详情
```
GET /api/v1/me/resume/parse-records/{recordId}
```

## 核心组件

### 1. ResumeParseController
处理HTTP请求，参数验证，调用业务服务。

### 2. ResumeParseService
主要业务逻辑，协调各个组件完成简历解析流程。

### 3. ThirdPartyResumeParseService
负责调用第三方简历解析API，处理文件上传和结果获取。

### 4. ResumeDataConversionService
将第三方解析结果转换为系统内部数据结构。

### 5. 各种Mapper接口
数据访问层，负责数据库操作。

## 数据转换逻辑

### 基本信息转换
- 姓名 → nickname
- 性别 → gender (男=1, 女=2, 其他=0)
- 生日 → birthday
- 地区信息 → regionName, address
- 期望职位和行业 → bio

### 联系方式转换
- 手机号 → contactType=2
- 邮箱 → contactType=1
- 微信 → contactType=4
- QQ → contactType=5

### 教育经历转换
- 学校名称、学位、专业直接映射
- 学位等级智能识别（博士=4, 硕士=3, 本科=2, 专科=1, 高中=0）
- 时间格式转换

### 工作经历转换
- 公司信息、职位、部门直接映射
- 公司规模智能转换
- 薪资范围解析

### 技能转换
- IT技能 → skillType=1
- 业务技能 → skillType=2
- 语言技能 → skillType=3
- 其他技能 → skillType=4
- 自动去重处理

## 异常处理

### 文件验证异常
- 文件为空
- 文件过大
- 不支持的文件类型

### 解析异常
- 第三方服务调用失败
- 解析结果验证失败
- 网络超时

### 数据存储异常
- 数据库连接失败
- 数据转换错误
- 约束违反

## 配置说明

```yaml
resume:
  parse:
    api-url: http://*************:8000/parse_file  # 第三方API地址
    timeout: 30000                                  # 超时时间(毫秒)
    max-retries: 3                                  # 最大重试次数
    supported-file-types:                           # 支持的文件类型
      - doc
      - docx
      - pdf
    max-file-size: 10485760                         # 最大文件大小(字节)
    enable-file-cache: true                         # 是否启用文件缓存
    file-cache-dir: /tmp/resume-cache               # 文件缓存目录
    enable-result-cache: true                       # 是否启用结果缓存
    result-cache-expire: 3600                       # 结果缓存过期时间(秒)
```

## 测试覆盖

### 单元测试
- ✅ ResumeParseServiceTest - 主要业务逻辑测试
- ✅ ResumeDataConversionServiceTest - 数据转换逻辑测试
- ✅ ThirdPartyResumeParseServiceTest - 第三方服务调用测试

### 集成测试
- ✅ ResumeParseControllerTest - API接口测试

### 测试场景
- 成功解析流程
- 文件验证失败
- 第三方服务异常
- 数据转换异常
- 重复数据处理
- 重试机制
- 参数验证

## 部署说明

1. 执行数据库迁移脚本：`V3__Create_Resume_Related_Tables.sql`
2. 更新应用配置文件，配置第三方API地址和相关参数
3. 确保第三方简历解析服务可访问
4. 重启应用服务

## 监控和日志

- 解析请求和响应日志
- 第三方服务调用监控
- 解析成功率统计
- 错误率和异常监控
- 性能指标监控

## 安全考虑

- 文件类型和大小限制
- 用户权限验证
- 敏感信息脱敏
- 文件临时存储清理
- API访问频率限制

## 性能优化

- 异步处理大文件
- 结果缓存机制
- 数据库批量操作
- 连接池优化
- 重试策略优化

## 扩展性

- 支持更多文件格式
- 多个第三方解析服务
- 自定义解析规则
- 解析结果后处理
- 批量解析功能

## 问题排查

### 常见问题
1. 文件上传失败 - 检查文件大小和格式
2. 解析超时 - 调整超时配置或检查网络
3. 数据存储失败 - 检查数据库连接和表结构
4. 重复数据 - 检查去重逻辑和数据库约束

### 日志关键字
- "简历解析" - 主要业务流程
- "第三方服务" - 外部API调用
- "数据转换" - 数据处理过程
- "文件验证" - 文件检查过程

## 总结

简历解析API功能已完整实现，包括：
- 完整的数据库设计和表结构
- 健壮的API接口和业务逻辑
- 智能的数据转换和映射
- 完善的异常处理机制
- 全面的测试覆盖

该功能为用户提供了便捷的简历信息导入方式，大大提升了用户体验和数据录入效率。
