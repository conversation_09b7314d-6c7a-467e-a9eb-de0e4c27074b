#!/bin/bash

# 用户中心监控系统快速部署脚本
# 用途：一键部署完整的监控栈

set -e

# 配置参数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
MONITORING_DIR="$PROJECT_ROOT/monitoring-configs"
DOCKER_COMPOSE_FILE="$MONITORING_DIR/docker-compose-monitoring.yml"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')] $1${NC}"
}

log_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] ✅ $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')] ❌ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')] ⚠️  $1${NC}"
}

log_info() {
    echo -e "${PURPLE}[$(date '+%H:%M:%S')] ℹ️  $1${NC}"
}

# 显示横幅
show_banner() {
    echo -e "${BLUE}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                    用户中心监控系统部署                        ║
║                                                              ║
║  🔍 Prometheus + Grafana + AlertManager + Zipkin            ║
║  📊 业务指标 + 健康检查 + 链路追踪                            ║
║  🚨 智能告警 + 自动化运维                                     ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# 检查系统要求
check_requirements() {
    log "检查系统要求..."
    
    # 检查Docker
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose >/dev/null 2>&1; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查端口占用
    local ports=(3000 9090 9093 9411 9100 8080)
    for port in "${ports[@]}"; do
        if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
            log_warning "端口 $port 已被占用，可能会导致服务启动失败"
        fi
    done
    
    # 检查磁盘空间
    local available_space
    available_space=$(df / | awk 'NR==2 {print $4}')
    if [ "$available_space" -lt 2097152 ]; then  # 2GB in KB
        log_warning "可用磁盘空间不足2GB，建议释放更多空间"
    fi
    
    log_success "系统要求检查完成"
}

# 创建必要的目录和文件
setup_directories() {
    log "创建监控配置目录..."
    
    # 创建Grafana配置目录
    mkdir -p "$MONITORING_DIR/grafana/provisioning/datasources"
    mkdir -p "$MONITORING_DIR/grafana/provisioning/dashboards"
    mkdir -p "$MONITORING_DIR/grafana/dashboards"
    
    # 创建AlertManager模板目录
    mkdir -p "$MONITORING_DIR/alertmanager/templates"
    
    # 创建Nginx配置目录
    mkdir -p "$MONITORING_DIR/nginx"
    
    log_success "目录创建完成"
}

# 生成Grafana数据源配置
create_grafana_datasource() {
    log "生成Grafana数据源配置..."
    
    cat > "$MONITORING_DIR/grafana/provisioning/datasources/prometheus.yml" << 'EOF'
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "5s"
      queryTimeout: "60s"
      httpMethod: "POST"
EOF

    log_success "Grafana数据源配置已生成"
}

# 生成Grafana仪表板配置
create_grafana_dashboard_config() {
    log "生成Grafana仪表板配置..."
    
    cat > "$MONITORING_DIR/grafana/provisioning/dashboards/dashboards.yml" << 'EOF'
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards
EOF

    log_success "Grafana仪表板配置已生成"
}

# 生成用户中心专用仪表板
create_user_center_dashboard() {
    log "生成用户中心专用仪表板..."
    
    cat > "$MONITORING_DIR/grafana/dashboards/user-center-dashboard.json" << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "用户中心监控仪表板",
    "tags": ["user-center", "business"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "用户注册趋势",
        "type": "graph",
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
        "targets": [
          {
            "expr": "rate(user_registration_total[5m]) * 60",
            "legendFormat": "注册速率 (每分钟)",
            "refId": "A"
          }
        ],
        "yAxes": [
          {"label": "注册数/分钟", "min": 0},
          {"show": false}
        ],
        "xAxis": {"show": true},
        "legend": {"show": true}
      },
      {
        "id": 2,
        "title": "登录成功率",
        "type": "stat",
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
        "targets": [
          {
            "expr": "rate(user_login_total[5m]) / (rate(user_login_total[5m]) + rate(user_login_failure_total[5m])) * 100",
            "legendFormat": "成功率",
            "refId": "A"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent",
            "min": 0,
            "max": 100,
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "yellow", "value": 80},
                {"color": "green", "value": 95}
              ]
            }
          }
        }
      },
      {
        "id": 3,
        "title": "缓存性能",
        "type": "graph",
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8},
        "targets": [
          {
            "expr": "cache_hit_rate",
            "legendFormat": "缓存命中率 (%)",
            "refId": "A"
          },
          {
            "expr": "rate(cache_hit_total[5m])",
            "legendFormat": "缓存命中速率",
            "refId": "B"
          }
        ]
      }
    ],
    "time": {"from": "now-1h", "to": "now"},
    "refresh": "5s",
    "schemaVersion": 27,
    "version": 1
  }
}
EOF

    log_success "用户中心仪表板已生成"
}

# 生成Nginx配置
create_nginx_config() {
    log "生成Nginx反向代理配置..."
    
    cat > "$MONITORING_DIR/nginx/nginx.conf" << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream grafana {
        server grafana:3000;
    }
    
    upstream prometheus {
        server prometheus:9090;
    }
    
    upstream alertmanager {
        server alertmanager:9093;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location /grafana/ {
            proxy_pass http://grafana/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /prometheus/ {
            proxy_pass http://prometheus/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /alertmanager/ {
            proxy_pass http://alertmanager/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location / {
            return 301 /grafana/;
        }
    }
}
EOF

    log_success "Nginx配置已生成"
}

# 启动监控服务
start_monitoring_services() {
    log "启动监控服务..."
    
    cd "$MONITORING_DIR"
    
    # 拉取镜像
    log "拉取Docker镜像..."
    docker-compose -f docker-compose-monitoring.yml pull
    
    # 启动服务
    log "启动服务容器..."
    docker-compose -f docker-compose-monitoring.yml up -d
    
    # 等待服务启动
    log "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    check_services_health
    
    log_success "监控服务启动完成"
}

# 检查服务健康状态
check_services_health() {
    log "检查服务健康状态..."
    
    local services=(
        "Prometheus:http://localhost:9090/-/healthy"
        "Grafana:http://localhost:3000/api/health"
        "AlertManager:http://localhost:9093/-/healthy"
        "Zipkin:http://localhost:9411/health"
    )
    
    for service_info in "${services[@]}"; do
        local service_name="${service_info%%:*}"
        local health_url="${service_info#*:}"
        
        local max_attempts=6
        local attempt=1
        
        while [ $attempt -le $max_attempts ]; do
            if curl -s "$health_url" >/dev/null 2>&1; then
                log_success "$service_name 服务健康"
                break
            else
                if [ $attempt -eq $max_attempts ]; then
                    log_error "$service_name 服务不健康"
                else
                    log "等待 $service_name 服务启动... (尝试 $attempt/$max_attempts)"
                    sleep 10
                fi
            fi
            ((attempt++))
        done
    done
}

# 配置定时任务
setup_cron_jobs() {
    log "配置定时任务..."
    
    # 创建cron任务文件
    cat > /tmp/user-center-monitoring-cron << EOF
# 用户中心监控定时任务

# 每5分钟执行健康检查
*/5 * * * * $SCRIPT_DIR/health_check.sh >/dev/null 2>&1

# 每10分钟收集指标
*/10 * * * * $SCRIPT_DIR/metrics_collector.sh >/dev/null 2>&1

# 每小时生成报告
0 * * * * $SCRIPT_DIR/metrics_collector.sh -r >/dev/null 2>&1

# 每天清理过期文件
0 2 * * * $SCRIPT_DIR/metrics_collector.sh -c >/dev/null 2>&1
EOF

    # 安装cron任务
    if command -v crontab >/dev/null 2>&1; then
        crontab /tmp/user-center-monitoring-cron
        rm /tmp/user-center-monitoring-cron
        log_success "定时任务配置完成"
    else
        log_warning "crontab 未找到，请手动配置定时任务"
        log_info "定时任务配置文件已保存到: /tmp/user-center-monitoring-cron"
    fi
}

# 显示访问信息
show_access_info() {
    echo
    log_info "监控系统部署完成！访问信息如下："
    echo
    echo -e "${GREEN}📊 Grafana 仪表板:${NC}"
    echo -e "   URL: ${BLUE}http://localhost:3000${NC}"
    echo -e "   用户名: ${YELLOW}admin${NC}"
    echo -e "   密码: ${YELLOW}admin123${NC}"
    echo
    echo -e "${GREEN}🔍 Prometheus 监控:${NC}"
    echo -e "   URL: ${BLUE}http://localhost:9090${NC}"
    echo
    echo -e "${GREEN}🚨 AlertManager 告警:${NC}"
    echo -e "   URL: ${BLUE}http://localhost:9093${NC}"
    echo
    echo -e "${GREEN}🔗 Zipkin 链路追踪:${NC}"
    echo -e "   URL: ${BLUE}http://localhost:9411${NC}"
    echo
    echo -e "${GREEN}📈 用户中心监控API:${NC}"
    echo -e "   健康检查: ${BLUE}http://localhost:8080/api/monitoring/health${NC}"
    echo -e "   指标概览: ${BLUE}http://localhost:8080/api/monitoring/metrics/overview${NC}"
    echo
    echo -e "${YELLOW}💡 提示:${NC}"
    echo -e "   - 首次访问Grafana需要修改默认密码"
    echo -e "   - 可以通过 ${BLUE}docker-compose logs -f${NC} 查看服务日志"
    echo -e "   - 使用 ${BLUE}$SCRIPT_DIR/health_check.sh${NC} 手动执行健康检查"
    echo -e "   - 使用 ${BLUE}$SCRIPT_DIR/metrics_collector.sh -r${NC} 生成指标报告"
    echo
}

# 主函数
main() {
    show_banner
    
    log "开始部署用户中心监控系统..."
    
    # 检查系统要求
    check_requirements
    
    # 创建目录结构
    setup_directories
    
    # 生成配置文件
    create_grafana_datasource
    create_grafana_dashboard_config
    create_user_center_dashboard
    create_nginx_config
    
    # 启动服务
    start_monitoring_services
    
    # 配置定时任务
    if [ "${SETUP_CRON:-true}" = "true" ]; then
        setup_cron_jobs
    fi
    
    # 显示访问信息
    show_access_info
    
    log_success "监控系统部署完成！"
}

# 显示帮助信息
show_help() {
    cat << EOF
用户中心监控系统部署脚本

用法: $0 [选项]

选项:
    -h, --help              显示帮助信息
    --no-cron               不配置定时任务
    --check-only            仅检查系统要求
    --stop                  停止监控服务
    --restart               重启监控服务
    --status                查看服务状态

示例:
    $0                      完整部署监控系统
    $0 --check-only         仅检查系统要求
    $0 --no-cron            部署但不配置定时任务
    $0 --stop               停止所有监控服务
    $0 --restart            重启所有监控服务

EOF
}

# 停止监控服务
stop_monitoring_services() {
    log "停止监控服务..."
    
    cd "$MONITORING_DIR"
    docker-compose -f docker-compose-monitoring.yml down
    
    log_success "监控服务已停止"
}

# 重启监控服务
restart_monitoring_services() {
    log "重启监控服务..."
    
    cd "$MONITORING_DIR"
    docker-compose -f docker-compose-monitoring.yml restart
    
    # 等待服务启动
    sleep 30
    check_services_health
    
    log_success "监控服务已重启"
}

# 查看服务状态
show_services_status() {
    log "查看服务状态..."
    
    cd "$MONITORING_DIR"
    docker-compose -f docker-compose-monitoring.yml ps
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --no-cron)
            export SETUP_CRON=false
            shift
            ;;
        --check-only)
            check_requirements
            exit 0
            ;;
        --stop)
            stop_monitoring_services
            exit 0
            ;;
        --restart)
            restart_monitoring_services
            exit 0
            ;;
        --status)
            show_services_status
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main "$@"
