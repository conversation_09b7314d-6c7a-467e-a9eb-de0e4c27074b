#!/bin/bash

# 用户中心指标收集脚本
# 用途：定期收集业务指标并存储到文件或发送到外部系统

set -e

# 配置参数
APP_NAME="用户中心"
METRICS_URL="http://localhost:8080/api/monitoring/metrics/overview"
PROMETHEUS_URL="http://localhost:18080/actuator/prometheus"
OUTPUT_DIR="/var/log/user-center-metrics"
RETENTION_DAYS=30
TIMEOUT=10

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}$(date '+%Y-%m-%d %H:%M:%S') - $1${NC}"
}

log_error() {
    echo -e "${RED}$(date '+%Y-%m-%d %H:%M:%S') - ERROR: $1${NC}"
}

log_success() {
    echo -e "${GREEN}$(date '+%Y-%m-%d %H:%M:%S') - SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}$(date '+%Y-%m-%d %H:%M:%S') - WARNING: $1${NC}"
}

# 创建输出目录
create_output_dir() {
    if [ ! -d "$OUTPUT_DIR" ]; then
        mkdir -p "$OUTPUT_DIR"
        log "创建输出目录: $OUTPUT_DIR"
    fi
}

# 收集业务指标
collect_business_metrics() {
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local output_file="$OUTPUT_DIR/business_metrics_$timestamp.json"
    
    log "开始收集业务指标..."
    
    local metrics_data
    metrics_data=$(curl -s --max-time "$TIMEOUT" "$METRICS_URL" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$metrics_data" ]; then
        echo "$metrics_data" > "$output_file"
        log_success "业务指标已保存到: $output_file"
        
        # 解析关键指标
        if command -v jq >/dev/null 2>&1; then
            local total_users cache_hit_rate active_sessions pending_ops
            total_users=$(echo "$metrics_data" | jq -r '.data.infrastructure_metrics.total_users // 0')
            cache_hit_rate=$(echo "$metrics_data" | jq -r '.data.performance_metrics.cache_hit_rate // 0')
            active_sessions=$(echo "$metrics_data" | jq -r '.data.performance_metrics.active_sessions // 0')
            pending_ops=$(echo "$metrics_data" | jq -r '.data.performance_metrics.pending_operations // 0')
            
            log "关键指标 - 用户总数: $total_users, 缓存命中率: ${cache_hit_rate}%, 活跃会话: $active_sessions, 待处理操作: $pending_ops"
            
            # 生成CSV格式的指标摘要
            local csv_file="$OUTPUT_DIR/metrics_summary.csv"
            if [ ! -f "$csv_file" ]; then
                echo "timestamp,total_users,cache_hit_rate,active_sessions,pending_operations" > "$csv_file"
            fi
            echo "$(date '+%Y-%m-%d %H:%M:%S'),$total_users,$cache_hit_rate,$active_sessions,$pending_ops" >> "$csv_file"
        fi
        
        return 0
    else
        log_error "无法收集业务指标"
        return 1
    fi
}

# 收集Prometheus指标
collect_prometheus_metrics() {
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local output_file="$OUTPUT_DIR/prometheus_metrics_$timestamp.txt"
    
    log "开始收集Prometheus指标..."
    
    local prometheus_data
    prometheus_data=$(curl -s --max-time "$TIMEOUT" "$PROMETHEUS_URL" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$prometheus_data" ]; then
        echo "$prometheus_data" > "$output_file"
        log_success "Prometheus指标已保存到: $output_file"
        
        # 统计指标数量
        local metric_count
        metric_count=$(echo "$prometheus_data" | grep -c '^[a-zA-Z]' || echo "0")
        log "收集到 $metric_count 个Prometheus指标"
        
        return 0
    else
        log_error "无法收集Prometheus指标"
        return 1
    fi
}

# 生成指标报告
generate_metrics_report() {
    local report_file="$OUTPUT_DIR/metrics_report_$(date '+%Y%m%d_%H%M%S').html"
    
    log "生成指标报告..."
    
    # 获取最新的业务指标文件
    local latest_metrics_file
    latest_metrics_file=$(ls -t "$OUTPUT_DIR"/business_metrics_*.json 2>/dev/null | head -1)
    
    if [ -z "$latest_metrics_file" ]; then
        log_error "未找到业务指标文件"
        return 1
    fi
    
    # 生成HTML报告
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$APP_NAME 指标报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px; }
        .metric-card { background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0; }
        .metric-title { font-weight: bold; color: #495057; margin-bottom: 10px; }
        .metric-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .timestamp { text-align: center; color: #6c757d; margin-top: 20px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #dee2e6; padding: 8px; text-align: left; }
        th { background-color: #e9ecef; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>$APP_NAME 指标报告</h1>
            <p>生成时间: $(date '+%Y-%m-%d %H:%M:%S')</p>
        </div>
EOF

    # 解析并添加指标数据
    if command -v jq >/dev/null 2>&1; then
        local metrics_data
        metrics_data=$(cat "$latest_metrics_file")
        
        # 用户指标
        cat >> "$report_file" << EOF
        <h2>用户指标</h2>
        <div class="metric-grid">
            <div class="metric-card">
                <div class="metric-title">用户总数</div>
                <div class="metric-value">$(echo "$metrics_data" | jq -r '.data.infrastructure_metrics.total_users // 0')</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">注册总数</div>
                <div class="metric-value">$(echo "$metrics_data" | jq -r '.data.user_metrics.total_registrations // 0')</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">登录总数</div>
                <div class="metric-value">$(echo "$metrics_data" | jq -r '.data.user_metrics.total_logins // 0')</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">登录失败数</div>
                <div class="metric-value status-warning">$(echo "$metrics_data" | jq -r '.data.user_metrics.login_failures // 0')</div>
            </div>
        </div>

        <h2>性能指标</h2>
        <div class="metric-grid">
            <div class="metric-card">
                <div class="metric-title">缓存命中率</div>
                <div class="metric-value status-good">$(echo "$metrics_data" | jq -r '.data.performance_metrics.cache_hit_rate // 0')%</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">活跃会话数</div>
                <div class="metric-value">$(echo "$metrics_data" | jq -r '.data.performance_metrics.active_sessions // 0')</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">待处理操作</div>
                <div class="metric-value">$(echo "$metrics_data" | jq -r '.data.performance_metrics.pending_operations // 0')</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">缓存命中数</div>
                <div class="metric-value">$(echo "$metrics_data" | jq -r '.data.performance_metrics.cache_hits // 0')</div>
            </div>
        </div>

        <h2>基础设施指标</h2>
        <div class="metric-grid">
            <div class="metric-card">
                <div class="metric-title">数据库连接数</div>
                <div class="metric-value">$(echo "$metrics_data" | jq -r '.data.infrastructure_metrics.database_connections // 0')</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">Redis连接状态</div>
                <div class="metric-value status-good">$(echo "$metrics_data" | jq -r '.data.infrastructure_metrics.redis_connection_status // 0')</div>
            </div>
        </div>
EOF
    fi

    # 添加系统信息
    cat >> "$report_file" << EOF
        <h2>系统信息</h2>
        <table>
            <tr><th>项目</th><th>值</th></tr>
            <tr><td>服务器时间</td><td>$(date)</td></tr>
            <tr><td>系统负载</td><td>$(uptime | awk -F'load average:' '{print $2}')</td></tr>
            <tr><td>内存使用率</td><td>$(free | awk 'NR==2{printf "%.1f%%", $3*100/$2}')</td></tr>
            <tr><td>磁盘使用率</td><td>$(df / | awk 'NR==2 {print $5}')</td></tr>
            <tr><td>运行时间</td><td>$(uptime | awk '{print $3,$4}' | sed 's/,//')</td></tr>
        </table>

        <div class="timestamp">
            <p>报告文件: $(basename "$report_file")</p>
            <p>数据来源: $(basename "$latest_metrics_file")</p>
        </div>
    </div>
</body>
</html>
EOF

    log_success "指标报告已生成: $report_file"
    echo "$report_file"
}

# 清理过期文件
cleanup_old_files() {
    log "清理 $RETENTION_DAYS 天前的文件..."
    
    local deleted_count=0
    
    # 清理JSON文件
    find "$OUTPUT_DIR" -name "business_metrics_*.json" -mtime +$RETENTION_DAYS -type f | while read -r file; do
        rm -f "$file"
        deleted_count=$((deleted_count + 1))
        log "删除过期文件: $(basename "$file")"
    done
    
    # 清理Prometheus文件
    find "$OUTPUT_DIR" -name "prometheus_metrics_*.txt" -mtime +$RETENTION_DAYS -type f | while read -r file; do
        rm -f "$file"
        deleted_count=$((deleted_count + 1))
        log "删除过期文件: $(basename "$file")"
    done
    
    # 清理报告文件
    find "$OUTPUT_DIR" -name "metrics_report_*.html" -mtime +$RETENTION_DAYS -type f | while read -r file; do
        rm -f "$file"
        deleted_count=$((deleted_count + 1))
        log "删除过期文件: $(basename "$file")"
    done
    
    log "清理完成"
}

# 发送指标到外部系统
send_metrics_to_external() {
    local external_url="$1"
    local latest_metrics_file
    latest_metrics_file=$(ls -t "$OUTPUT_DIR"/business_metrics_*.json 2>/dev/null | head -1)
    
    if [ -z "$latest_metrics_file" ]; then
        log_error "未找到指标文件"
        return 1
    fi
    
    log "发送指标到外部系统: $external_url"
    
    local response
    response=$(curl -s -X POST "$external_url" \
                    -H "Content-Type: application/json" \
                    -d @"$latest_metrics_file" \
                    --max-time "$TIMEOUT" \
                    -w "%{http_code}" 2>/dev/null)
    
    if [ "$response" = "200" ]; then
        log_success "指标发送成功"
        return 0
    else
        log_error "指标发送失败，响应码: $response"
        return 1
    fi
}

# 显示统计信息
show_statistics() {
    log "指标收集统计信息:"
    
    local total_files
    total_files=$(find "$OUTPUT_DIR" -name "business_metrics_*.json" -type f | wc -l)
    log "业务指标文件数量: $total_files"
    
    local total_size
    total_size=$(du -sh "$OUTPUT_DIR" 2>/dev/null | awk '{print $1}')
    log "存储空间使用: $total_size"
    
    local oldest_file newest_file
    oldest_file=$(find "$OUTPUT_DIR" -name "business_metrics_*.json" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | head -1 | awk '{print $2}')
    newest_file=$(find "$OUTPUT_DIR" -name "business_metrics_*.json" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -1 | awk '{print $2}')
    
    if [ -n "$oldest_file" ]; then
        log "最早文件: $(basename "$oldest_file") ($(stat -c %y "$oldest_file" | cut -d' ' -f1))"
    fi
    
    if [ -n "$newest_file" ]; then
        log "最新文件: $(basename "$newest_file") ($(stat -c %y "$newest_file" | cut -d' ' -f1))"
    fi
}

# 主函数
main() {
    log "========== 开始指标收集 =========="
    
    create_output_dir
    
    local exit_code=0
    
    # 收集业务指标
    if ! collect_business_metrics; then
        exit_code=1
    fi
    
    # 收集Prometheus指标
    if [ "${COLLECT_PROMETHEUS:-false}" = "true" ]; then
        if ! collect_prometheus_metrics; then
            exit_code=1
        fi
    fi
    
    # 生成报告
    if [ "${GENERATE_REPORT:-false}" = "true" ]; then
        generate_metrics_report
    fi
    
    # 发送到外部系统
    if [ -n "${EXTERNAL_URL:-}" ]; then
        send_metrics_to_external "$EXTERNAL_URL"
    fi
    
    # 清理过期文件
    if [ "${CLEANUP:-false}" = "true" ]; then
        cleanup_old_files
    fi
    
    # 显示统计信息
    if [ "${SHOW_STATS:-false}" = "true" ]; then
        show_statistics
    fi
    
    if [ $exit_code -eq 0 ]; then
        log_success "指标收集完成"
    else
        log_error "指标收集过程中发现问题"
    fi
    
    log "========== 指标收集结束 =========="
    
    exit $exit_code
}

# 显示帮助信息
show_help() {
    cat << EOF
用户中心指标收集脚本

用法: $0 [选项]

选项:
    -h, --help              显示帮助信息
    -r, --report            生成HTML格式的指标报告
    -p, --prometheus        同时收集Prometheus格式指标
    -c, --cleanup           清理过期文件
    -s, --stats             显示统计信息
    -e, --external URL      发送指标到外部系统
    -o, --output DIR        设置输出目录 (默认: /var/log/user-center-metrics)
    -t, --timeout SECONDS   设置请求超时时间 (默认: 10秒)
    --retention DAYS        设置文件保留天数 (默认: 30天)

环境变量:
    COLLECT_PROMETHEUS      是否收集Prometheus指标 (true/false)
    GENERATE_REPORT         是否生成报告 (true/false)
    CLEANUP                 是否清理过期文件 (true/false)
    SHOW_STATS              是否显示统计信息 (true/false)
    EXTERNAL_URL            外部系统URL

示例:
    $0                      执行基本指标收集
    $0 -r                   收集指标并生成报告
    $0 -p -r -c             收集所有指标、生成报告并清理过期文件
    $0 -e http://api.com    收集指标并发送到外部系统
    $0 -s                   显示统计信息

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -r|--report)
            export GENERATE_REPORT=true
            shift
            ;;
        -p|--prometheus)
            export COLLECT_PROMETHEUS=true
            shift
            ;;
        -c|--cleanup)
            export CLEANUP=true
            shift
            ;;
        -s|--stats)
            export SHOW_STATS=true
            shift
            ;;
        -e|--external)
            EXTERNAL_URL="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        --retention)
            RETENTION_DAYS="$2"
            shift 2
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查依赖
if ! command -v curl >/dev/null 2>&1; then
    log_error "curl 命令未找到，请安装 curl"
    exit 1
fi

# 执行主函数
main "$@"
