#!/bin/bash

# 用户中心健康检查脚本
# 用途：定期检查应用健康状态并发送告警

set -e

# 配置参数
APP_NAME="用户中心"
HEALTH_URL="http://localhost:18080/actuator/health"
METRICS_URL="http://localhost:8080/api/monitoring/metrics/overview"
WEBHOOK_URL="${WEBHOOK_URL:-}"
EMAIL_TO="${EMAIL_TO:-<EMAIL>}"
LOG_FILE="/var/log/user-center-health.log"
TIMEOUT=10

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}$(date '+%Y-%m-%d %H:%M:%S') - ERROR: $1${NC}" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}$(date '+%Y-%m-%d %H:%M:%S') - SUCCESS: $1${NC}" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}$(date '+%Y-%m-%d %H:%M:%S') - WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

# 发送告警函数
send_alert() {
    local message="$1"
    local severity="$2"
    
    # 发送Webhook告警
    if [ -n "$WEBHOOK_URL" ]; then
        curl -s -X POST "$WEBHOOK_URL" \
             -H 'Content-Type: application/json' \
             -d "{
                 \"text\": \"$message\",
                 \"severity\": \"$severity\",
                 \"service\": \"user-center\",
                 \"timestamp\": \"$(date -Iseconds)\"
             }" || log_error "Webhook发送失败"
    fi
    
    # 发送邮件告警（需要配置sendmail或类似工具）
    if command -v mail >/dev/null 2>&1; then
        echo "$message" | mail -s "[$severity] $APP_NAME 健康检查告警" "$EMAIL_TO"
    fi
}

# 检查HTTP响应
check_http_response() {
    local url="$1"
    local expected_status="$2"
    local timeout="$3"
    
    local response
    response=$(curl -s -o /dev/null -w "%{http_code}" --max-time "$timeout" "$url" 2>/dev/null)
    
    if [ "$response" = "$expected_status" ]; then
        return 0
    else
        return 1
    fi
}

# 检查应用健康状态
check_application_health() {
    log "开始检查应用健康状态..."
    
    if check_http_response "$HEALTH_URL" "200" "$TIMEOUT"; then
        log_success "应用健康检查通过"
        
        # 获取详细健康信息
        local health_details
        health_details=$(curl -s --max-time "$TIMEOUT" "$HEALTH_URL" 2>/dev/null)
        
        if [ $? -eq 0 ]; then
            # 检查各组件状态
            local db_status redis_status
            db_status=$(echo "$health_details" | jq -r '.components.database.status // "UNKNOWN"' 2>/dev/null)
            redis_status=$(echo "$health_details" | jq -r '.components.redis.status // "UNKNOWN"' 2>/dev/null)
            
            if [ "$db_status" != "UP" ]; then
                log_error "数据库健康检查失败: $db_status"
                send_alert "数据库健康检查失败，状态: $db_status" "critical"
                return 1
            fi
            
            if [ "$redis_status" != "UP" ]; then
                log_error "Redis健康检查失败: $redis_status"
                send_alert "Redis健康检查失败，状态: $redis_status" "critical"
                return 1
            fi
            
            log_success "所有组件健康检查通过"
        else
            log_warning "无法获取详细健康信息"
        fi
        
        return 0
    else
        log_error "应用健康检查失败，HTTP状态码: $response"
        send_alert "应用健康检查失败，HTTP状态码: $response" "critical"
        return 1
    fi
}

# 检查业务指标
check_business_metrics() {
    log "开始检查业务指标..."
    
    if check_http_response "$METRICS_URL" "200" "$TIMEOUT"; then
        local metrics_data
        metrics_data=$(curl -s --max-time "$TIMEOUT" "$METRICS_URL" 2>/dev/null)
        
        if [ $? -eq 0 ]; then
            # 检查关键指标
            local cache_hit_rate pending_ops active_sessions
            cache_hit_rate=$(echo "$metrics_data" | jq -r '.data.performance_metrics.cache_hit_rate // 0' 2>/dev/null)
            pending_ops=$(echo "$metrics_data" | jq -r '.data.performance_metrics.pending_operations // 0' 2>/dev/null)
            active_sessions=$(echo "$metrics_data" | jq -r '.data.performance_metrics.active_sessions // 0' 2>/dev/null)
            
            # 检查缓存命中率
            if [ "$(echo "$cache_hit_rate < 80" | bc -l 2>/dev/null)" = "1" ]; then
                log_warning "缓存命中率过低: ${cache_hit_rate}%"
                send_alert "缓存命中率过低: ${cache_hit_rate}%" "warning"
            fi
            
            # 检查待处理操作数
            if [ "$(echo "$pending_ops > 100" | bc -l 2>/dev/null)" = "1" ]; then
                log_warning "待处理操作过多: $pending_ops"
                send_alert "待处理操作过多: $pending_ops" "warning"
            fi
            
            # 检查活跃会话数
            if [ "$(echo "$active_sessions > 1000" | bc -l 2>/dev/null)" = "1" ]; then
                log_warning "活跃会话数过多: $active_sessions"
                send_alert "活跃会话数过多: $active_sessions" "warning"
            fi
            
            log_success "业务指标检查完成"
            log "缓存命中率: ${cache_hit_rate}%, 待处理操作: $pending_ops, 活跃会话: $active_sessions"
        else
            log_error "无法获取业务指标数据"
            return 1
        fi
        
        return 0
    else
        log_error "业务指标接口检查失败"
        send_alert "业务指标接口不可用" "warning"
        return 1
    fi
}

# 检查系统资源
check_system_resources() {
    log "开始检查系统资源..."
    
    # 检查磁盘空间
    local disk_usage
    disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$disk_usage" -gt 90 ]; then
        log_error "磁盘空间不足: ${disk_usage}%"
        send_alert "磁盘空间不足: ${disk_usage}%" "critical"
    elif [ "$disk_usage" -gt 80 ]; then
        log_warning "磁盘空间告警: ${disk_usage}%"
        send_alert "磁盘空间告警: ${disk_usage}%" "warning"
    fi
    
    # 检查内存使用率
    local mem_usage
    mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ "$mem_usage" -gt 90 ]; then
        log_error "内存使用率过高: ${mem_usage}%"
        send_alert "内存使用率过高: ${mem_usage}%" "critical"
    elif [ "$mem_usage" -gt 80 ]; then
        log_warning "内存使用率告警: ${mem_usage}%"
        send_alert "内存使用率告警: ${mem_usage}%" "warning"
    fi
    
    # 检查CPU负载
    local cpu_load
    cpu_load=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores
    cpu_cores=$(nproc)
    
    if [ "$(echo "$cpu_load > $cpu_cores * 2" | bc -l 2>/dev/null)" = "1" ]; then
        log_error "CPU负载过高: $cpu_load (核心数: $cpu_cores)"
        send_alert "CPU负载过高: $cpu_load (核心数: $cpu_cores)" "critical"
    elif [ "$(echo "$cpu_load > $cpu_cores * 1.5" | bc -l 2>/dev/null)" = "1" ]; then
        log_warning "CPU负载告警: $cpu_load (核心数: $cpu_cores)"
        send_alert "CPU负载告警: $cpu_load (核心数: $cpu_cores)" "warning"
    fi
    
    log "系统资源检查完成 - 磁盘: ${disk_usage}%, 内存: ${mem_usage}%, CPU负载: $cpu_load"
}

# 生成健康报告
generate_health_report() {
    local report_file="/tmp/user-center-health-report-$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "========================================"
        echo "$APP_NAME 健康检查报告"
        echo "检查时间: $(date)"
        echo "========================================"
        echo
        
        echo "1. 应用健康状态:"
        curl -s --max-time "$TIMEOUT" "$HEALTH_URL" | jq '.' 2>/dev/null || echo "无法获取健康状态"
        echo
        
        echo "2. 业务指标概览:"
        curl -s --max-time "$TIMEOUT" "$METRICS_URL" | jq '.data' 2>/dev/null || echo "无法获取业务指标"
        echo
        
        echo "3. 系统资源状态:"
        echo "磁盘使用率: $(df / | awk 'NR==2 {print $5}')"
        echo "内存使用率: $(free | awk 'NR==2{printf "%.1f%%", $3*100/$2}')"
        echo "CPU负载: $(uptime | awk -F'load average:' '{print $2}')"
        echo "运行时间: $(uptime | awk '{print $3,$4}' | sed 's/,//')"
        echo
        
        echo "========================================"
    } > "$report_file"
    
    log "健康报告已生成: $report_file"
    echo "$report_file"
}

# 主函数
main() {
    log "========== 开始健康检查 =========="
    
    local exit_code=0
    
    # 检查应用健康状态
    if ! check_application_health; then
        exit_code=1
    fi
    
    # 检查业务指标
    if ! check_business_metrics; then
        exit_code=1
    fi
    
    # 检查系统资源
    check_system_resources
    
    # 生成报告
    if [ "${GENERATE_REPORT:-false}" = "true" ]; then
        generate_health_report
    fi
    
    if [ $exit_code -eq 0 ]; then
        log_success "所有健康检查通过"
    else
        log_error "健康检查发现问题"
    fi
    
    log "========== 健康检查完成 =========="
    
    exit $exit_code
}

# 显示帮助信息
show_help() {
    cat << EOF
用户中心健康检查脚本

用法: $0 [选项]

选项:
    -h, --help              显示帮助信息
    -r, --report            生成详细健康报告
    -w, --webhook URL       设置Webhook告警URL
    -e, --email EMAIL       设置邮件告警地址
    -t, --timeout SECONDS   设置请求超时时间 (默认: 10秒)

环境变量:
    WEBHOOK_URL             Webhook告警URL
    EMAIL_TO                邮件告警地址
    GENERATE_REPORT         是否生成报告 (true/false)

示例:
    $0                      执行基本健康检查
    $0 -r                   执行检查并生成报告
    $0 -w http://webhook    设置Webhook并执行检查
    $0 -e <EMAIL>    设置邮件地址并执行检查

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -r|--report)
            export GENERATE_REPORT=true
            shift
            ;;
        -w|--webhook)
            WEBHOOK_URL="$2"
            shift 2
            ;;
        -e|--email)
            EMAIL_TO="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查依赖
if ! command -v curl >/dev/null 2>&1; then
    log_error "curl 命令未找到，请安装 curl"
    exit 1
fi

if ! command -v jq >/dev/null 2>&1; then
    log_warning "jq 命令未找到，JSON解析功能将受限"
fi

if ! command -v bc >/dev/null 2>&1; then
    log_warning "bc 命令未找到，数值比较功能将受限"
fi

# 创建日志目录
mkdir -p "$(dirname "$LOG_FILE")"

# 执行主函数
main "$@"
