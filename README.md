# User Center Service: Comprehensive User Profile and Authentication Management

A Spring Boot-based user center service that provides centralized user management, authentication, and profile management capabilities with support for real-name verification, multi-dimensional user profiles, and secure OAuth2 client integration.

The service offers a comprehensive solution for managing user identities and profiles in enterprise applications. It provides robust user authentication mechanisms, detailed profile management including education, work experience, and contact information, along with real-name verification capabilities. Built with Spring Boot 3.2.0 and integrated with Nacos for service discovery and configuration management, the service ensures scalability and maintainability.

Key features include:
- Multi-factor authentication support with customizable identity types
- Comprehensive user profile management including personal, educational, and professional information
- Real-name verification with account merging capabilities
- OAuth2 client integration for secure third-party access
- Audit logging for user operations
- Configurable async operations with thread pool management

## Repository Structure
```
├── src/main/java/com/tinyzk/user/center/     # Core application code
│   ├── common/                               # Common utilities and configurations
│   │   ├── annotation/                       # Custom annotations for features like audit logging
│   │   ├── aop/                             # Aspect-oriented programming implementations
│   │   ├── enums/                           # Enumeration classes for various statuses
│   │   ├── event/                           # Event handling for user operations
│   │   ├── exception/                       # Custom exception handling
│   │   └── filter/                          # Request filters including client identification
│   ├── config/                              # Application configurations
│   │   ├── AsyncConfig.java                 # Async task executor configuration
│   │   ├── SecurityConfig.java              # Spring Security configuration
│   │   └── RedisConfig.java                 # Redis connection configuration
│   ├── controller/                          # REST API endpoints
│   ├── service/                             # Business logic implementation
│   ├── mapper/                              # MyBatis Plus data access layer
│   └── entity/                              # Domain model classes
├── src/main/resources/                      # Application resources
│   ├── application.yml                      # Main application configuration
│   ├── application-*.yml                    # Environment-specific configurations
│   └── mapper/                              # MyBatis mapper XML files
└── Dockerfile                               # Container configuration
```

## Usage Instructions
### Prerequisites
- JDK 17 or higher
- MySQL 8.0+
- Redis
- Maven 3.6+
- Nacos service discovery and configuration center

### Installation

#### Local Development Setup
1. Clone the repository:
```bash
git clone <repository-url>
cd user-center
```

2. Configure application properties:
```bash
cp src/main/resources/application-local.yml.example src/main/resources/application-local.yml
# Edit application-local.yml with your local configuration
```

3. Build the project:
```bash
./mvnw clean package -DskipTests
```

4. Run the application:
```bash
./mvnw spring-boot:run -Dspring.profiles.active=local
```

#### Docker Deployment
1. Build the Docker image:
```bash
docker build -t user-center:latest .
```

2. Run the container:
```bash
docker run -d \
  -p 8080:8080 \
  -e APP_PROFILE=prod \
  -e JAVA_OPTS="-Xms512M -Xmx512M" \
  user-center:latest
```

### Quick Start
1. Register a new user:
```bash
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "identityType": "EMAIL",
    "identifier": "<EMAIL>",
    "credential": "password123",
    "nickname": "John Doe"
  }'
```

2. Login:
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "identityType": "EMAIL",
    "identifier": "<EMAIL>",
    "credential": "password123"
  }'
```

### More Detailed Examples
1. Update user profile:
```bash
curl -X PUT http://localhost:8080/api/v1/users/{userId}/profile \
  -H "Content-Type: application/json" \
  -d '{
    "nickname": "John Doe",
    "gender": 1,
    "birthday": "1990-01-01",
    "bio": "Software Engineer"
  }'
```

2. Add work experience:
```bash
curl -X POST http://localhost:8080/api/v1/users/{userId}/works \
  -H "Content-Type: application/json" \
  -d '{
    "companyName": "Tech Corp",
    "position": "Senior Developer",
    "startDate": "2020-01-01",
    "endDate": "2023-12-31",
    "description": "Full-stack development"
  }'
```

### Troubleshooting
1. Connection Issues
- Check Nacos connectivity:
```bash
curl http://<nacos-host>:8848/nacos/v1/ns/instance/list?serviceName=user-center
```
- Verify Redis connection:
```bash
redis-cli ping
```

2. Common Errors
- `UserNotFoundException`: Verify user ID and authentication status
- `InvalidCredentialsException`: Check provided credentials
- `ClientAuthenticationException`: Verify OAuth client configuration

## Data Flow
The service implements a layered architecture for processing user-related operations.

```ascii
Client Request → Controller → Service Layer → Data Access Layer → Database
     ↑              ↓            ↓               ↓                 ↓
     └──────────────┴────────────┴───────────────┴─────────────────┘
                         Response Flow
```

Key component interactions:
1. Controllers handle HTTP requests and input validation
2. Services implement business logic and transaction management
3. Mappers handle data persistence using MyBatis Plus
4. Events system manages asynchronous operations
5. AOP handles cross-cutting concerns like audit logging

## Infrastructure

![Infrastructure diagram](./docs/infra.svg)
### Resources
- Lambda Functions:
  - `UserAuditEventHandler`: Processes user audit events
  - `UserNotificationHandler`: Handles user notifications

### Database
- RDS MySQL instances for user data storage
- Redis clusters for caching and session management

### Monitoring
- Prometheus metrics endpoint at `/actuator/prometheus`
- Health check endpoint at `/actuator/health`

## Deployment
1. Prerequisites:
- Docker registry access
- Nacos configuration center setup
- Database migrations applied

2. Deployment Steps:
```bash
# Build application
./mvnw clean package -DskipTests

# Build and push Docker image
docker build -t user-center:${VERSION} .
docker push user-center:${VERSION}

# Deploy to environment
kubectl apply -f k8s/
```