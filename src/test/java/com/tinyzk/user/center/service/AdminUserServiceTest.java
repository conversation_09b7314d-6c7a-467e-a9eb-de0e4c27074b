package com.tinyzk.user.center.service;

import com.tinyzk.user.center.entity.UserBase;
import com.tinyzk.user.center.mapper.AdminUserMapper;
import com.tinyzk.user.center.service.impl.AdminUserServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * AdminUserService 单元测试
 */
@ExtendWith(MockitoExtension.class)
public class AdminUserServiceTest {

    @Mock
    private AdminUserMapper adminUserMapper;

    @InjectMocks
    private AdminUserServiceImpl adminUserService;

    private UserBase testUser;

    @BeforeEach
    void setUp() {
        // 初始化测试用户
        testUser = new UserBase();
        testUser.setUserId(1L);
        testUser.setStatus(1); // 默认启用状态
    }

    @Test
    void disableUser_WhenUserExists_ShouldDisableUser() {
        // 模拟用户存在
        when(adminUserMapper.selectById(anyLong())).thenReturn(testUser);
        when(adminUserMapper.updateUserStatus(anyLong(), anyInt())).thenReturn(1);

        // 执行禁用操作
        boolean result = adminUserService.disableUser(1L, "测试禁用原因");

        // 验证结果
        assertTrue(result);
        verify(adminUserMapper).selectById(1L);
        verify(adminUserMapper).updateUserStatus(1L, 2); // 2 表示禁用状态
    }

    @Test
    void disableUser_WhenUserNotExists_ShouldReturnFalse() {
        // 模拟用户不存在
        when(adminUserMapper.selectById(anyLong())).thenReturn(null);

        // 执行禁用操作
        boolean result = adminUserService.disableUser(999L, "测试禁用原因");

        // 验证结果
        assertFalse(result);
        verify(adminUserMapper).selectById(999L);
        verify(adminUserMapper, never()).updateUserStatus(anyLong(), anyInt());
    }

    @Test
    void disableUser_WhenUserAlreadyDisabled_ShouldReturnTrue() {
        // 设置用户为已禁用状态
        testUser.setStatus(2);
        when(adminUserMapper.selectById(anyLong())).thenReturn(testUser);

        // 执行禁用操作
        boolean result = adminUserService.disableUser(1L, "测试禁用原因");

        // 验证结果
        assertTrue(result);
        verify(adminUserMapper).selectById(1L);
        verify(adminUserMapper, never()).updateUserStatus(anyLong(), anyInt());
    }

    @Test
    void enableUser_WhenUserExists_ShouldEnableUser() {
        // 设置用户为禁用状态
        testUser.setStatus(2);
        when(adminUserMapper.selectById(anyLong())).thenReturn(testUser);
        when(adminUserMapper.updateUserStatus(anyLong(), anyInt())).thenReturn(1);

        // 执行启用操作
        boolean result = adminUserService.enableUser(1L);

        // 验证结果
        assertTrue(result);
        verify(adminUserMapper).selectById(1L);
        verify(adminUserMapper).updateUserStatus(1L, 1); // 1 表示启用状态
    }

    @Test
    void enableUser_WhenUserNotExists_ShouldReturnFalse() {
        // 模拟用户不存在
        when(adminUserMapper.selectById(anyLong())).thenReturn(null);

        // 执行启用操作
        boolean result = adminUserService.enableUser(999L);

        // 验证结果
        assertFalse(result);
        verify(adminUserMapper).selectById(999L);
        verify(adminUserMapper, never()).updateUserStatus(anyLong(), anyInt());
    }

    @Test
    void enableUser_WhenUserAlreadyEnabled_ShouldReturnTrue() {
        // 用户已经是启用状态
        when(adminUserMapper.selectById(anyLong())).thenReturn(testUser);

        // 执行启用操作
        boolean result = adminUserService.enableUser(1L);

        // 验证结果
        assertTrue(result);
        verify(adminUserMapper).selectById(1L);
        verify(adminUserMapper, never()).updateUserStatus(anyLong(), anyInt());
    }
}
