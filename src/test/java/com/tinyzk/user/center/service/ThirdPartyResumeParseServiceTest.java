package com.tinyzk.user.center.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tinyzk.user.center.common.exception.BusinessException;
import com.tinyzk.user.center.common.exception.FileUploadException;
import com.tinyzk.user.center.common.exception.ResumeParseException;
import com.tinyzk.user.center.config.ResumeParseConfig;
import com.tinyzk.user.center.dto.ThirdPartyParseResultDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 第三方简历解析服务测试
 */
@ExtendWith(MockitoExtension.class)
class ThirdPartyResumeParseServiceTest {

    @Mock
    private ResumeParseConfig config;

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private ThirdPartyResumeParseService thirdPartyService;

    private MockMultipartFile validFile;
    private MockMultipartFile invalidFile;
    private MockMultipartFile oversizedFile;

    @BeforeEach
    void setUp() {
        // 配置mock
        when(config.getMaxFileSize()).thenReturn(10 * 1024 * 1024L); // 10MB
        when(config.getSupportedFileTypes()).thenReturn(new String[]{"doc", "docx", "pdf"});
        when(config.getApiUrl()).thenReturn("http://test-api.com/parse");
        when(config.getMaxRetries()).thenReturn(3);

        // 创建测试文件
        validFile = new MockMultipartFile(
                "file",
                "test-resume.pdf",
                "application/pdf",
                "test content".getBytes()
        );

        invalidFile = new MockMultipartFile(
                "file",
                "test-resume.txt",
                "text/plain",
                "test content".getBytes()
        );

        // 创建超大文件
        byte[] largeContent = new byte[11 * 1024 * 1024]; // 11MB
        oversizedFile = new MockMultipartFile(
                "file",
                "large-resume.pdf",
                "application/pdf",
                largeContent
        );
    }

    @Test
    void testParseResume_Success() throws Exception {
        // Given
        String responseJson = createSuccessResponseJson();
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseJson, HttpStatus.OK);
        
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class)))
                .thenReturn(responseEntity);
        
        ThirdPartyParseResultDTO expectedResult = createSuccessParseResult();
        when(objectMapper.readValue(responseJson, ThirdPartyParseResultDTO.class))
                .thenReturn(expectedResult);

        // When
        ThirdPartyParseResultDTO result = thirdPartyService.parseResume(validFile);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getErrorCode());
        assertEquals("succeed", result.getErrorMessage());
        assertNotNull(result.getParsingResult());

        verify(restTemplate, times(1)).exchange(anyString(), any(), any(), eq(String.class));
        verify(objectMapper, times(1)).readValue(responseJson, ThirdPartyParseResultDTO.class);
    }

    @Test
    void testParseResume_FileValidationFailure_NullFile() {
        // When & Then
        assertThrows(BusinessException.class, () -> {
            thirdPartyService.parseResume(null);
        });
    }

    @Test
    void testParseResume_FileValidationFailure_EmptyFile() {
        // Given
        MockMultipartFile emptyFile = new MockMultipartFile(
                "file",
                "empty.pdf",
                "application/pdf",
                new byte[0]
        );

        // When & Then
        assertThrows(BusinessException.class, () -> {
            thirdPartyService.parseResume(emptyFile);
        });
    }

    @Test
    void testParseResume_FileValidationFailure_OversizedFile() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            thirdPartyService.parseResume(oversizedFile);
        });
        
        assertTrue(exception.getMessage().contains("文件大小超过限制"));
    }

    @Test
    void testParseResume_FileValidationFailure_UnsupportedFileType() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            thirdPartyService.parseResume(invalidFile);
        });
        
        assertTrue(exception.getMessage().contains("不支持的文件类型"));
    }

    @Test
    void testParseResume_ApiCallFailure_HttpError() throws Exception {
        // Given
        ResponseEntity<String> responseEntity = new ResponseEntity<>("Error", HttpStatus.INTERNAL_SERVER_ERROR);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class)))
                .thenReturn(responseEntity);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            thirdPartyService.parseResume(validFile);
        });
        
        assertTrue(exception.getMessage().contains("第三方服务调用失败"));
    }

    @Test
    void testParseResume_ApiCallFailure_EmptyResponse() throws Exception {
        // Given
        ResponseEntity<String> responseEntity = new ResponseEntity<>(null, HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class)))
                .thenReturn(responseEntity);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            thirdPartyService.parseResume(validFile);
        });
        
        assertTrue(exception.getMessage().contains("第三方服务返回空响应"));
    }

    @Test
    void testParseResume_ParseResultValidationFailure_ErrorCode() throws Exception {
        // Given
        String responseJson = createErrorResponseJson();
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseJson, HttpStatus.OK);
        
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class)))
                .thenReturn(responseEntity);
        
        ThirdPartyParseResultDTO errorResult = createErrorParseResult();
        when(objectMapper.readValue(responseJson, ThirdPartyParseResultDTO.class))
                .thenReturn(errorResult);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            thirdPartyService.parseResume(validFile);
        });
        
        assertTrue(exception.getMessage().contains("解析失败"));
    }

    @Test
    void testParseResumeWithRetry_Success() throws Exception {
        // Given
        String responseJson = createSuccessResponseJson();
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseJson, HttpStatus.OK);
        
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class)))
                .thenReturn(responseEntity);
        
        ThirdPartyParseResultDTO expectedResult = createSuccessParseResult();
        when(objectMapper.readValue(responseJson, ThirdPartyParseResultDTO.class))
                .thenReturn(expectedResult);

        // When
        ThirdPartyParseResultDTO result = thirdPartyService.parseResumeWithRetry(validFile);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getErrorCode());
        verify(restTemplate, times(1)).exchange(anyString(), any(), any(), eq(String.class));
    }

    @Test
    void testParseResumeWithRetry_FailureAfterRetries() throws Exception {
        // Given
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class)))
                .thenThrow(new RuntimeException("Network error"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            thirdPartyService.parseResumeWithRetry(validFile);
        });
        
        assertTrue(exception.getMessage().contains("简历解析失败，已重试 3 次"));
        verify(restTemplate, times(3)).exchange(anyString(), any(), any(), eq(String.class));
    }

    @Test
    void testParseResumeWithRetry_SuccessAfterOneRetry() throws Exception {
        // Given
        String responseJson = createSuccessResponseJson();
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseJson, HttpStatus.OK);
        
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class)))
                .thenThrow(new RuntimeException("Network error"))
                .thenReturn(responseEntity);
        
        ThirdPartyParseResultDTO expectedResult = createSuccessParseResult();
        when(objectMapper.readValue(responseJson, ThirdPartyParseResultDTO.class))
                .thenReturn(expectedResult);

        // When
        ThirdPartyParseResultDTO result = thirdPartyService.parseResumeWithRetry(validFile);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getErrorCode());
        verify(restTemplate, times(2)).exchange(anyString(), any(), any(), eq(String.class));
    }

    // Helper methods

    private String createSuccessResponseJson() {
        return """
                {
                    "errorcode": 0,
                    "errormessage": "succeed",
                    "cv_id": "test-cv-id",
                    "parsing_result": {
                        "basic_info": {
                            "name": "张三",
                            "gender": "男"
                        },
                        "contact_info": {
                            "phone_number": "13800138000",
                            "email": "<EMAIL>"
                        }
                    }
                }
                """;
    }

    private String createErrorResponseJson() {
        return """
                {
                    "errorcode": 1,
                    "errormessage": "解析失败",
                    "cv_id": null,
                    "parsing_result": null
                }
                """;
    }

    private ThirdPartyParseResultDTO createSuccessParseResult() {
        ThirdPartyParseResultDTO result = new ThirdPartyParseResultDTO();
        result.setErrorCode(0);
        result.setErrorMessage("succeed");
        result.setCvId("test-cv-id");

        ThirdPartyParseResultDTO.ParsingResult parsingResult = new ThirdPartyParseResultDTO.ParsingResult();
        
        ThirdPartyParseResultDTO.BasicInfo basicInfo = new ThirdPartyParseResultDTO.BasicInfo();
        basicInfo.setName("张三");
        basicInfo.setGender("男");
        parsingResult.setBasicInfo(basicInfo);

        ThirdPartyParseResultDTO.ContactInfo contactInfo = new ThirdPartyParseResultDTO.ContactInfo();
        contactInfo.setPhoneNumber("13800138000");
        contactInfo.setEmail("<EMAIL>");
        parsingResult.setContactInfo(contactInfo);

        result.setParsingResult(parsingResult);
        return result;
    }

    private ThirdPartyParseResultDTO createErrorParseResult() {
        ThirdPartyParseResultDTO result = new ThirdPartyParseResultDTO();
        result.setErrorCode(1);
        result.setErrorMessage("解析失败");
        result.setCvId(null);
        result.setParsingResult(null);
        return result;
    }
}
