package com.tinyzk.user.center.service;

import com.tinyzk.user.center.common.context.ClientContext;
import com.tinyzk.user.center.common.enums.IdentityType;
import com.tinyzk.user.center.dto.RegisterDTO;
import com.tinyzk.user.center.entity.OAuthClientDetails;
import com.tinyzk.user.center.entity.UserAuditLog;
import com.tinyzk.user.center.mapper.UserAuditLogMapper;
import com.tinyzk.user.center.service.impl.UserAuthServiceImpl;
import com.tinyzk.user.center.vo.RegisterVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户审计日志测试类
 * 验证审计日志中的clientId记录是否正常工作
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class UserAuditLogTest {

    @Autowired
    private UserAuthService userAuthService;
    
    @Autowired
    private UserAuditLogMapper userAuditLogMapper;
    
    private static final String TEST_CLIENT_ID = "test-client-001";
    
    @BeforeEach
    void setUp() {
        // 设置测试客户端上下文
        ClientContext clientContext = new ClientContext();
        clientContext.setClientId(TEST_CLIENT_ID);
        clientContext.setAnonymous(false);
        
        // 模拟客户端详情
        OAuthClientDetails clientDetails = new OAuthClientDetails();
        clientDetails.setClientId(TEST_CLIENT_ID);
        clientDetails.setClientName("测试客户端");
        clientDetails.setStatus(1);
        clientContext.setClientDetails(clientDetails);
        
        ClientContext.setCurrentContext(clientContext);
    }
    
    @Test
    void testRegisterAuditLogWithClientId() {
        // 准备注册数据
        RegisterDTO registerDTO = new RegisterDTO();
        registerDTO.setIdentityType(IdentityType.PHONE);
        registerDTO.setIdentifier("13800138999");
        registerDTO.setCredential("password123");
        registerDTO.setNickname("测试用户");
        
        // 执行注册
        RegisterVO result = userAuthService.register(registerDTO);
        
        // 验证注册结果
        assertNotNull(result);
        assertNotNull(result.getUserId());
        assertEquals("测试用户", result.getNickname());
        
        // 等待异步处理完成
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 查询审计日志
        UserAuditLog auditLog = userAuditLogMapper.selectById(result.getUserId());
        if (auditLog != null) {
            // 验证审计日志包含客户端ID
            assertEquals(TEST_CLIENT_ID, auditLog.getClientId());
            assertEquals("REGISTER_SUCCESS", auditLog.getOperationType());
            assertEquals(result.getUserId(), auditLog.getUserId());
            
            System.out.println("✅ 审计日志验证成功:");
            System.out.println("   用户ID: " + auditLog.getUserId());
            System.out.println("   操作类型: " + auditLog.getOperationType());
            System.out.println("   客户端ID: " + auditLog.getClientId());
            System.out.println("   创建时间: " + auditLog.getCreatedAt());
        } else {
            fail("未找到对应的审计日志记录");
        }
    }
    
    @Test
    void testAnonymousClientAuditLog() {
        // 设置匿名客户端上下文
        ClientContext anonymousContext = new ClientContext();
        anonymousContext.setClientId("anonymous");
        anonymousContext.setAnonymous(true);
        anonymousContext.setClientDetails(null);
        ClientContext.setCurrentContext(anonymousContext);
        
        // 准备注册数据
        RegisterDTO registerDTO = new RegisterDTO();
        registerDTO.setIdentityType(IdentityType.PHONE);
        registerDTO.setIdentifier("13800138888");
        registerDTO.setCredential("password123");
        
        try {
            // 执行注册（应该失败）
            userAuthService.register(registerDTO);
            fail("匿名客户端注册应该失败");
        } catch (Exception e) {
            // 验证异常消息
            assertTrue(e.getMessage().contains("客户端身份"));
            System.out.println("✅ 匿名客户端验证正常: " + e.getMessage());
        }
    }
} 