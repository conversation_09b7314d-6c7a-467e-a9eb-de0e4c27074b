package com.tinyzk.user.center.service;

import com.tinyzk.user.center.dto.ThirdPartyParseResultDTO;
import com.tinyzk.user.center.entity.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简历数据转换服务测试
 */
@ExtendWith(MockitoExtension.class)
class ResumeDataConversionServiceTest {

    @InjectMocks
    private ResumeDataConversionService conversionService;

    private Long userId;

    @BeforeEach
    void setUp() {
        userId = 1L;
    }

    @Test
    void testConvertBasicInfo() {
        // Given
        ThirdPartyParseResultDTO.BasicInfo basicInfo = new ThirdPartyParseResultDTO.BasicInfo();
        basicInfo.setName("张三");
        basicInfo.setGender("男");
        basicInfo.setAge(30);
        basicInfo.setDateOfBirth("1993-05-15");
        basicInfo.setCurrentLocation("北京市");
        basicInfo.setDetailedLocation("北京市朝阳区");
        basicInfo.setDesiredPosition("Java开发工程师");
        basicInfo.setDesiredIndustry("互联网");

        // When
        UserProfile result = conversionService.convertBasicInfo(userId, basicInfo);

        // Then
        assertNotNull(result);
        assertEquals(userId, result.getUserId());
        assertEquals("张三", result.getNickname());
        assertEquals(Integer.valueOf(1), result.getGender()); // 男
        assertEquals(LocalDate.of(1993, 5, 15), result.getBirthday());
        assertEquals("北京市", result.getRegionName());
        assertEquals("北京市朝阳区", result.getAddress());
        assertTrue(result.getBio().contains("期望职位: Java开发工程师"));
        assertTrue(result.getBio().contains("期望行业: 互联网"));
    }

    @Test
    void testConvertContactInfo() {
        // Given
        ThirdPartyParseResultDTO.ContactInfo contactInfo = new ThirdPartyParseResultDTO.ContactInfo();
        contactInfo.setPhoneNumber("13800138000");
        contactInfo.setEmail("<EMAIL>");
        contactInfo.setWechat("zhangsan_wx");
        contactInfo.setQq("123456789");

        // When
        List<UserContactMethods> results = conversionService.convertContactInfo(userId, contactInfo);

        // Then
        assertNotNull(results);
        assertEquals(4, results.size());

        // 验证手机号
        UserContactMethods phone = results.stream()
                .filter(c -> c.getContactType() == 2 && "手机号".equals(c.getLabel()))
                .findFirst().orElse(null);
        assertNotNull(phone);
        assertEquals("13800138000", phone.getContactValue());

        // 验证邮箱
        UserContactMethods email = results.stream()
                .filter(c -> c.getContactType() == 1 && "邮箱".equals(c.getLabel()))
                .findFirst().orElse(null);
        assertNotNull(email);
        assertEquals("<EMAIL>", email.getContactValue());

        // 验证微信
        UserContactMethods wechat = results.stream()
                .filter(c -> c.getContactType() == 4 && "微信".equals(c.getLabel()))
                .findFirst().orElse(null);
        assertNotNull(wechat);
        assertEquals("zhangsan_wx", wechat.getContactValue());

        // 验证QQ
        UserContactMethods qq = results.stream()
                .filter(c -> c.getContactType() == 5 && "QQ".equals(c.getLabel()))
                .findFirst().orElse(null);
        assertNotNull(qq);
        assertEquals("123456789", qq.getContactValue());
    }

    @Test
    void testConvertEducationExperience() {
        // Given
        ThirdPartyParseResultDTO.EducationExperience education = new ThirdPartyParseResultDTO.EducationExperience();
        education.setSchoolName("清华大学");
        education.setDegree("本科");
        education.setMajor("计算机科学与技术");
        education.setStartTimeYear("2011");
        education.setStartTimeMonth("09");
        education.setEndTimeYear("2015");
        education.setEndTimeMonth("06");
        education.setGpa("3.8");

        List<ThirdPartyParseResultDTO.EducationExperience> educationList = Arrays.asList(education);

        // When
        List<UserEducationHistory> results = conversionService.convertEducationExperience(userId, educationList);

        // Then
        assertNotNull(results);
        assertEquals(1, results.size());

        UserEducationHistory result = results.get(0);
        assertEquals(userId, result.getUserId());
        assertEquals("清华大学", result.getSchoolName());
        assertEquals("本科", result.getDegree());
        assertEquals("计算机科学与技术", result.getMajor());
        assertEquals(Integer.valueOf(2), result.getDegreeLevel()); // 本科
        assertEquals(LocalDate.of(2011, 9, 1), result.getStartDate());
        assertEquals(LocalDate.of(2015, 6, 1), result.getEndDate());
        assertEquals(Float.valueOf(3.8f), result.getMajorGpa());
    }

    @Test
    void testConvertWorkExperience() {
        // Given
        ThirdPartyParseResultDTO.WorkExperience work = new ThirdPartyParseResultDTO.WorkExperience();
        work.setCompanyName("阿里巴巴");
        work.setJobTitle("高级Java开发工程师");
        work.setDepartment("技术部");
        work.setStartTimeYear("2018");
        work.setStartTimeMonth("03");
        work.setEndTimeYear("2023");
        work.setEndTimeMonth("12");
        work.setDescription("负责电商平台后端开发");
        work.setIndustry("互联网");
        work.setLocation("杭州");
        work.setCompanySize("1000-9999人");
        work.setSalary("15000~25000元/月");

        List<ThirdPartyParseResultDTO.WorkExperience> workList = Arrays.asList(work);

        // When
        List<UserWorkHistory> results = conversionService.convertWorkExperience(userId, workList);

        // Then
        assertNotNull(results);
        assertEquals(1, results.size());

        UserWorkHistory result = results.get(0);
        assertEquals(userId, result.getUserId());
        assertEquals("阿里巴巴", result.getCompanyName());
        assertEquals("高级Java开发工程师", result.getPositionName());
        assertEquals("技术部", result.getDepartment());
        assertEquals("负责电商平台后端开发", result.getDescription());
        assertEquals(LocalDate.of(2018, 3, 1), result.getStartDate());
        assertEquals(LocalDate.of(2023, 12, 1), result.getEndDate());
        assertEquals("互联网", result.getCompanyIndustry());
        assertEquals("杭州", result.getCompanyLocation());
        assertNotNull(result.getCompanySize());
        assertNotNull(result.getSalaryMin());
        assertNotNull(result.getSalaryMax());
    }

    @Test
    void testConvertProjectExperience() {
        // Given
        ThirdPartyParseResultDTO.ProjectExperience project = new ThirdPartyParseResultDTO.ProjectExperience();
        project.setProjectName("电商平台重构");
        project.setCompanyName("阿里巴巴");
        project.setJobTitle("技术负责人");
        project.setStartTimeYear("2020");
        project.setStartTimeMonth("01");
        project.setEndTimeYear("2021");
        project.setEndTimeMonth("12");
        project.setDescription("负责电商平台的架构重构和性能优化");

        List<ThirdPartyParseResultDTO.ProjectExperience> projectList = Arrays.asList(project);

        // When
        List<UserProjectHistory> results = conversionService.convertProjectExperience(userId, projectList);

        // Then
        assertNotNull(results);
        assertEquals(1, results.size());

        UserProjectHistory result = results.get(0);
        assertEquals(userId, result.getUserId());
        assertEquals("电商平台重构", result.getProjectName());
        assertEquals("技术负责人", result.getRole());
        assertEquals("负责电商平台的架构重构和性能优化", result.getDescription());
        assertEquals("阿里巴巴", result.getAssociatedOrganization());
        assertEquals(LocalDate.of(2020, 1, 1), result.getStartDate());
        assertEquals(LocalDate.of(2021, 12, 1), result.getEndDate());
    }

    @Test
    void testConvertSkills() {
        // Given
        ThirdPartyParseResultDTO.Others others = new ThirdPartyParseResultDTO.Others();
        others.setItSkills(Arrays.asList("Java", "Spring Boot", "MySQL"));
        others.setBusinessSkills(Arrays.asList("项目管理", "团队协作"));
        others.setSkills(Arrays.asList("沟通能力", "Java")); // Java重复，应该被过滤

        // When
        List<UserSkills> results = conversionService.convertSkills(userId, others);

        // Then
        assertNotNull(results);
        assertEquals(6, results.size()); // 3个IT技能 + 2个业务技能 + 1个其他技能（沟通能力），重复的Java被过滤

        // 验证IT技能
        long itSkillsCount = results.stream()
                .filter(s -> s.getSkillType() == 1)
                .count();
        assertEquals(3, itSkillsCount);

        // 验证业务技能
        long businessSkillsCount = results.stream()
                .filter(s -> s.getSkillType() == 2)
                .count();
        assertEquals(2, businessSkillsCount);

        // 验证数据来源
        assertTrue(results.stream().allMatch(s -> "resume".equals(s.getSource())));
        assertTrue(results.stream().allMatch(s -> "public".equals(s.getVisibility())));
    }

    @Test
    void testConvertBasicInfo_NullInput() {
        // When
        UserProfile result = conversionService.convertBasicInfo(userId, null);

        // Then
        assertNull(result);
    }

    @Test
    void testConvertContactInfo_NullInput() {
        // When
        List<UserContactMethods> results = conversionService.convertContactInfo(userId, null);

        // Then
        assertNotNull(results);
        assertTrue(results.isEmpty());
    }

    @Test
    void testConvertEducationExperience_EmptyList() {
        // When
        List<UserEducationHistory> results = conversionService.convertEducationExperience(userId, Arrays.asList());

        // Then
        assertNotNull(results);
        assertTrue(results.isEmpty());
    }

    @Test
    void testConvertSkills_NullInput() {
        // When
        List<UserSkills> results = conversionService.convertSkills(userId, null);

        // Then
        assertNotNull(results);
        assertTrue(results.isEmpty());
    }
}
