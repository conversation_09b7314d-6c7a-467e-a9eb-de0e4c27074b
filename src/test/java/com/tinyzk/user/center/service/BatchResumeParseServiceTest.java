package com.tinyzk.user.center.service;

import com.tinyzk.user.center.dto.BatchResumeUploadRequestDTO;
import com.tinyzk.user.center.dto.ThirdPartyParseResultDTO;
import com.tinyzk.user.center.dto.UserDuplicationCheckDTO;
import com.tinyzk.user.center.mapper.AdminUserMapper;
import com.tinyzk.user.center.mapper.UserAuthMapper;
import com.tinyzk.user.center.mapper.UserBaseMapper;
import com.tinyzk.user.center.mapper.UserProfileMapper;
import com.tinyzk.user.center.service.impl.BatchResumeParseServiceImpl;
import com.tinyzk.user.center.vo.BatchResumeUploadResultVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 批量简历解析服务测试类
 */
@ExtendWith(MockitoExtension.class)
class BatchResumeParseServiceTest {

    @Mock
    private ThirdPartyResumeParseService thirdPartyResumeParseService;

    @Mock
    private AdminUserMapper adminUserMapper;

    @Mock
    private UserBaseMapper userBaseMapper;

    @Mock
    private UserAuthMapper userAuthMapper;

    @Mock
    private UserProfileMapper userProfileMapper;

    @InjectMocks
    private BatchResumeParseServiceImpl batchResumeParseService;

    private BatchResumeUploadRequestDTO requestDTO;
    private MultipartFile[] testFiles;

    @BeforeEach
    void setUp() {
        // 创建测试文件
        testFiles = new MultipartFile[]{
            new MockMultipartFile("file1", "resume1.pdf", "application/pdf", "test content 1".getBytes()),
            new MockMultipartFile("file2", "resume2.pdf", "application/pdf", "test content 2".getBytes())
        };

        // 创建请求DTO
        requestDTO = new BatchResumeUploadRequestDTO();
        requestDTO.setFiles(testFiles);
        requestDTO.setOverwriteExisting(false);
        requestDTO.setParseBasicInfo(true);
        requestDTO.setParseContactInfo(true);
        requestDTO.setMaxConcurrency(2);
        requestDTO.setTimeoutSeconds(30);
    }

    @Test
    void testBatchParseResumes_Success() {
        // Given
        ThirdPartyParseResultDTO parseResult = createMockParseResult();
        when(thirdPartyResumeParseService.parseResume(any(MultipartFile.class)))
            .thenReturn(parseResult);
        when(adminUserMapper.selectUserIdByPhone(anyString())).thenReturn(null);
        when(adminUserMapper.selectUserIdsByRealName(anyString())).thenReturn(null);

        // When
        BatchResumeUploadResultVO result = batchResumeParseService.batchParseResumes(requestDTO);

        // Then
        assertNotNull(result);
        assertNotNull(result.getStatistics());
        assertEquals(2, result.getStatistics().getTotalFiles());
        assertNotNull(result.getFileResults());
        assertEquals(2, result.getFileResults().size());
        
        // 验证调用次数
        verify(thirdPartyResumeParseService, times(2)).parseResume(any(MultipartFile.class));
    }

    @Test
    void testBatchParseResumes_WithDuplicateUser() {
        // Given
        ThirdPartyParseResultDTO parseResult = createMockParseResult();
        when(thirdPartyResumeParseService.parseResume(any(MultipartFile.class)))
            .thenReturn(parseResult);
        when(adminUserMapper.selectUserIdByPhone("13800138000")).thenReturn(123L);

        // When
        BatchResumeUploadResultVO result = batchResumeParseService.batchParseResumes(requestDTO);

        // Then
        assertNotNull(result);
        assertTrue(result.getStatistics().getSkippedCount() > 0);
    }

    @Test
    void testBatchParseResumes_WithOverwrite() {
        // Given
        requestDTO.setOverwriteExisting(true);
        ThirdPartyParseResultDTO parseResult = createMockParseResult();
        when(thirdPartyResumeParseService.parseResume(any(MultipartFile.class)))
            .thenReturn(parseResult);
        when(adminUserMapper.selectUserIdByPhone("13800138000")).thenReturn(123L);

        // When
        BatchResumeUploadResultVO result = batchResumeParseService.batchParseResumes(requestDTO);

        // Then
        assertNotNull(result);
        assertTrue(result.getStatistics().getUpdatedUserCount() >= 0);
    }

    @Test
    void testCheckUserDuplication_ByPhone() {
        // Given
        UserDuplicationCheckDTO checkDTO = new UserDuplicationCheckDTO();
        checkDTO.setPhone("13800138000");
        checkDTO.setRealName("张三");
        
        when(adminUserMapper.selectUserIdByPhoneAndRealName("13800138000", "张三"))
            .thenReturn(123L);

        // When
        UserDuplicationCheckDTO.CheckResult result = batchResumeParseService.checkUserDuplication(checkDTO);

        // Then
        assertNotNull(result);
        assertTrue(result.getIsDuplicate());
        assertEquals(123L, result.getDuplicateUserId());
        assertEquals(UserDuplicationCheckDTO.DuplicateMatchType.PHONE, result.getMatchType());
    }

    @Test
    void testCheckUserDuplication_NoDuplicate() {
        // Given
        UserDuplicationCheckDTO checkDTO = new UserDuplicationCheckDTO();
        checkDTO.setPhone("13800138000");
        checkDTO.setRealName("张三");
        
        when(adminUserMapper.selectUserIdByPhoneAndRealName(anyString(), anyString()))
            .thenReturn(null);
        when(adminUserMapper.selectUserIdByPhone(anyString())).thenReturn(null);
        when(adminUserMapper.selectUserIdsByRealName(anyString())).thenReturn(null);

        // When
        UserDuplicationCheckDTO.CheckResult result = batchResumeParseService.checkUserDuplication(checkDTO);

        // Then
        assertNotNull(result);
        assertFalse(result.getIsDuplicate());
        assertNull(result.getDuplicateUserId());
    }

    @Test
    void testBatchParseResumes_WithException() {
        // Given
        when(thirdPartyResumeParseService.parseResume(any(MultipartFile.class)))
            .thenThrow(new RuntimeException("API调用失败"));

        // When
        BatchResumeUploadResultVO result = batchResumeParseService.batchParseResumes(requestDTO);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getStatistics().getFailureCount());
        
        for (BatchResumeUploadResultVO.FileProcessResult fileResult : result.getFileResults()) {
            assertEquals(BatchResumeUploadResultVO.ProcessStatus.FAILURE, fileResult.getStatus());
            assertNotNull(fileResult.getErrorMessage());
        }
    }

    private ThirdPartyParseResultDTO createMockParseResult() {
        ThirdPartyParseResultDTO result = new ThirdPartyParseResultDTO();
        result.setErrorCode(0);
        result.setErrorMessage("succeed");
        result.setCvId("test-cv-id");

        ThirdPartyParseResultDTO.ParsingResult parsingResult = new ThirdPartyParseResultDTO.ParsingResult();
        
        // Basic info
        ThirdPartyParseResultDTO.BasicInfo basicInfo = new ThirdPartyParseResultDTO.BasicInfo();
        basicInfo.setName("张三");
        basicInfo.setGender("男");
        basicInfo.setAge(30);
        parsingResult.setBasicInfo(basicInfo);

        // Contact info
        ThirdPartyParseResultDTO.ContactInfo contactInfo = new ThirdPartyParseResultDTO.ContactInfo();
        contactInfo.setPhoneNumber("13800138000");
        contactInfo.setEmail("<EMAIL>");
        parsingResult.setContactInfo(contactInfo);

        result.setParsingResult(parsingResult);
        return result;
    }
}
