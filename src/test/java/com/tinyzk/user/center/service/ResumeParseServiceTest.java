package com.tinyzk.user.center.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tinyzk.user.center.dto.ResumeParseRequestDTO;
import com.tinyzk.user.center.dto.ThirdPartyParseResultDTO;
import com.tinyzk.user.center.entity.*;
import com.tinyzk.user.center.mapper.*;
import com.tinyzk.user.center.vo.ResumeParseResultVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 简历解析服务测试
 */
@ExtendWith(MockitoExtension.class)
class ResumeParseServiceTest {

    @Mock
    private ThirdPartyResumeParseService thirdPartyService;

    @Mock
    private ResumeDataConversionService conversionService;

    @Mock
    private ResumeParseRecordsMapper parseRecordsMapper;

    @Mock
    private UserProfileMapper userProfileMapper;

    @Mock
    private UserContactMethodsMapper contactMethodsMapper;

    @Mock
    private UserEducationHistoryMapper educationMapper;

    @Mock
    private UserWorkHistoryMapper workHistoryMapper;

    @Mock
    private UserProjectHistoryMapper projectHistoryMapper;

    @Mock
    private UserTrainingMapper trainingMapper;

    @Mock
    private UserSkillsMapper skillsMapper;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private ResumeParseService resumeParseService;

    private Long userId;
    private ResumeParseRequestDTO requestDTO;
    private MockMultipartFile mockFile;
    private ThirdPartyParseResultDTO mockParseResult;

    @BeforeEach
    void setUp() {
        userId = 1L;
        
        // 创建模拟文件
        mockFile = new MockMultipartFile(
                "file",
                "test-resume.pdf",
                "application/pdf",
                "test content".getBytes()
        );

        // 创建请求DTO
        requestDTO = new ResumeParseRequestDTO();
        requestDTO.setFile(mockFile);
        requestDTO.setOverwriteExisting(false);
        requestDTO.setParseBasicInfo(true);
        requestDTO.setParseContactInfo(true);
        requestDTO.setParseEducation(true);
        requestDTO.setParseWorkExperience(true);
        requestDTO.setParseProjectExperience(true);
        requestDTO.setParseSkills(true);

        // 创建模拟解析结果
        mockParseResult = createMockParseResult();
    }

    @Test
    void testParseResume_Success() throws Exception {
        // Given
        when(parseRecordsMapper.insert(any(ResumeParseRecords.class))).thenReturn(1);
        when(parseRecordsMapper.updateById(any(ResumeParseRecords.class))).thenReturn(1);
        when(thirdPartyService.parseResumeWithRetry(any())).thenReturn(mockParseResult);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        
        // Mock conversion results
        when(conversionService.convertBasicInfo(eq(userId), any())).thenReturn(createMockUserProfile());
        when(conversionService.convertContactInfo(eq(userId), any())).thenReturn(createMockContactMethods());
        when(conversionService.convertEducationExperience(eq(userId), any())).thenReturn(createMockEducations());
        when(conversionService.convertWorkExperience(eq(userId), any())).thenReturn(createMockWorkHistories());
        when(conversionService.convertProjectExperience(eq(userId), any())).thenReturn(createMockProjects());
        when(conversionService.convertSkills(eq(userId), any())).thenReturn(createMockSkills());

        // Mock mapper operations
        when(userProfileMapper.selectByUserId(userId)).thenReturn(null);
        when(userProfileMapper.insert(any(UserProfile.class))).thenReturn(1);
        when(contactMethodsMapper.selectByUserIdAndTypeAndValue(anyLong(), anyInt(), anyString())).thenReturn(null);
        when(contactMethodsMapper.insert(any(UserContactMethods.class))).thenReturn(1);
        when(educationMapper.selectByUserIdAndSchoolAndMajor(anyLong(), anyString(), anyString())).thenReturn(null);
        when(educationMapper.insert(any(UserEducationHistory.class))).thenReturn(1);
        when(workHistoryMapper.selectByUserIdAndCompanyAndPosition(anyLong(), anyString(), anyString())).thenReturn(null);
        when(workHistoryMapper.insert(any(UserWorkHistory.class))).thenReturn(1);
        when(projectHistoryMapper.selectByUserIdAndProjectName(anyLong(), anyString())).thenReturn(null);
        when(projectHistoryMapper.insert(any(UserProjectHistory.class))).thenReturn(1);
        when(skillsMapper.selectByUserIdAndSkillName(anyLong(), anyString())).thenReturn(null);
        when(skillsMapper.insert(any(UserSkills.class))).thenReturn(1);

        // When
        ResumeParseResultVO result = resumeParseService.parseResume(userId, requestDTO);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getParseStatus()); // 解析成功
        assertEquals("解析成功", result.getParseStatusDesc());
        assertNotNull(result.getStatistics());
        assertTrue(result.getStatistics().getBasicInfoUpdated());
        assertTrue(result.getStatistics().getContactMethodsAdded() > 0);
        assertTrue(result.getStatistics().getEducationRecordsAdded() > 0);
        assertTrue(result.getStatistics().getWorkExperienceAdded() > 0);
        assertTrue(result.getStatistics().getProjectExperienceAdded() > 0);
        assertTrue(result.getStatistics().getSkillsAdded() > 0);

        // Verify interactions
        verify(parseRecordsMapper, times(1)).insert(any(ResumeParseRecords.class));
        verify(parseRecordsMapper, times(2)).updateById(any(ResumeParseRecords.class));
        verify(thirdPartyService, times(1)).parseResumeWithRetry(mockFile);
        verify(userProfileMapper, times(1)).insert(any(UserProfile.class));
        verify(contactMethodsMapper, times(2)).insert(any(UserContactMethods.class)); // 手机号和邮箱
        verify(educationMapper, times(1)).insert(any(UserEducationHistory.class));
        verify(workHistoryMapper, times(1)).insert(any(UserWorkHistory.class));
        verify(projectHistoryMapper, times(1)).insert(any(UserProjectHistory.class));
        verify(skillsMapper, times(3)).insert(any(UserSkills.class)); // 3个技能
    }

    @Test
    void testParseResume_ThirdPartyServiceFailure() throws Exception {
        // Given
        when(parseRecordsMapper.insert(any(ResumeParseRecords.class))).thenReturn(1);
        when(parseRecordsMapper.updateById(any(ResumeParseRecords.class))).thenReturn(1);
        when(thirdPartyService.parseResumeWithRetry(any())).thenThrow(new RuntimeException("第三方服务异常"));

        // When & Then
        assertThrows(Exception.class, () -> {
            resumeParseService.parseResume(userId, requestDTO);
        });

        // Verify error handling
        verify(parseRecordsMapper, times(1)).insert(any(ResumeParseRecords.class));
        verify(parseRecordsMapper, times(2)).updateById(any(ResumeParseRecords.class)); // 状态更新为解析中和失败
    }

    @Test
    void testGetParseRecords() {
        // Given
        List<ResumeParseRecords> mockRecords = Arrays.asList(
                createMockParseRecord(1L, "resume1.pdf", 2),
                createMockParseRecord(2L, "resume2.doc", 2)
        );
        when(parseRecordsMapper.selectByUserId(userId)).thenReturn(mockRecords);

        // When
        List<ResumeParseResultVO> results = resumeParseService.getParseRecords(userId);

        // Then
        assertNotNull(results);
        assertEquals(2, results.size());
        assertEquals("resume1.pdf", results.get(0).getOriginalFilename());
        assertEquals("resume2.doc", results.get(1).getOriginalFilename());
        assertEquals("解析成功", results.get(0).getParseStatusDesc());
        assertEquals("解析成功", results.get(1).getParseStatusDesc());

        verify(parseRecordsMapper, times(1)).selectByUserId(userId);
    }

    @Test
    void testGetParseRecord() {
        // Given
        Long recordId = 1L;
        ResumeParseRecords mockRecord = createMockParseRecord(recordId, "resume.pdf", 2);
        when(parseRecordsMapper.selectByUserIdAndRecordId(userId, recordId)).thenReturn(mockRecord);

        // When
        ResumeParseResultVO result = resumeParseService.getParseRecord(userId, recordId);

        // Then
        assertNotNull(result);
        assertEquals(recordId, result.getRecordId());
        assertEquals("resume.pdf", result.getOriginalFilename());
        assertEquals("解析成功", result.getParseStatusDesc());

        verify(parseRecordsMapper, times(1)).selectByUserIdAndRecordId(userId, recordId);
    }

    // Helper methods for creating mock objects

    private ThirdPartyParseResultDTO createMockParseResult() {
        ThirdPartyParseResultDTO result = new ThirdPartyParseResultDTO();
        result.setErrorCode(0);
        result.setErrorMessage("succeed");
        result.setCvId("test-cv-id");

        ThirdPartyParseResultDTO.ParsingResult parsingResult = new ThirdPartyParseResultDTO.ParsingResult();
        
        // Basic info
        ThirdPartyParseResultDTO.BasicInfo basicInfo = new ThirdPartyParseResultDTO.BasicInfo();
        basicInfo.setName("张三");
        basicInfo.setGender("男");
        basicInfo.setAge(30);
        parsingResult.setBasicInfo(basicInfo);

        // Contact info
        ThirdPartyParseResultDTO.ContactInfo contactInfo = new ThirdPartyParseResultDTO.ContactInfo();
        contactInfo.setPhoneNumber("13800138000");
        contactInfo.setEmail("<EMAIL>");
        parsingResult.setContactInfo(contactInfo);

        // Education
        ThirdPartyParseResultDTO.EducationExperience education = new ThirdPartyParseResultDTO.EducationExperience();
        education.setSchoolName("清华大学");
        education.setDegree("本科");
        education.setMajor("计算机科学与技术");
        parsingResult.setEducationExperience(Arrays.asList(education));

        // Work experience
        ThirdPartyParseResultDTO.WorkExperience work = new ThirdPartyParseResultDTO.WorkExperience();
        work.setCompanyName("阿里巴巴");
        work.setJobTitle("高级工程师");
        parsingResult.setWorkExperience(Arrays.asList(work));

        // Project experience
        ThirdPartyParseResultDTO.ProjectExperience project = new ThirdPartyParseResultDTO.ProjectExperience();
        project.setProjectName("电商平台");
        project.setJobTitle("技术负责人");
        parsingResult.setProjectExperience(Arrays.asList(project));

        // Skills
        ThirdPartyParseResultDTO.Others others = new ThirdPartyParseResultDTO.Others();
        others.setItSkills(Arrays.asList("Java", "Spring", "MySQL"));
        parsingResult.setOthers(others);

        result.setParsingResult(parsingResult);
        return result;
    }

    private UserProfile createMockUserProfile() {
        UserProfile profile = new UserProfile();
        profile.setUserId(userId);
        profile.setNickname("张三");
        profile.setGender(1);
        return profile;
    }

    private List<UserContactMethods> createMockContactMethods() {
        List<UserContactMethods> contacts = new ArrayList<>();
        
        UserContactMethods phone = new UserContactMethods();
        phone.setUserId(userId);
        phone.setContactType(2);
        phone.setContactValue("13800138000");
        contacts.add(phone);

        UserContactMethods email = new UserContactMethods();
        email.setUserId(userId);
        email.setContactType(1);
        email.setContactValue("<EMAIL>");
        contacts.add(email);

        return contacts;
    }

    private List<UserEducationHistory> createMockEducations() {
        UserEducationHistory education = new UserEducationHistory();
        education.setUserId(userId);
        education.setSchoolName("清华大学");
        education.setDegree("本科");
        education.setMajor("计算机科学与技术");
        return Arrays.asList(education);
    }

    private List<UserWorkHistory> createMockWorkHistories() {
        UserWorkHistory work = new UserWorkHistory();
        work.setUserId(userId);
        work.setCompanyName("阿里巴巴");
        work.setPositionName("高级工程师");
        return Arrays.asList(work);
    }

    private List<UserProjectHistory> createMockProjects() {
        UserProjectHistory project = new UserProjectHistory();
        project.setUserId(userId);
        project.setProjectName("电商平台");
        project.setRole("技术负责人");
        return Arrays.asList(project);
    }

    private List<UserSkills> createMockSkills() {
        List<UserSkills> skills = new ArrayList<>();
        
        UserSkills skill1 = new UserSkills();
        skill1.setUserId(userId);
        skill1.setSkillName("Java");
        skill1.setSkillType(1);
        skills.add(skill1);

        UserSkills skill2 = new UserSkills();
        skill2.setUserId(userId);
        skill2.setSkillName("Spring");
        skill2.setSkillType(1);
        skills.add(skill2);

        UserSkills skill3 = new UserSkills();
        skill3.setUserId(userId);
        skill3.setSkillName("MySQL");
        skill3.setSkillType(1);
        skills.add(skill3);

        return skills;
    }

    private ResumeParseRecords createMockParseRecord(Long recordId, String filename, Integer status) {
        ResumeParseRecords record = new ResumeParseRecords();
        record.setRecordId(recordId);
        record.setUserId(userId);
        record.setOriginalFilename(filename);
        record.setFileSize(1024L);
        record.setFileType("pdf");
        record.setParseStatus(status);
        record.setParseDuration(5000);
        return record;
    }
}
