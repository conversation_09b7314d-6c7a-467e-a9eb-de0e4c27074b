package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.service.AdminUserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * AdminUserController 单元测试
 */
@ExtendWith(MockitoExtension.class)
public class AdminUserControllerTest {

    private MockMvc mockMvc;

    @Mock
    private AdminUserService adminUserService;

    @InjectMocks
    private AdminUserController adminUserController;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(adminUserController).build();
    }

    @Test
    void disableUser_WhenUserExists_ShouldReturnSuccess() throws Exception {
        // 模拟服务层返回成功
        when(adminUserService.disableUser(anyLong(), anyString())).thenReturn(true);

        // 执行请求并验证响应
        mockMvc.perform(post("/api/v1/admin/users/1/disable")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"reason\": \"违反社区规定\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"));
    }

    @Test
    void disableUser_WhenUserNotExists_ShouldReturnError() throws Exception {
        // 模拟服务层返回失败
        when(adminUserService.disableUser(anyLong(), anyString())).thenReturn(false);

        // 执行请求并验证响应
        mockMvc.perform(post("/api/v1/admin/users/999/disable")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"reason\": \"违反社区规定\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("用户不存在或禁用失败"));
    }

    @Test
    void enableUser_WhenUserExists_ShouldReturnSuccess() throws Exception {
        // 模拟服务层返回成功
        when(adminUserService.enableUser(anyLong())).thenReturn(true);

        // 执行请求并验证响应
        mockMvc.perform(post("/api/v1/admin/users/1/enable")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"));
    }

    @Test
    void enableUser_WhenUserNotExists_ShouldReturnError() throws Exception {
        // 模拟服务层返回失败
        when(adminUserService.enableUser(anyLong())).thenReturn(false);

        // 执行请求并验证响应
        mockMvc.perform(post("/api/v1/admin/users/999/enable")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("用户不存在或启用失败"));
    }
}
