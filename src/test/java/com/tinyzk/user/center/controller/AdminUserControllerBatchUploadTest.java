package com.tinyzk.user.center.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tinyzk.user.center.service.BatchResumeParseService;
import com.tinyzk.user.center.vo.BatchResumeUploadResultVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * AdminUserController批量上传功能集成测试
 */
@WebMvcTest(AdminUserController.class)
class AdminUserControllerBatchUploadTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private BatchResumeParseService batchResumeParseService;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMultipartFile testFile1;
    private MockMultipartFile testFile2;
    private BatchResumeUploadResultVO mockResult;

    @BeforeEach
    void setUp() {
        // 创建测试文件
        testFile1 = new MockMultipartFile(
                "files",
                "resume1.pdf",
                "application/pdf",
                "test resume content 1".getBytes()
        );

        testFile2 = new MockMultipartFile(
                "files",
                "resume2.pdf",
                "application/pdf",
                "test resume content 2".getBytes()
        );

        // 创建模拟结果
        mockResult = createMockBatchResult();
    }

    @Test
    void testBatchUploadResumes_Success() throws Exception {
        // Given
        when(batchResumeParseService.batchParseResumes(any())).thenReturn(mockResult);

        // When & Then
        mockMvc.perform(multipart("/api/v1/admin/users/batch-resume-upload")
                        .file(testFile1)
                        .file(testFile2)
                        .param("overwriteExisting", "false")
                        .param("parseBasicInfo", "true")
                        .param("parseContactInfo", "true")
                        .param("maxConcurrency", "3")
                        .param("timeoutSeconds", "60")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("批量简历解析完成"))
                .andExpect(jsonPath("$.data.statistics.totalFiles").value(2))
                .andExpect(jsonPath("$.data.statistics.successCount").value(2))
                .andExpect(jsonPath("$.data.fileResults").isArray())
                .andExpect(jsonPath("$.data.fileResults.length()").value(2));
    }

    @Test
    void testBatchUploadResumes_WithOverwrite() throws Exception {
        // Given
        when(batchResumeParseService.batchParseResumes(any())).thenReturn(mockResult);

        // When & Then
        mockMvc.perform(multipart("/api/v1/admin/users/batch-resume-upload")
                        .file(testFile1)
                        .param("overwriteExisting", "true")
                        .param("parseBasicInfo", "true")
                        .param("parseContactInfo", "true")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testBatchUploadResumes_NoFiles() throws Exception {
        // When & Then
        mockMvc.perform(multipart("/api/v1/admin/users/batch-resume-upload")
                        .param("overwriteExisting", "false")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testBatchUploadResumes_InvalidParameters() throws Exception {
        // When & Then
        mockMvc.perform(multipart("/api/v1/admin/users/batch-resume-upload")
                        .file(testFile1)
                        .param("maxConcurrency", "0") // 无效的并发数
                        .param("timeoutSeconds", "-1") // 无效的超时时间
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testBatchUploadResumes_ServiceException() throws Exception {
        // Given
        when(batchResumeParseService.batchParseResumes(any()))
                .thenThrow(new RuntimeException("服务异常"));

        // When & Then
        mockMvc.perform(multipart("/api/v1/admin/users/batch-resume-upload")
                        .file(testFile1)
                        .param("overwriteExisting", "false")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void testBatchUploadResumes_AllParseOptions() throws Exception {
        // Given
        when(batchResumeParseService.batchParseResumes(any())).thenReturn(mockResult);

        // When & Then
        mockMvc.perform(multipart("/api/v1/admin/users/batch-resume-upload")
                        .file(testFile1)
                        .param("overwriteExisting", "false")
                        .param("parseBasicInfo", "true")
                        .param("parseContactInfo", "true")
                        .param("parseEducation", "true")
                        .param("parseWorkExperience", "true")
                        .param("parseProjectExperience", "true")
                        .param("parseSkills", "true")
                        .param("parseTraining", "true")
                        .param("parseLanguages", "true")
                        .param("parseCertificates", "true")
                        .param("parseAwards", "true")
                        .param("maxConcurrency", "5")
                        .param("timeoutSeconds", "120")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    private BatchResumeUploadResultVO createMockBatchResult() {
        BatchResumeUploadResultVO result = new BatchResumeUploadResultVO();
        result.setStartTime(LocalDateTime.now().minusMinutes(1));
        result.setEndTime(LocalDateTime.now());
        result.setTotalDurationMs(60000L);

        // 统计信息
        BatchResumeUploadResultVO.ProcessStatistics statistics = new BatchResumeUploadResultVO.ProcessStatistics();
        statistics.setTotalFiles(2);
        statistics.setSuccessCount(2);
        statistics.setFailureCount(0);
        statistics.setSkippedCount(0);
        statistics.setNewUserCount(2);
        statistics.setUpdatedUserCount(0);
        statistics.setSuccessRate(100.0);
        result.setStatistics(statistics);

        // 文件处理结果
        List<BatchResumeUploadResultVO.FileProcessResult> fileResults = new ArrayList<>();
        
        BatchResumeUploadResultVO.FileProcessResult result1 = new BatchResumeUploadResultVO.FileProcessResult();
        result1.setFileName("resume1.pdf");
        result1.setFileSize(1024L);
        result1.setStatus(BatchResumeUploadResultVO.ProcessStatus.SUCCESS);
        result1.setUserId(1001L);
        result1.setUserOperation(BatchResumeUploadResultVO.UserOperationType.CREATED);
        result1.setProcessingTimeMs(5000L);
        
        BatchResumeUploadResultVO.UserInfoSummary userInfo1 = new BatchResumeUploadResultVO.UserInfoSummary();
        userInfo1.setName("张三");
        userInfo1.setPhone("13800138000");
        userInfo1.setEmail("<EMAIL>");
        userInfo1.setGender("男");
        userInfo1.setAge(30);
        result1.setUserInfo(userInfo1);
        
        fileResults.add(result1);

        BatchResumeUploadResultVO.FileProcessResult result2 = new BatchResumeUploadResultVO.FileProcessResult();
        result2.setFileName("resume2.pdf");
        result2.setFileSize(2048L);
        result2.setStatus(BatchResumeUploadResultVO.ProcessStatus.SUCCESS);
        result2.setUserId(1002L);
        result2.setUserOperation(BatchResumeUploadResultVO.UserOperationType.CREATED);
        result2.setProcessingTimeMs(4500L);
        
        BatchResumeUploadResultVO.UserInfoSummary userInfo2 = new BatchResumeUploadResultVO.UserInfoSummary();
        userInfo2.setName("李四");
        userInfo2.setPhone("13900139000");
        userInfo2.setEmail("<EMAIL>");
        userInfo2.setGender("女");
        userInfo2.setAge(28);
        result2.setUserInfo(userInfo2);
        
        fileResults.add(result2);

        result.setFileResults(fileResults);
        return result;
    }
}
