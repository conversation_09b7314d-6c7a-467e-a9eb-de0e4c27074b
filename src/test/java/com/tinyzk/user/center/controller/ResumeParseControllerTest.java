package com.tinyzk.user.center.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tinyzk.user.center.common.util.AuthUtil;
import com.tinyzk.user.center.dto.ResumeParseRequestDTO;
import com.tinyzk.user.center.service.ResumeParseService;
import com.tinyzk.user.center.vo.ResumeParseResultVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 简历解析控制器测试
 */
@WebMvcTest(ResumeParseController.class)
class ResumeParseControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ResumeParseService resumeParseService;

    @Autowired
    private ObjectMapper objectMapper;

    private Long userId;
    private MockMultipartFile testFile;

    @BeforeEach
    void setUp() {
        userId = 1L;
        testFile = new MockMultipartFile(
                "file",
                "test-resume.pdf",
                "application/pdf",
                "test content".getBytes()
        );

        // Mock AuthUtil
        try (var mockedStatic = mockStatic(AuthUtil.class)) {
            mockedStatic.when(AuthUtil::getCurrentUserId).thenReturn(userId);
        }
    }

    @Test
    void testParseResume_Success() throws Exception {
        // Given
        ResumeParseResultVO mockResult = createMockParseResult();
        when(resumeParseService.parseResume(eq(userId), any(ResumeParseRequestDTO.class)))
                .thenReturn(mockResult);

        // Mock AuthUtil for this test
        try (var mockedStatic = mockStatic(AuthUtil.class)) {
            mockedStatic.when(AuthUtil::getCurrentUserId).thenReturn(userId);

            // When & Then
            mockMvc.perform(multipart("/api/v1/me/resume/parse")
                            .file(testFile)
                            .param("overwriteExisting", "false")
                            .param("parseBasicInfo", "true")
                            .param("parseContactInfo", "true")
                            .param("parseEducation", "true")
                            .param("parseWorkExperience", "true")
                            .param("parseProjectExperience", "true")
                            .param("parseSkills", "true")
                            .param("parseTraining", "true")
                            .param("parseLanguages", "true")
                            .param("parseCertificates", "true")
                            .param("parseAwards", "true")
                            .contentType(MediaType.MULTIPART_FORM_DATA))
                    .andExpect(status().isCreated())
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.message").value("简历解析完成"))
                    .andExpect(jsonPath("$.data.recordId").value(1))
                    .andExpect(jsonPath("$.data.parseStatus").value(2))
                    .andExpect(jsonPath("$.data.parseStatusDesc").value("解析成功"))
                    .andExpect(jsonPath("$.data.originalFilename").value("test-resume.pdf"));
        }

        verify(resumeParseService, times(1)).parseResume(eq(userId), any(ResumeParseRequestDTO.class));
    }

    @Test
    void testParseResume_MissingFile() throws Exception {
        // When & Then
        mockMvc.perform(multipart("/api/v1/me/resume/parse")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isBadRequest());

        verify(resumeParseService, never()).parseResume(anyLong(), any(ResumeParseRequestDTO.class));
    }

    @Test
    void testParseResume_ServiceException() throws Exception {
        // Given
        when(resumeParseService.parseResume(eq(userId), any(ResumeParseRequestDTO.class)))
                .thenThrow(new RuntimeException("解析服务异常"));

        // Mock AuthUtil for this test
        try (var mockedStatic = mockStatic(AuthUtil.class)) {
            mockedStatic.when(AuthUtil::getCurrentUserId).thenReturn(userId);

            // When & Then
            mockMvc.perform(multipart("/api/v1/me/resume/parse")
                            .file(testFile)
                            .contentType(MediaType.MULTIPART_FORM_DATA))
                    .andExpect(status().isInternalServerError())
                    .andExpect(jsonPath("$.code").value(500))
                    .andExpect(jsonPath("$.message").value("系统繁忙，请稍后重试"));
        }

        verify(resumeParseService, times(1)).parseResume(eq(userId), any(ResumeParseRequestDTO.class));
    }

    @Test
    void testGetParseRecords_Success() throws Exception {
        // Given
        List<ResumeParseResultVO> mockRecords = Arrays.asList(
                createMockParseResult(),
                createMockParseResult()
        );
        when(resumeParseService.getParseRecords(userId)).thenReturn(mockRecords);

        // Mock AuthUtil for this test
        try (var mockedStatic = mockStatic(AuthUtil.class)) {
            mockedStatic.when(AuthUtil::getCurrentUserId).thenReturn(userId);

            // When & Then
            mockMvc.perform(get("/api/v1/me/resume/parse-records")
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.message").value("查询成功"))
                    .andExpect(jsonPath("$.data").isArray())
                    .andExpect(jsonPath("$.data.length()").value(2));
        }

        verify(resumeParseService, times(1)).getParseRecords(userId);
    }

    @Test
    void testGetParseRecord_Success() throws Exception {
        // Given
        Long recordId = 1L;
        ResumeParseResultVO mockRecord = createMockParseResult();
        when(resumeParseService.getParseRecord(userId, recordId)).thenReturn(mockRecord);

        // Mock AuthUtil for this test
        try (var mockedStatic = mockStatic(AuthUtil.class)) {
            mockedStatic.when(AuthUtil::getCurrentUserId).thenReturn(userId);

            // When & Then
            mockMvc.perform(get("/api/v1/me/resume/parse-records/{recordId}", recordId)
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.message").value("查询成功"))
                    .andExpect(jsonPath("$.data.recordId").value(1))
                    .andExpect(jsonPath("$.data.parseStatus").value(2));
        }

        verify(resumeParseService, times(1)).getParseRecord(userId, recordId);
    }

    @Test
    void testGetParseRecord_NotFound() throws Exception {
        // Given
        Long recordId = 999L;
        when(resumeParseService.getParseRecord(userId, recordId))
                .thenThrow(new RuntimeException("解析记录不存在"));

        // Mock AuthUtil for this test
        try (var mockedStatic = mockStatic(AuthUtil.class)) {
            mockedStatic.when(AuthUtil::getCurrentUserId).thenReturn(userId);

            // When & Then
            mockMvc.perform(get("/api/v1/me/resume/parse-records/{recordId}", recordId)
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isInternalServerError())
                    .andExpect(jsonPath("$.code").value(500));
        }

        verify(resumeParseService, times(1)).getParseRecord(userId, recordId);
    }

    @Test
    void testParseResume_WithAllParameters() throws Exception {
        // Given
        ResumeParseResultVO mockResult = createMockParseResult();
        when(resumeParseService.parseResume(eq(userId), any(ResumeParseRequestDTO.class)))
                .thenReturn(mockResult);

        // Mock AuthUtil for this test
        try (var mockedStatic = mockStatic(AuthUtil.class)) {
            mockedStatic.when(AuthUtil::getCurrentUserId).thenReturn(userId);

            // When & Then
            mockMvc.perform(multipart("/api/v1/me/resume/parse")
                            .file(testFile)
                            .param("overwriteExisting", "true")
                            .param("parseBasicInfo", "false")
                            .param("parseContactInfo", "false")
                            .param("parseEducation", "false")
                            .param("parseWorkExperience", "false")
                            .param("parseProjectExperience", "false")
                            .param("parseSkills", "false")
                            .param("parseTraining", "false")
                            .param("parseLanguages", "false")
                            .param("parseCertificates", "false")
                            .param("parseAwards", "false")
                            .contentType(MediaType.MULTIPART_FORM_DATA))
                    .andExpect(status().isCreated())
                    .andExpect(jsonPath("$.code").value(200));
        }

        // Verify that the service was called with correct parameters
        verify(resumeParseService, times(1)).parseResume(eq(userId), argThat(dto -> 
                dto.getOverwriteExisting() == true &&
                dto.getParseBasicInfo() == false &&
                dto.getParseContactInfo() == false &&
                dto.getParseEducation() == false &&
                dto.getParseWorkExperience() == false &&
                dto.getParseProjectExperience() == false &&
                dto.getParseSkills() == false &&
                dto.getParseTraining() == false &&
                dto.getParseLanguages() == false &&
                dto.getParseCertificates() == false &&
                dto.getParseAwards() == false
        ));
    }

    // Helper methods

    private ResumeParseResultVO createMockParseResult() {
        ResumeParseResultVO result = new ResumeParseResultVO();
        result.setRecordId(1L);
        result.setParseStatus(2);
        result.setParseStatusDesc("解析成功");
        result.setOriginalFilename("test-resume.pdf");
        result.setFileSize(1024L);
        result.setFileType("pdf");
        result.setParseDuration(5000);
        result.setCreatedAt(LocalDateTime.now());

        ResumeParseResultVO.ParseStatistics statistics = new ResumeParseResultVO.ParseStatistics();
        statistics.setBasicInfoUpdated(true);
        statistics.setContactMethodsAdded(2);
        statistics.setEducationRecordsAdded(1);
        statistics.setWorkExperienceAdded(1);
        statistics.setProjectExperienceAdded(1);
        statistics.setSkillsAdded(3);
        statistics.setTrainingRecordsAdded(0);
        statistics.setLanguagesAdded(0);
        statistics.setCertificatesAdded(0);
        statistics.setAwardsAdded(0);
        statistics.setDuplicateRecordsSkipped(0);
        statistics.setFailedRecords(0);

        result.setStatistics(statistics);
        return result;
    }
}
