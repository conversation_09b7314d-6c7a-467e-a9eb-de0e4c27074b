package com.tinyzk.user.center;
// Generated by Qodo Gen

import com.tinyzk.user.center.common.enums.IdentityType;
import com.tinyzk.user.center.dto.LoginDTO;
import com.tinyzk.user.center.dto.RegisterDTO;
import com.tinyzk.user.center.entity.UserAuth;
import com.tinyzk.user.center.entity.UserBase;
import com.tinyzk.user.center.entity.UserProfile;
import com.tinyzk.user.center.mapper.UserAuthMapper;
import com.tinyzk.user.center.mapper.UserBaseMapper;
import com.tinyzk.user.center.mapper.UserProfileMapper;
import com.tinyzk.user.center.service.impl.UserAuthServiceImpl;
import com.tinyzk.user.center.vo.LoginVO;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.crypto.password.PasswordEncoder;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class UserAuthServiceImpl_loginTest {


    // Successful login with existing user and valid credentials
    @Test
    public void test_successful_login_with_existing_user() {
        // Arrange
        UserAuthMapper userAuthMapper = mock(UserAuthMapper.class);
        UserBaseMapper userBaseMapper = mock(UserBaseMapper.class);
        UserProfileMapper userProfileMapper = mock(UserProfileMapper.class);
        PasswordEncoder passwordEncoder = mock(PasswordEncoder.class);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);
    
        UserAuthServiceImpl userAuthService = new UserAuthServiceImpl(userAuthMapper, userBaseMapper, userProfileMapper, passwordEncoder, eventPublisher);
    
        // Create test data
        LoginDTO loginDTO = new LoginDTO();
        loginDTO.setIdentityType(IdentityType.USERNAME);
        loginDTO.setIdentifier("testuser");
        loginDTO.setCredential("password123");
    
        UserAuth userAuth = new UserAuth();
        userAuth.setAuthId(1L);
        userAuth.setUserId(100L);
        userAuth.setIdentityType(IdentityType.USERNAME.name());
        userAuth.setIdentifier("testuser");
        userAuth.setCredential("encodedPassword");
    
        UserBase userBase = new UserBase();
        userBase.setUserId(100L);
        userBase.setStatus(1);
        userBase.setRealNameVerified(true);
    
        UserProfile userProfile = new UserProfile();
        userProfile.setUserId(100L);
        userProfile.setNickname("Test User");
        userProfile.setAvatarUrl("http://example.com/avatar.jpg");
    
        // Configure mocks
        when(userAuthMapper.selectOne(any())).thenReturn(userAuth);
        when(userBaseMapper.selectById(100L)).thenReturn(userBase);
        when(passwordEncoder.matches("password123", "encodedPassword")).thenReturn(true);
        when(userProfileMapper.selectOne(any())).thenReturn(userProfile);
    
        // Act
        LoginVO result = userAuthService.login(loginDTO);
    
        // Assert
        assertNotNull(result);
        assertEquals(100L, result.getUserId());
        assertFalse(result.getIsNewUser());
        assertTrue(result.getRealNameVerified());
        assertEquals("Test User", result.getNickname());
        assertEquals("http://example.com/avatar.jpg", result.getAvatarUrl());
    
        // Verify interactions
        verify(userAuthMapper).updateById(any(UserAuth.class));
        verify(eventPublisher).publishEvent(any());
    }

    // Login with non-existent user (triggers implicit registration)
    @Test
    public void test_login_with_nonexistent_user_triggers_registration() {
        // Arrange
        UserAuthMapper userAuthMapper = mock(UserAuthMapper.class);
        UserBaseMapper userBaseMapper = mock(UserBaseMapper.class);
        UserProfileMapper userProfileMapper = mock(UserProfileMapper.class);
        PasswordEncoder passwordEncoder = mock(PasswordEncoder.class);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);
        HttpServletRequest request = mock(HttpServletRequest.class);
    
        UserAuthServiceImpl userAuthService = spy(new UserAuthServiceImpl(userAuthMapper, userBaseMapper, userProfileMapper, passwordEncoder, eventPublisher));
    
        // Create test data
        LoginDTO loginDTO = new LoginDTO();
        loginDTO.setIdentityType(IdentityType.WECHAT_MP);
        loginDTO.setIdentifier("wx123456");
    
        // Mock the register method to verify it's called with correct parameters
        LoginVO expectedLoginVO = new LoginVO();
        expectedLoginVO.setUserId(200L);
        expectedLoginVO.setIsNewUser(true);
        expectedLoginVO.setNickname("WeChatUser");
        expectedLoginVO.setAvatarUrl("http://wechat.com/avatar.jpg");
    
        doReturn(expectedLoginVO).when(userAuthService).register(any(RegisterDTO.class));
    
        // Configure userAuthMapper to return null (user doesn't exist)
        when(userAuthMapper.selectOne(any())).thenReturn(null);
    
        // Act
        LoginVO result = userAuthService.login(loginDTO);
    
        // Assert
        assertNotNull(result);
        assertEquals(200L, result.getUserId());
        assertTrue(result.getIsNewUser());
        assertEquals("WeChatUser", result.getNickname());
    
        // Verify register was called with correct parameters
        ArgumentCaptor<RegisterDTO> registerDTOCaptor = ArgumentCaptor.forClass(RegisterDTO.class);
        verify(userAuthService).register(registerDTOCaptor.capture());
    
        RegisterDTO capturedRegisterDTO = registerDTOCaptor.getValue();
        assertEquals(IdentityType.WECHAT_MP, capturedRegisterDTO.getIdentityType());
        assertEquals("wx123456", capturedRegisterDTO.getIdentifier());
        assertEquals("WeChatUser", capturedRegisterDTO.getNickname());
        assertEquals("http://wechat.com/avatar.jpg", capturedRegisterDTO.getAvatarUrl());
    }
}