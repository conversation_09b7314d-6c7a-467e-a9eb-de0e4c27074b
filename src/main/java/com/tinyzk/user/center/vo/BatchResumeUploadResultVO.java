package com.tinyzk.user.center.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 批量简历上传结果VO
 */
@Data
@Schema(description = "批量简历上传结果VO")
public class BatchResumeUploadResultVO {

    /**
     * 处理统计信息
     */
    @Schema(description = "处理统计信息")
    private ProcessStatistics statistics;

    /**
     * 每个文件的处理结果
     */
    @Schema(description = "每个文件的处理结果")
    private List<FileProcessResult> fileResults;

    /**
     * 处理开始时间
     */
    @Schema(description = "处理开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 处理结束时间
     */
    @Schema(description = "处理结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 总处理时间（毫秒）
     */
    @Schema(description = "总处理时间（毫秒）")
    private Long totalDurationMs;

    /**
     * 处理统计信息
     */
    @Data
    @Schema(description = "处理统计信息")
    public static class ProcessStatistics {
        /**
         * 总文件数
         */
        @Schema(description = "总文件数")
        private Integer totalFiles;

        /**
         * 成功处理数
         */
        @Schema(description = "成功处理数")
        private Integer successCount;

        /**
         * 失败处理数
         */
        @Schema(description = "失败处理数")
        private Integer failureCount;

        /**
         * 跳过处理数（重复用户且不覆盖）
         */
        @Schema(description = "跳过处理数（重复用户且不覆盖）")
        private Integer skippedCount;

        /**
         * 新创建用户数
         */
        @Schema(description = "新创建用户数")
        private Integer newUserCount;

        /**
         * 更新用户数
         */
        @Schema(description = "更新用户数")
        private Integer updatedUserCount;

        /**
         * 成功率
         */
        @Schema(description = "成功率")
        private Double successRate;
    }

    /**
     * 单个文件处理结果
     */
    @Data
    @Schema(description = "单个文件处理结果")
    public static class FileProcessResult {
        /**
         * 文件名
         */
        @Schema(description = "文件名")
        private String fileName;

        /**
         * 文件大小（字节）
         */
        @Schema(description = "文件大小（字节）")
        private Long fileSize;

        /**
         * 处理状态
         */
        @Schema(description = "处理状态")
        private ProcessStatus status;

        /**
         * 创建或更新的用户ID
         */
        @Schema(description = "创建或更新的用户ID")
        private Long userId;

        /**
         * 用户操作类型
         */
        @Schema(description = "用户操作类型")
        private UserOperationType userOperation;

        /**
         * 错误信息
         */
        @Schema(description = "错误信息")
        private String errorMessage;

        /**
         * 错误类型
         */
        @Schema(description = "错误类型")
        private ErrorType errorType;

        /**
         * 处理时间（毫秒）
         */
        @Schema(description = "处理时间（毫秒）")
        private Long processingTimeMs;

        /**
         * 解析到的用户信息摘要
         */
        @Schema(description = "解析到的用户信息摘要")
        private UserInfoSummary userInfo;

        /**
         * 第三方解析记录ID
         */
        @Schema(description = "第三方解析记录ID")
        private Long parseRecordId;
    }

    /**
     * 处理状态枚举
     */
    @Schema(description = "处理状态")
    public enum ProcessStatus {
        SUCCESS("成功"),
        FAILURE("失败"),
        SKIPPED("跳过");

        private final String description;

        ProcessStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 用户操作类型枚举
     */
    @Schema(description = "用户操作类型")
    public enum UserOperationType {
        CREATED("新建用户"),
        UPDATED("更新用户"),
        SKIPPED("跳过（重复用户）");

        private final String description;

        UserOperationType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 错误类型枚举
     */
    @Schema(description = "错误类型")
    public enum ErrorType {
        FILE_VALIDATION("文件验证失败"),
        PARSE_API_ERROR("解析API调用失败"),
        DATA_CONVERSION("数据转换失败"),
        DATABASE_ERROR("数据库操作失败"),
        TIMEOUT("处理超时"),
        UNKNOWN("未知错误");

        private final String description;

        ErrorType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 用户信息摘要
     */
    @Data
    @Schema(description = "用户信息摘要")
    public static class UserInfoSummary {
        /**
         * 姓名
         */
        @Schema(description = "姓名")
        private String name;

        /**
         * 手机号
         */
        @Schema(description = "手机号")
        private String phone;

        /**
         * 邮箱
         */
        @Schema(description = "邮箱")
        private String email;

        /**
         * 性别
         */
        @Schema(description = "性别")
        private String gender;

        /**
         * 年龄
         */
        @Schema(description = "年龄")
        private Integer age;

        /**
         * 工作年限
         */
        @Schema(description = "工作年限")
        private String workExperience;

        /**
         * 最高学历
         */
        @Schema(description = "最高学历")
        private String education;
    }
}
