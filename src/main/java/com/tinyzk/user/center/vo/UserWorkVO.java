package com.tinyzk.user.center.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户工作经历视图对象
 */
@Data
@Schema(description = "用户工作经历视图对象")
public class UserWorkVO {

    @Schema(description = "工作经历ID")
    private Long workId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "公司/组织名称")
    private String companyName;

    @Schema(description = "公司/组织Logo URL")
    private String companyLogo;

    @Schema(description = "公司/组织官网 URL")
    private String companyUrl;

    @Schema(description = "公司/组织规模")
    private Integer companySize;

    @Schema(description = "公司/组织行业")
    private String companyIndustry;

    @Schema(description = "公司/组织地点")
    private String companyLocation;

    @Schema(description = "职位/头衔")
    private String position;

    @Schema(description = "所属部门")
    private String department;

    @Schema(description = "入职日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @Schema(description = "离职日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @Schema(description = "工作职责描述")
    private String description;

    @Schema(description = "工作业绩/成果")
    private String achievements;

    @Schema(description = "汇报对象")
    private String reportingTo;

    @Schema(description = "离职原因")
    private String reasonForLeaving;

    @Schema(description = "薪资最小值")
    private BigDecimal salaryMin;

    @Schema(description = "薪资最大值")
    private BigDecimal salaryMax;

    @Schema(description = "认证方式(1-工牌、2-劳动合同、3-社保、4-个税)")
    private Integer certificationType;

    @Schema(description = "认证状态(0-未认证、1-已认证)")
    private Integer certificationStatus;

    @Schema(description = "可见性")
    private String visibility;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
} 