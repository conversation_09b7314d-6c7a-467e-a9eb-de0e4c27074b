package com.tinyzk.user.center.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 简历解析结果VO
 */
@Data
@Schema(description = "简历解析结果VO")
public class ResumeParseResultVO {

    /**
     * 解析记录ID
     */
    @Schema(description = "解析记录ID")
    private Long recordId;

    /**
     * 解析状态 (0-待解析, 1-解析中, 2-解析成功, 3-解析失败)
     */
    @Schema(description = "解析状态", example = "2")
    private Integer parseStatus;

    /**
     * 解析状态描述
     */
    @Schema(description = "解析状态描述", example = "解析成功")
    private String parseStatusDesc;

    /**
     * 原始文件名
     */
    @Schema(description = "原始文件名")
    private String originalFilename;

    /**
     * 文件大小
     */
    @Schema(description = "文件大小(字节)")
    private Long fileSize;

    /**
     * 文件类型
     */
    @Schema(description = "文件类型")
    private String fileType;

    /**
     * 解析耗时(毫秒)
     */
    @Schema(description = "解析耗时(毫秒)")
    private Integer parseDuration;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 解析统计信息
     */
    @Schema(description = "解析统计信息")
    private ParseStatistics statistics;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    /**
     * 解析统计信息内部类
     */
    @Data
    @Schema(description = "解析统计信息")
    public static class ParseStatistics {
        
        @Schema(description = "是否更新了基本信息")
        private Boolean basicInfoUpdated;
        
        @Schema(description = "新增联系方式数量")
        private Integer contactMethodsAdded;
        
        @Schema(description = "新增教育经历数量")
        private Integer educationRecordsAdded;
        
        @Schema(description = "新增工作经历数量")
        private Integer workExperienceAdded;
        
        @Schema(description = "新增项目经历数量")
        private Integer projectExperienceAdded;
        
        @Schema(description = "新增技能数量")
        private Integer skillsAdded;
        
        @Schema(description = "新增培训经历数量")
        private Integer trainingRecordsAdded;
        
        @Schema(description = "新增语言能力数量")
        private Integer languagesAdded;
        
        @Schema(description = "新增证书数量")
        private Integer certificatesAdded;
        
        @Schema(description = "新增获奖记录数量")
        private Integer awardsAdded;
        
        @Schema(description = "跳过的重复记录数量")
        private Integer duplicateRecordsSkipped;
        
        @Schema(description = "处理失败的记录数量")
        private Integer failedRecords;
        
        @Schema(description = "处理失败的详细信息")
        private List<String> failureDetails;
    }
}
