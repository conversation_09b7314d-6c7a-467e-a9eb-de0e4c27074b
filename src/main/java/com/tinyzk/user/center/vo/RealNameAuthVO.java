package com.tinyzk.user.center.vo;

import com.tinyzk.user.center.common.enums.RealNameAuthStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;

/**
 * 实名认证响应 VO
 */
@Data
@Schema(description = "实名认证响应")
public class RealNameAuthVO {
    
    @Schema(description = "用户ID")
    private Long userId;
    
    @Schema(description = "真实姓名（脱敏）")
    private String realName;
    
    @Schema(description = "身份证号码（脱敏）")
    private String idNumber;
    
    @Schema(description = "认证状态")
    private RealNameAuthStatus status;
    
    @Schema(description = "提交认证时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime requestedAt;
    
    @Schema(description = "认证通过时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime verifiedAt;
    
}
