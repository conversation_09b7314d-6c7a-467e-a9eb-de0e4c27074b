package com.tinyzk.user.center.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户列表视图对象
 */
@Data
public class UserListVO {
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;
    
    /**
     * 用户昵称
     */
    @Schema(description = "用户昵称")
    private String nickname;
    
    /**
     * 用户标识列表（如邮箱、手机号等）
     */
    @Schema(description = "用户标识列表")
    private List<IdentifierVO> identifiers;
    
    /**
     * 用户状态 (1:正常, 2:禁用, 0:注销)
     */
    @Schema(description = "用户状态")
    private Integer status;
    
    /**
     * 实名认证状态
     */
    @Schema(description = "实名认证状态")
    private Boolean realNameVerified;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 最后登录时间
     */
    @Schema(description = "最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginAt;
    
    /**
     * 头像URL
     */
    @Schema(description = "头像URL")
    private String avatarUrl;
    
    /**
     * 用户标识视图对象
     */
    @Data
    public static class IdentifierVO {
        /**
         * 用户ID
         */
        @Schema(description = "用户ID")
        private Long userId;
        
        /**
         * 标识类型
         */
        @Schema(description = "标识类型")
        private String type;
        
        /**
         * 标识值（脱敏）
         */
        @Schema(description = "标识值（脱敏）")
        private String value;
    }
}
