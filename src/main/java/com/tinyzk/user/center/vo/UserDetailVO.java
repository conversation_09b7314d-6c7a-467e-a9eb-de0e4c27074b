package com.tinyzk.user.center.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tinyzk.user.center.common.enums.PoliticalStatus;
import com.tinyzk.user.center.common.enums.SpecialStatus;
import com.tinyzk.user.center.common.enums.MaritalStatus;
import com.tinyzk.user.center.common.enums.FertilityStatus;
import com.tinyzk.user.center.common.enums.HealthStatus;

/**
 * 用户详情视图对象
 */
@Data
public class UserDetailVO {
    /**
     * 用户基础信息
     */
    @Schema(description = "用户基础信息")
    private UserBaseInfo userBase;
    
    /**
     * 用户资料信息
     */
    @Schema(description = "用户资料信息")
    private UserProfileInfo userProfile;
    
    /**
     * 用户认证信息列表
     */
    @Schema(description = "用户认证信息列表")
    private List<UserAuthInfo> userAuths;
    
    /**
     * 用户联系方式列表
     */
    @Schema(description = "用户联系方式列表")
    private List<UserContactInfo> userContactMethods;
    
    /**
     * 用户教育经历列表
     */
    @Schema(description = "用户教育经历列表")
    private List<UserEducationInfo> userEducationHistories;
    
    /**
     * 用户工作经历列表
     */
    @Schema(description = "用户工作经历列表")
    private List<UserWorkInfo> userWorkHistories;
    
    /**
     * 用户项目经历列表
     */
    @Schema(description = "用户项目经历列表")
    private List<UserProjectInfo> userProjectHistories;
    
    /**
     * 用户培训经历列表
     */
    @Schema(description = "用户培训经历列表")
    private List<UserTrainingInfo> userTrainingHistories;
    
    /**
     * 用户兼职经历列表
     */
    @Schema(description = "用户兼职经历列表")
    private List<UserPartTimeInfo> userPartTimeHistories;
    
    /**
     * 用户基础信息
     */
    @Data
    public static class UserBaseInfo {
        /**
         * 用户ID
         */
        @Schema(description = "用户ID")
        private Long userId;
        
        /**
         * 实名认证状态
         */
        @Schema(description = "实名认证状态")
        private Boolean realNameVerified;
        
        /**
         * 真实姓名（脱敏）
         */
        @Schema(description = "真实姓名（脱敏）")
        private String realName;
        
        /**
         * 身份证号（脱敏）
         */
        @Schema(description = "身份证号（脱敏）")
        private String idCardNumber;
        
        /**
         * 用户状态(1:正常,2:禁用,0:注销/逻辑删除,3:已合并)
         */
        @Schema(description = "用户状态: 1-正常,2-禁用,0-注销/逻辑删除,3-已合并")
        private Integer status;
        
        /**
         * 创建时间
         */
        @Schema(description = "创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createdAt;
        
        /**
         * 更新时间
         */
        @Schema(description = "更新时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime updatedAt;
        
        /**
         * 删除时间
         */
        @Schema(description = "删除时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime deletedAt;
    }
    
    /**
     * 用户资料信息
     */
    @Data
    public static class UserProfileInfo {
        /**
         * 资料ID
         */
        @Schema(description = "资料ID")
        private Long profileId;
        
        /**
         * 用户昵称
         */
        @Schema(description = "用户昵称")
        private String nickname;
        
        /**
         * 头像URL
         */
        @Schema(description = "头像URL")
        private String avatarUrl;
        
        /**
         * 性别(0:未知,1:男,2:女)
         */
        @Schema(description = "性别")
        private Integer gender;
        
        /**
         * 生日
         */
        @Schema(description = "生日")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate birthday;
        
        /**
         * 国籍
         */
        @Schema(description = "国籍")
        private String nationality;
        
        /**
         * 民族
         */
        @Schema(description = "民族")
        private String ethnicity;
        
        /**
         * 特殊身份(1-军人,2-警察,3-医生,4-教师,5-残疾人,6-其他)
         */
        @Schema(description = "特殊身份")
        private SpecialStatus specialStatus;
        
        /**
         * 政治面貌(1-中共党员,2-中共预备党员,3-共青团员,4-群众)
         */
        @Schema(description = "政治面貌")
        private PoliticalStatus politicalStatus;
        
        /**
         * 婚姻状况(1-未婚,2-已婚,3-离异,4-丧偶)
         */
        @Schema(description = "婚姻状况")
        private MaritalStatus maritalStatus;
        
        /**
         * 生育情况(1-未育,2-已育,3-已育一孩,4-已育两孩及以上)
         */
        @Schema(description = "生育情况")
        private FertilityStatus fertilityStatus;
        
        /**
         * 健康状况(1-健康,2-良好,3-一般,4-较差)
         */
        @Schema(description = "健康状况")
        private HealthStatus healthStatus;
        
        /**
         * 地区编码
         */
        @Schema(description = "地区编码")
        private String regionCode;
        
        /**
         * 地区名称
         */
        @Schema(description = "地区名称")
        private String regionName;
        
        /**
         * 详细地址
         */
        @Schema(description = "详细地址")
        private String address;
        
        /**
         * 个人简介
         */
        @Schema(description = "个人简介")
        private String bio;
        
        /**
         * 创建时间
         */
        @Schema(description = "创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createdAt;
        
        /**
         * 更新时间
         */
        @Schema(description = "更新时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime updatedAt;
    }
    
    /**
     * 用户认证信息
     */
    @Data
    public static class UserAuthInfo {
        /**
         * 认证ID
         */
        @Schema(description = "认证ID")
        private Long authId;
        
        /**
         * 认证类型
         */
        @Schema(description = "认证类型")
        private String identityType;
        
        /**
         * 认证标识（脱敏）
         */
        @Schema(description = "认证标识（脱敏）")
        private String identifier;
        
        /**
         * 是否已验证(1:已验证,0:未验证)
         */
        @Schema(description = "是否已验证")
        private Integer verified;
        
        /**
         * 最后登录时间
         */
        @Schema(description = "最后登录时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastLoginAt;
        
        /**
         * 创建时间
         */
        @Schema(description = "创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createdAt;
    }
    
    /**
     * 用户联系方式信息
     */
    @Data
    public static class UserContactInfo {
        /**
         * 联系方式ID
         */
        @Schema(description = "联系方式ID")
        private Long contactId;
        
        /**
         * 联系方式类型
         */
        @Schema(description = "联系方式类型")
        private Integer contactType;
        
        /**
         * 联系方式值（脱敏）
         */
        @Schema(description = "联系方式值（脱敏）")
        private String contactValue;
        
        /**
         * 联系方式标签
         */
        @Schema(description = "联系方式标签")
        private String label;
        
        /**
         * 是否已验证
         */
        @Schema(description = "是否已验证")
        private Boolean isVerified;
        
        /**
         * 验证时间
         */
        @Schema(description = "验证时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime verifiedAt;
    }
    
    /**
     * 用户教育经历信息
     */
    @Data
    public static class UserEducationInfo {
        /**
         * 教育经历ID
         */
        @Schema(description = "教育经历ID")
        private Long eduId;
        
        /**
         * 学校名称
         */
        @Schema(description = "学校名称")
        private String schoolName;
        
        /**
         * 学位
         */
        @Schema(description = "学位")
        private String degree;
        
        /**
         * 学位等级
         */
        @Schema(description = "学位等级")
        private Integer degreeLevel;
        
        /**
         * 专业
         */
        @Schema(description = "专业")
        private String major;
        
        /**
         * 第二专业
         */
        @Schema(description = "第二专业")
        private String secondaryMajor;
        
        /**
         * 专业方向
         */
        @Schema(description = "专业方向")
        private String majorArea;
        
        /**
         * 专业GPA
         */
        @Schema(description = "专业GPA")
        private Float majorGpa;
        
        /**
         * 入学时间
         */
        @Schema(description = "入学时间")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate startDate;
        
        /**
         * 毕业时间
         */
        @Schema(description = "毕业时间")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate endDate;
        
        /**
         * 描述/在校经历/荣誉等
         */
        @Schema(description = "描述/在校经历/荣誉等")
        private String description;
    }
    
    /**
     * 用户工作经历信息
     */
    @Data
    public static class UserWorkInfo {
        /**
         * 工作经历ID
         */
        @Schema(description = "工作经历ID")
        private Long workId;
        
        /**
         * 公司/组织名称
         */
        @Schema(description = "公司/组织名称")
        private String companyName;
        
        /**
         * 公司/组织Logo URL
         */
        @Schema(description = "公司/组织Logo URL")
        private String companyLogo;
        
        /**
         * 公司/组织官网 URL
         */
        @Schema(description = "公司/组织官网 URL")
        private String companyUrl;
        
        /**
         * 公司/组织规模
         */
        @Schema(description = "公司/组织规模")
        private Integer companySize;
        
        /**
         * 公司/组织行业
         */
        @Schema(description = "公司/组织行业")
        private String companyIndustry;
        
        /**
         * 公司/组织地点
         */
        @Schema(description = "公司/组织地点")
        private String companyLocation;
        
        /**
         * 职位/头衔
         */
        @Schema(description = "职位/头衔")
        private String positionName;
        
        /**
         * 所属部门
         */
        @Schema(description = "所属部门")
        private String department;
        
        /**
         * 入职时间
         */
        @Schema(description = "入职时间")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate startDate;
        
        /**
         * 离职时间
         */
        @Schema(description = "离职时间")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate endDate;
        
        /**
         * 工作职责描述
         */
        @Schema(description = "工作职责描述")
        private String description;
        
        /**
         * 工作业绩/成果
         */
        @Schema(description = "工作业绩/成果")
        private String achievements;
    }
    
    /**
     * 用户项目经历信息
     */
    @Data
    public static class UserProjectInfo {
        /**
         * 项目经历ID
         */
        @Schema(description = "项目经历ID")
        private Long projectId;
        
        /**
         * 项目名称
         */
        @Schema(description = "项目名称")
        private String projectName;
        
        /**
         * 用户在项目中的角色/职责
         */
        @Schema(description = "用户在项目中的角色/职责")
        private String role;
        
        /**
         * 项目开始时间
         */
        @Schema(description = "项目开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate startDate;
        
        /**
         * 项目结束时间
         */
        @Schema(description = "项目结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate endDate;
        
        /**
         * 项目描述
         */
        @Schema(description = "项目描述")
        private String description;
        
        /**
         * 项目链接
         */
        @Schema(description = "项目链接")
        private String projectUrl;
        
        /**
         * 关联组织/公司
         */
        @Schema(description = "关联组织/公司")
        private String associatedOrganization;
    }
    
    /**
     * 用户培训经历信息
     */
    @Data
    public static class UserTrainingInfo {
        /**
         * 培训经历ID
         */
        @Schema(description = "培训经历ID")
        private Long trainingId;
        
        /**
         * 培训名称
         */
        @Schema(description = "培训名称")
        private String trainingName;
        
        /**
         * 培训类型（如：线上培训、线下培训）
         */
        @Schema(description = "培训类型（如：线上培训、线下培训）")
        private String trainingType;
        
        /**
         * 培训提供者/机构
         */
        @Schema(description = "培训提供者/机构")
        private String trainingProvider;
        
        /**
         * 开始时间
         */
        @Schema(description = "开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate startDate;
        
        /**
         * 结束时间
         */
        @Schema(description = "结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate endDate;
        
        /**
         * 培训描述
         */
        @Schema(description = "培训描述")
        private String description;
    }
    
    /**
     * 用户兼职经历信息
     */
    @Data
    public static class UserPartTimeInfo {
        /**
         * 兼职经历ID
         */
        @Schema(description = "兼职经历ID")
        private Long partTimeId;
        
        /**
         * 兼职名称
         */
        @Schema(description = "兼职名称")
        private String partTimeName;
        
        /**
         * 兼职类型
         * 1-咨询, 2-设计, 3-开发, 4-运营, 5-销售, 6-客服, 7-服务员, 8-其他
         */
        @Schema(description = "兼职类型")
        private Integer partTimeType;
        
        /**
         * 兼职提供者/机构
         */
        @Schema(description = "兼职提供者/机构")
        private String partTimeProvider;
        
        /**
         * 兼职地点
         */
        @Schema(description = "兼职地点")
        private String partTimeLocation;
        
        /**
         * 服务周期（如：1个月, 3个月, 6个月, 1年）
         */
        @Schema(description = "服务周期（如：1个月, 3个月, 6个月, 1年）")
        private String servicePeriod;
        
        /**
         * 开始时间
         */
        @Schema(description = "开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate startDate;
        
        /**
         * 结束时间
         */
        @Schema(description = "结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate endDate;
        
        /**
         * 兼职描述
         */
        @Schema(description = "兼职描述")
        private String description;
    }
}
