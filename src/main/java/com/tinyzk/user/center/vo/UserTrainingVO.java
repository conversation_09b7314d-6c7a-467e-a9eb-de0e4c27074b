package com.tinyzk.user.center.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户培训经历视图对象
 */
@Data
@Schema(description = "用户培训经历视图对象")
public class UserTrainingVO {

    @Schema(description = "培训经历ID", example = "1")
    private Long trainingId;

    @Schema(description = "用户ID", example = "10001")
    private Long userId;

    @Schema(description = "培训名称", example = "Web前端开发培训")
    private String trainingName;

    @Schema(description = "培训类型（如：线上培训、线下培训）", example = "线上培训")
    private String trainingType;

    @Schema(description = "培训提供者/机构", example = "某教育机构")
    private String trainingProvider;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "开始日期", example = "2023-01-01")
    private LocalDate startDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "结束日期（NULL表示进行中）", example = "2023-06-30")
    private LocalDate endDate;

    @Schema(description = "培训描述", example = "学习了HTML、CSS、JavaScript等前端技术")
    private String description;

    @Schema(description = "可见性", example = "PUBLIC")
    private String visibility;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
} 