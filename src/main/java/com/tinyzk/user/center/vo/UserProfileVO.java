package com.tinyzk.user.center.vo;

import com.tinyzk.user.center.common.enums.SpecialStatus;
import com.tinyzk.user.center.common.enums.PoliticalStatus;
import com.tinyzk.user.center.common.enums.MaritalStatus;
import com.tinyzk.user.center.common.enums.FertilityStatus;
import com.tinyzk.user.center.common.enums.HealthStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户个人资料视图对象
 */
@Data
@Schema(description = "用户个人资料视图对象")
public class UserProfileVO {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像URL")
    private String avatarUrl;

    @Schema(description = "性别, 0-未知, 1-男, 2-女")
    private Integer gender;

    @Schema(description = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;

    @Schema(description = "国籍")
    private String nationality;
    
    @Schema(description = "民族")
    private String ethnicity;
    
    @Schema(description = "特殊身份, SOLDIER-军人, POLICE-警察, DOCTOR-医生, TEACHER-教师, DISABLED-残疾人, OTHER-其他")
    private SpecialStatus specialStatus;
    
    @Schema(description = "政治面貌, CPC_MEMBER-中共党员, CPC_PREPARED_MEMBER-中共预备党员, YOUNG_PIONEER-共青团员, PUBLIC-群众")
    private PoliticalStatus politicalStatus;
    
    @Schema(description = "婚姻状况, UNMARRIED-未婚, MARRIED-已婚, DIVORCED-离异, WIDOWER-丧偶")
    private MaritalStatus maritalStatus;
    
    @Schema(description = "生育情况, UNBORN-未育, BORN-已育, BORN_ONE_CHILD-已育一孩, BORN_TWO_CHILDREN_OR_MORE-已育两孩及以上")
    private FertilityStatus fertilityStatus;
    
    @Schema(description = "健康状况, HEALTHY-健康, GOOD-良好, AVERAGE-一般, POOR-较差")
    private HealthStatus healthStatus;
    
    @Schema(description = "地区编码")
    private String regionCode;
    
    @Schema(description = "地区名称")
    private String regionName;
    
    @Schema(description = "详细地址")
    private String address;
    
    @Schema(description = "个人简介")
    private String bio;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}