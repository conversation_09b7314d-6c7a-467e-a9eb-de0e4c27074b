package com.tinyzk.user.center.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分页响应视图对象
 * @param <T> 数据类型
 */
@Data
@NoArgsConstructor
public class PageResponseVO<T> {
    /**
     * 数据列表
     */
    @Schema(description = "数据列表")
    private List<T> data;
    
    /**
     * 当前页码
     */
    @Schema(description = "当前页码")
    private Integer pageNumber;
    
    /**
     * 每页数量
     */
    @Schema(description = "每页数量")
    private Integer pageSize;
    
    /**
     * 总记录数
     */
    @Schema(description = "总记录数")
    private Long total;
    
    /**
     * 总页数
     */
    @Schema(description = "总页数")
    private Integer totalPages;
    
    /**
     * 构造分页响应对象
     * 
     * @param data 数据列表
     * @param pageNumber 当前页码
     * @param pageSize 每页数量
     * @param total 总记录数
     */
    public PageResponseVO(List<T> data, Integer pageNumber, Integer pageSize, Long total) {
        this.data = data;
        this.pageNumber = pageNumber;
        this.pageSize = pageSize;
        this.total = total;
        this.totalPages = (int) Math.ceil((double) total / pageSize);
    }
}
