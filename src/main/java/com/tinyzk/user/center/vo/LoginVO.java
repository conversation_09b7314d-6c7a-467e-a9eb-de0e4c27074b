package com.tinyzk.user.center.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 登录响应视图对象
 */
@Data
@Schema(description = "登录响应VO")
public class LoginVO {
    
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;
    
    /**
     * 是否是新用户（首次登录自动注册）
     */
    @Schema(description = "是否为新用户")
    private Boolean isNewUser;
    
    /**
     * 是否已实名认证
     */
    @Schema(description = "是否已实名认证")
    private Boolean realNameVerified;
    
    /**
     * 用户昵称
     */
    @Schema(description = "昵称")
    private String nickname;
    
    /**
     * 头像URL
     */
    @Schema(description = "头像URL")
    private String avatarUrl;

    @Schema(description = "JWT Token")
    private String token;
}
