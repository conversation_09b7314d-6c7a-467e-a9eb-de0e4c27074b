package com.tinyzk.user.center.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户兼职经历视图对象
 */
@Data
@Schema(description = "用户兼职经历视图对象")
public class UserPartTimeVO {

    @Schema(description = "兼职经历ID", example = "1")
    private Long partTimeId;

    @Schema(description = "用户ID", example = "10001")
    private Long userId;

    @Schema(description = "兼职名称", example = "前端开发兼职")
    private String partTimeName;

    @Schema(description = "兼职类型(1-咨询, 2-设计, 3-开发, 4-运营, 5-销售, 6-客服, 7-服务员, 8-其他)", example = "3")
    private Integer partTimeType;

    @Schema(description = "兼职提供者/机构", example = "某科技公司")
    private String partTimeProvider;

    @Schema(description = "兼职地点", example = "北京市")
    private String partTimeLocation;

    @Schema(description = "兼职薪资", example = "5000")
    private BigDecimal partTimeSalary;

    @Schema(description = "服务周期（如：1个月, 3个月, 6个月, 1年）", example = "3个月")
    private String servicePeriod;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "开始日期", example = "2023-01-01")
    private LocalDate startDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "结束日期（NULL表示进行中）", example = "2023-06-30")
    private LocalDate endDate;

    @Schema(description = "兼职描述", example = "负责公司官网前端页面开发和维护")
    private String description;

    @Schema(description = "可见性", example = "PUBLIC")
    private String visibility;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
} 