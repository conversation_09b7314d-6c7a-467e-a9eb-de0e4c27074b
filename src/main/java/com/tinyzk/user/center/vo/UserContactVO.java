package com.tinyzk.user.center.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户联系方式视图对象
 */
@Data
@Schema(description = "用户联系方式视图对象")
public class UserContactVO {

    @Schema(description = "联系方式ID", example = "1")
    private Long contactId;

    @Schema(description = "用户ID", example = "10001")
    private Long userId;

    @Schema(description = "联系方式类型(1-备用邮箱, 2-工作电话, 3-紧急联系人, 4-微信, 5-QQ、6-即刻、7-小红书、8-微博、9-抖音、10-其他)", example = "1")
    private Integer contactType;

    @Schema(description = "联系方式的值(邮箱地址, 电话号码, URL, ID等)", example = "<EMAIL>")
    private String contactValue;

    @Schema(description = "用户自定义标签(1-备用邮箱, 2-工作电话, 3-紧急联系人, 4-社交账号)", example = "1")
    private Integer label;

    @Schema(description = "可见性(PUBLIC, CONNECTIONS, PRIVATE)", example = "PUBLIC")
    private String visibility;

    @Schema(description = "是否已验证", example = "false")
    private Boolean isVerified;

    @Schema(description = "验证时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime verifiedAt;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
} 