package com.tinyzk.user.center.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 文件上传消息DTO
 */
@Data
public class FileUploadMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * OSS对象键
     */
    private String ossKey;

    /**
     * 文件URL
     */
    private String fileUrl;

    /**
     * 上传状态
     */
    private String uploadStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 文件MD5
     */
    private String fileMd5;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务ID
     */
    private String businessId;
}
