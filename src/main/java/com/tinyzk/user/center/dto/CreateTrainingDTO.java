package com.tinyzk.user.center.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 创建用户培训经历DTO
 */
@Data
@Schema(description = "创建用户培训经历DTO")
public class CreateTrainingDTO {

    @NotEmpty(message = "培训名称不能为空")
    @Schema(description = "培训名称", required = true, example = "Web前端开发培训")
    private String trainingName;

    @Schema(description = "培训类型（如：线上培训、线下培训）", example = "线上培训")
    private String trainingType;

    @Schema(description = "培训提供者/机构", example = "某教育机构")
    private String trainingProvider;

    @NotNull(message = "开始日期不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "开始日期（格式：yyyy-MM-dd）", required = true, example = "2023-01-01")
    private LocalDate startDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "结束日期（格式：yyyy-MM-dd，NULL表示进行中）", example = "2023-06-30")
    private LocalDate endDate;

    @Schema(description = "培训描述", example = "学习了HTML、CSS、JavaScript等前端技术")
    private String description;

    @Schema(description = "可见性（PUBLIC, FRIENDS, PRIVATE）", example = "PUBLIC")
    private String visibility;
} 