package com.tinyzk.user.center.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 批量简历上传请求DTO
 */
@Data
@Schema(description = "批量简历上传请求DTO")
public class BatchResumeUploadRequestDTO {

    /**
     * 简历文件数组
     */
    @NotNull(message = "简历文件不能为空")
    @Size(min = 1, max = 20, message = "文件数量必须在1-20个之间")
    @Schema(description = "简历文件数组", required = true)
    private MultipartFile[] files;

    /**
     * 是否覆盖现有数据
     */
    @Schema(description = "是否覆盖现有数据", defaultValue = "false")
    private Boolean overwriteExisting = false;

    /**
     * 解析选项 - 是否解析基本信息
     */
    @Schema(description = "是否解析基本信息", defaultValue = "true")
    private Boolean parseBasicInfo = true;

    /**
     * 解析选项 - 是否解析联系方式
     */
    @Schema(description = "是否解析联系方式", defaultValue = "true")
    private Boolean parseContactInfo = true;

    /**
     * 解析选项 - 是否解析教育经历
     */
    @Schema(description = "是否解析教育经历", defaultValue = "true")
    private Boolean parseEducation = true;

    /**
     * 解析选项 - 是否解析工作经历
     */
    @Schema(description = "是否解析工作经历", defaultValue = "true")
    private Boolean parseWorkExperience = true;

    /**
     * 解析选项 - 是否解析项目经历
     */
    @Schema(description = "是否解析项目经历", defaultValue = "true")
    private Boolean parseProjectExperience = true;

    /**
     * 解析选项 - 是否解析技能信息
     */
    @Schema(description = "是否解析技能信息", defaultValue = "true")
    private Boolean parseSkills = true;

    /**
     * 解析选项 - 是否解析培训经历
     */
    @Schema(description = "是否解析培训经历", defaultValue = "true")
    private Boolean parseTraining = true;

    /**
     * 解析选项 - 是否解析语言能力
     */
    @Schema(description = "是否解析语言能力", defaultValue = "true")
    private Boolean parseLanguages = true;

    /**
     * 解析选项 - 是否解析证书信息
     */
    @Schema(description = "是否解析证书信息", defaultValue = "true")
    private Boolean parseCertificates = true;

    /**
     * 解析选项 - 是否解析获奖记录
     */
    @Schema(description = "是否解析获奖记录", defaultValue = "true")
    private Boolean parseAwards = true;

    /**
     * 最大并发处理数量
     */
    @Schema(description = "最大并发处理数量", defaultValue = "5")
    private Integer maxConcurrency = 5;

    /**
     * 单个文件处理超时时间（秒）
     */
    @Schema(description = "单个文件处理超时时间（秒）", defaultValue = "60")
    private Integer timeoutSeconds = 60;

    /**
     * 转换为单个简历解析请求DTO
     */
    public ResumeParseRequestDTO toResumeParseRequestDTO(MultipartFile file) {
        ResumeParseRequestDTO requestDTO = new ResumeParseRequestDTO();
        requestDTO.setFile(file);
        requestDTO.setOverwriteExisting(this.overwriteExisting);
        requestDTO.setParseBasicInfo(this.parseBasicInfo);
        requestDTO.setParseContactInfo(this.parseContactInfo);
        requestDTO.setParseEducation(this.parseEducation);
        requestDTO.setParseWorkExperience(this.parseWorkExperience);
        requestDTO.setParseProjectExperience(this.parseProjectExperience);
        requestDTO.setParseSkills(this.parseSkills);
        requestDTO.setParseTraining(this.parseTraining);
        requestDTO.setParseLanguages(this.parseLanguages);
        requestDTO.setParseCertificates(this.parseCertificates);
        requestDTO.setParseAwards(this.parseAwards);
        return requestDTO;
    }
}
