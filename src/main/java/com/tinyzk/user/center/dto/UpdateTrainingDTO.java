package com.tinyzk.user.center.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 更新用户培训经历DTO
 */
@Data
@Schema(description = "更新用户培训经历DTO")
public class UpdateTrainingDTO {

    @Schema(description = "培训名称", example = "Web前端开发培训")
    private String trainingName;

    @Schema(description = "培训类型（如：线上培训、线下培训）", example = "线上培训")
    private String trainingType;

    @Schema(description = "培训提供者/机构", example = "某教育机构")
    private String trainingProvider;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "开始日期（格式：yyyy-MM-dd）", example = "2023-01-01")
    private LocalDate startDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "结束日期（格式：yyyy-MM-dd，NULL表示进行中）", example = "2023-06-30")
    private LocalDate endDate;

    @Schema(description = "培训描述", example = "学习了HTML、CSS、JavaScript等前端技术")
    private String description;

    @Schema(description = "可见性（PUBLIC, FRIENDS, PRIVATE）", example = "PUBLIC")
    private String visibility;
} 