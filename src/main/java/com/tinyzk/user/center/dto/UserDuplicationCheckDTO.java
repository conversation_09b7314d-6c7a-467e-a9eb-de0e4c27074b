package com.tinyzk.user.center.dto;

import lombok.Data;

/**
 * 用户去重检查DTO
 */
@Data
public class UserDuplicationCheckDTO {

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 身份证号
     */
    private String idCardNumber;

    /**
     * 检查结果
     */
    private CheckResult checkResult;

    /**
     * 检查结果
     */
    @Data
    public static class CheckResult {
        /**
         * 是否存在重复用户
         */
        private Boolean isDuplicate;

        /**
         * 重复用户ID
         */
        private Long duplicateUserId;

        /**
         * 重复匹配类型
         */
        private DuplicateMatchType matchType;

        /**
         * 匹配的标识符值
         */
        private String matchedIdentifier;
    }

    /**
     * 重复匹配类型枚举
     */
    public enum DuplicateMatchType {
        PHONE("手机号匹配"),
        EMAIL("邮箱匹配"),
        ID_CARD("身份证号匹配"),
        NAME_AND_ID_CARD("姓名和身份证号匹配");

        private final String description;

        DuplicateMatchType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
