package com.tinyzk.user.center.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.URL;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 创建用户工作经历数据传输对象
 */
@Data
@Schema(description = "创建用户工作经历请求")
public class CreateWorkDTO {

    @NotBlank(message = "公司名称不能为空")
    @Length(max = 255, message = "公司名称长度不能超过255个字符")
    @Schema(description = "公司/组织名称", example = "XXX科技有限公司")
    private String companyName;

    @URL(message = "公司Logo必须是有效的URL")
    @Length(max = 512, message = "公司Logo URL长度不能超过512个字符")
    @Schema(description = "公司/组织Logo URL(可选)", example = "https://example.com/logo.png")
    private String companyLogo;

    @URL(message = "公司官网必须是有效的URL")
    @Length(max = 512, message = "公司官网URL长度不能超过512个字符")
    @Schema(description = "公司/组织官网URL(可选)", example = "https://company.com")
    private String companyUrl;

    @Min(value = 1, message = "公司规模不能小于1")
    @Schema(description = "公司/组织规模", example = "500")
    private Integer companySize;

    @Length(max = 100, message = "公司行业长度不能超过100个字符")
    @Schema(description = "公司/组织行业", example = "互联网")
    private String companyIndustry;

    @Length(max = 100, message = "公司地点长度不能超过100个字符")
    @Schema(description = "公司/组织地点", example = "北京")
    private String companyLocation;

    @NotBlank(message = "职位不能为空")
    @Length(max = 100, message = "职位长度不能超过100个字符")
    @Schema(description = "职位/头衔", example = "高级后端工程师")
    private String positionName;

    @Length(max = 100, message = "部门长度不能超过100个字符")
    @Schema(description = "所属部门", example = "研发部")
    private String department;

    @NotNull(message = "入职日期不能为空")
    @Past(message = "入职日期必须是过去的日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "入职日期", example = "2020-01-01")
    private LocalDate startDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "离职日期(NULL表示在职)", example = "2023-01-01")
    private LocalDate endDate;

    @Length(max = 1000, message = "工作描述长度不能超过1000个字符")
    @Schema(description = "工作职责描述", example = "负责后端系统架构设计和开发")
    private String description;

    @Length(max = 1000, message = "工作业绩长度不能超过1000个字符")
    @Schema(description = "工作业绩/成果", example = "重构了核心服务，提高了系统性能30%")
    private String achievements;

    @Length(max = 100, message = "汇报对象长度不能超过100个字符")
    @Schema(description = "汇报对象", example = "技术总监")
    private String reportingTo;

    @Length(max = 500, message = "离职原因长度不能超过500个字符")
    @Schema(description = "离职原因", example = "寻求更好的职业发展")
    private String reasonForLeaving;
    
    @DecimalMin(value = "0.0", inclusive = true, message = "最小薪资不能小于0")
    @Schema(description = "薪资最小值", example = "10000")
    private BigDecimal salaryMin;
    
    @DecimalMin(value = "0.0", inclusive = true, message = "最大薪资不能小于0")
    @Schema(description = "薪资最大值", example = "20000")
    private BigDecimal salaryMax;

    @Min(value = 0, message = "认证方式不能小于0")
    @Max(value = 4, message = "认证方式不能大于4")
    @Schema(description = "认证方式(1-工牌、2-劳动合同、3-社保、4-个税)", example = "2")
    private Integer certificationType;

    @Min(value = 0, message = "认证状态不能小于0")
    @Max(value = 1, message = "认证状态不能大于1")
    @Schema(description = "认证状态(0-未认证、1-已认证)", example = "1")
    private Integer certificationStatus;

    @Schema(description = "可见性(PUBLIC-公开, FRIENDS-好友可见, PRIVATE-私密)", example = "PUBLIC")
    private String visibility;
} 