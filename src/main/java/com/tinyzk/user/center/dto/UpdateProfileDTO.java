package com.tinyzk.user.center.dto;

import com.tinyzk.user.center.common.enums.SpecialStatus;
import com.tinyzk.user.center.common.enums.PoliticalStatus;
import com.tinyzk.user.center.common.enums.MaritalStatus;
import com.tinyzk.user.center.common.enums.FertilityStatus;
import com.tinyzk.user.center.common.enums.HealthStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 更新用户个人资料数据传输对象
 */
@Data
@Schema(description = "更新用户个人资料数据传输对象")
public class UpdateProfileDTO {

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像URL")
    private String avatarUrl;

    @Schema(description = "性别, 0-未知, 1-男, 2-女")
    private Integer gender;

    @Schema(description = "生日")
    private LocalDate birthday;

    @Schema(description = "国籍")
    private String nationality;
    
    @Schema(description = "民族")
    private String ethnicity;
    
    @Schema(description = "特殊身份, 1-军人, 2-警察, 3-医生, 4-教师, 5-残疾人, 6-其他")
    private SpecialStatus specialStatus;
    
    @Schema(description = "政治面貌, 1-中共党员, 2-中共预备党员, 3-共青团员, 4-群众")
    private PoliticalStatus politicalStatus;
    
    @Schema(description = "婚姻状况, 1-未婚, 2-已婚, 3-离异, 4-丧偶")
    private MaritalStatus maritalStatus;
    
    @Schema(description = "生育情况, 1-未育, 2-已育, 3-已育一孩, 4-已育两孩及以上")
    private FertilityStatus fertilityStatus;
    
    @Schema(description = "健康状况, 1-健康, 2-良好, 3-一般, 4-较差")
    private HealthStatus healthStatus;
    
    @Schema(description = "地区编码")
    private String regionCode;
    
    @Schema(description = "地区名称")
    private String regionName;
    
    @Schema(description = "详细地址")
    private String address;
    
    @Schema(description = "个人简介")
    private String bio;

    // 注意：这里只包含允许用户修改的字段
    // 不允许用户直接修改 userId, createdAt, updatedAt, deletedAt 等字段
}