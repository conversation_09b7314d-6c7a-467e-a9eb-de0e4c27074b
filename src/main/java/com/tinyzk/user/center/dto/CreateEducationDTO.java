package com.tinyzk.user.center.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Past;
import java.time.LocalDate;

/**
 * 创建用户教育经历数据传输对象
 */
@Data
@Schema(description = "创建用户教育经历请求")
public class CreateEducationDTO {

    @NotBlank(message = "学校名称不能为空")
    @Length(max = 255, message = "学校名称长度不能超过255个字符")
    @Schema(description = "学校名称", example = "北京大学")
    private String schoolName;

    @NotBlank(message = "学位不能为空")
    @Length(max = 100, message = "学位长度不能超过100个字符")
    @Schema(description = "学位", example = "学士")
    private String degree;

    @Min(value = 1, message = "学位等级不能小于1")
    @Max(value = 5, message = "学位等级不能大于5")
    @Schema(description = "学位等级(1-本科, 2-硕士, 3-博士, 4-高中, 5-其他)", example = "1")
    private Integer degreeLevel;

    @Length(max = 100, message = "专业长度不能超过100个字符")
    @Schema(description = "专业", example = "计算机科学与技术")
    private String major;

    @Length(max = 100, message = "第二专业长度不能超过100个字符")
    @Schema(description = "第二专业(可选)", example = "数学")
    private String secondaryMajor;

    @Length(max = 100, message = "专业方向长度不能超过100个字符")
    @Schema(description = "专业方向(可选)", example = "人工智能")
    private String majorArea;

    @Min(value = 0, message = "专业GPA不能小于0")
    @Max(value = 5, message = "专业GPA不能大于5")
    @Schema(description = "专业GPA(可选)", example = "3.8")
    private Float majorGpa;

    @Past(message = "入学日期必须是过去的日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "入学日期", example = "2018-09-01")
    private LocalDate startDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "毕业/离校日期(NULL表示在读)", example = "2022-07-01")
    private LocalDate endDate;

    @Length(max = 1000, message = "描述长度不能超过1000个字符")
    @Schema(description = "描述/在校经历/荣誉等", example = "参加过多次学科竞赛，获得省级奖项")
    private String description;

    @Length(max = 500, message = "社团经历长度不能超过500个字符")
    @Schema(description = "社团经历", example = "学生会副主席，创业社社长")
    private String clubExperience;

    @Schema(description = "可见性(PUBLIC-公开, FRIENDS-好友可见, PRIVATE-私密)", example = "PUBLIC")
    private String visibility;
} 