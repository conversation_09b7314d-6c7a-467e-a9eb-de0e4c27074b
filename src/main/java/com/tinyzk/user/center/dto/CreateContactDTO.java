package com.tinyzk.user.center.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

/**
 * 创建用户联系方式DTO
 */
@Data
@Schema(description = "创建用户联系方式DTO")
public class CreateContactDTO {

    @NotNull(message = "联系方式类型不能为空")
    @Schema(description = "联系方式类型(1-备用邮箱, 2-工作电话, 3-紧急联系人, 4-微信, 5-QQ、6-即刻、7-小红书、8-微博、9-抖音、10-其他)", 
            required = true, example = "1")
    private Integer contactType;

    @NotEmpty(message = "联系方式值不能为空")
    @Schema(description = "联系方式的值(邮箱地址, 电话号码, URL, ID等)", required = true, example = "<EMAIL>")
    private String contactValue;

    @Schema(description = "用户自定义标签(1-备用邮箱, 2-工作电话, 3-紧急联系人, 4-社交账号)", example = "1")
    private Integer label;

    @Schema(description = "可见性(PUBLIC, FRIENDS, PRIVATE)", example = "PUBLIC")
    private String visibility;

    @Schema(description = "是否已验证", example = "false")
    private Boolean isVerified;
} 