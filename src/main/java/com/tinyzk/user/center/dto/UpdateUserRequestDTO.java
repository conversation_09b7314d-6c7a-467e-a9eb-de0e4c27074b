package com.tinyzk.user.center.dto;

import com.tinyzk.user.center.common.enums.SpecialStatus;
import com.tinyzk.user.center.common.enums.PoliticalStatus;
import com.tinyzk.user.center.common.enums.MaritalStatus;
import com.tinyzk.user.center.common.enums.FertilityStatus;
import com.tinyzk.user.center.common.enums.HealthStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 更新用户信息请求DTO
 */
@Data
public class UpdateUserRequestDTO {
    
    /**
     * 用户基础信息
     */
    @Schema(description = "用户基础信息")
    private UserBaseDTO userBase;
    
    /**
     * 用户资料信息
     */
    @Schema(description = "用户资料信息")
    private UserProfileDTO userProfile;
    
    /**
     * 用户基础信息DTO
     */
    @Data
    public static class UserBaseDTO {
        /**
         * 用户状态 (1-正常, 2-禁用)
         */
        @Schema(description = "用户状态")
        private Integer status;
        
        /**
         * 是否实名认证
         */
        @Schema(description = "是否实名认证")
        private Boolean realNameVerified;
    }
    
    /**
     * 用户资料信息DTO
     */
    @Data
    public static class UserProfileDTO {
        /**
         * 昵称
         */
        @Schema(description = "昵称")
        private String nickname;
        
        /**
         * 头像URL
         */
        @Schema(description = "头像URL")
        private String avatarUrl;
        
        /**
         * 性别 (1-男, 2-女, 0-未知)
         */
        @Schema(description = "性别")
        private Integer gender;
        
        /**
         * 生日
         */
        @Schema(description = "生日")
        private LocalDate birthday;
        
        /**
         * 个人简介
         */
        @Schema(description = "个人简介")
        private String bio;
        
        /**
         * 国籍
         */
        @Schema(description = "国籍")
        private String nationality;
        
        /**
         * 民族
         */
        @Schema(description = "民族")
        private String ethnicity;
        
        /**
         * 特殊身份状态
         */
        @Schema(description = "特殊身份状态")
        private SpecialStatus specialStatus;
        
        /**
         * 政治面貌
         */
        @Schema(description = "政治面貌")
        private PoliticalStatus politicalStatus;
        
        /**
         * 婚姻状况
         */
        @Schema(description = "婚姻状况")
        private MaritalStatus maritalStatus;
        
        /**
         * 生育状况
         */
        @Schema(description = "生育状况")
        private FertilityStatus fertilityStatus;
        
        /**
         * 健康状况
         */
        @Schema(description = "健康状况")
        private HealthStatus healthStatus;
        
        /**
         * 地区编码
         */
        @Schema(description = "地区编码")
        private String regionCode;
        
        /**
         * 地区名称
         */
        @Schema(description = "地区名称")
        private String regionName;
        
        /**
         * 详细地址
         */
        @Schema(description = "详细地址")
        private String address;
    }
}
