package com.tinyzk.user.center.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotNull;

/**
 * 简历解析请求DTO
 */
@Data
@Schema(description = "简历解析请求DTO")
public class ResumeParseRequestDTO {

    /**
     * 简历文件
     */
    @NotNull(message = "简历文件不能为空")
    @Schema(description = "简历文件", required = true)
    private MultipartFile file;

    /**
     * 是否覆盖现有数据
     */
    @Schema(description = "是否覆盖现有数据", defaultValue = "false")
    private Boolean overwriteExisting = false;

    /**
     * 解析选项 - 是否解析基本信息
     */
    @Schema(description = "是否解析基本信息", defaultValue = "true")
    private Boolean parseBasicInfo = true;

    /**
     * 解析选项 - 是否解析联系方式
     */
    @Schema(description = "是否解析联系方式", defaultValue = "true")
    private Boolean parseContactInfo = true;

    /**
     * 解析选项 - 是否解析教育经历
     */
    @Schema(description = "是否解析教育经历", defaultValue = "true")
    private Boolean parseEducation = true;

    /**
     * 解析选项 - 是否解析工作经历
     */
    @Schema(description = "是否解析工作经历", defaultValue = "true")
    private Boolean parseWorkExperience = true;

    /**
     * 解析选项 - 是否解析项目经历
     */
    @Schema(description = "是否解析项目经历", defaultValue = "true")
    private Boolean parseProjectExperience = true;

    /**
     * 解析选项 - 是否解析技能信息
     */
    @Schema(description = "是否解析技能信息", defaultValue = "true")
    private Boolean parseSkills = true;

    /**
     * 解析选项 - 是否解析培训经历
     */
    @Schema(description = "是否解析培训经历", defaultValue = "true")
    private Boolean parseTraining = true;

    /**
     * 解析选项 - 是否解析语言能力
     */
    @Schema(description = "是否解析语言能力", defaultValue = "true")
    private Boolean parseLanguages = true;

    /**
     * 解析选项 - 是否解析证书信息
     */
    @Schema(description = "是否解析证书信息", defaultValue = "true")
    private Boolean parseCertificates = true;

    /**
     * 解析选项 - 是否解析获奖记录
     */
    @Schema(description = "是否解析获奖记录", defaultValue = "true")
    private Boolean parseAwards = true;
}
