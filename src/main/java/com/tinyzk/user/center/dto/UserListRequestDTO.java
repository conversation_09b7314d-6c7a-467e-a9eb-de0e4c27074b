package com.tinyzk.user.center.dto;

import lombok.Data;

import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
/**
 * 用户列表查询请求DTO
 */
@Data
public class UserListRequestDTO {
    /**
     * 页码
     */
    @Schema(description = "页码", required = false, defaultValue = "1")
    @Min(value = 1, message = "页码必须大于0")
    @Max(value = 1000, message = "如有需要，请根据实际情况排序请求")
    private Integer pageNumber = 1;
    
    /**
     * 每页数量
     */
    @Schema(description = "每页数量", required = false, defaultValue = "20")
    @Min(value = 1, message = "每页数量必须大于0")
    @Max(value = 100, message = "每页数量不能超过100")
    private Integer pageSize = 20;
    
    /**
     * 用户ID (精确匹配)
     */
    @Schema(description = "用户ID", required = false)
    private Long userId;
    
    /**
     * 用户昵称 (模糊匹配)
     */
    @Schema(description = "用户昵称", required = false)
    private String nickname;
    
    /**
     * 登录标识 (如邮箱、手机号，模糊匹配)
     */
    @Schema(description = "登录标识", required = false)
    private String identifier;
    
    /**
     * 用户状态 (精确匹配, 1-正常, 2-禁用)
     */
    @Schema(description = "用户状态", required = false)
    private Integer status;
    
    /**
     * 实名认证状态 (精确匹配)
     */
    @Schema(description = "实名认证状态", required = false)
    private Boolean realNameVerified;
    
    /**
     * 注册时间晚于
     */
    @Schema(description = "注册时间晚于", required = false)
    private LocalDateTime registeredAfter;
    
    /**
     * 注册时间早于
     */
    @Schema(description = "注册时间早于", required = false)
    private LocalDateTime registeredBefore;
    
    /**
     * 最后登录时间晚于
     */
    @Schema(description = "最后登录时间晚于", required = false)
    private LocalDateTime lastLoginAfter;
    
    /**
     * 最后登录时间早于
     */
    @Schema(description = "最后登录时间早于", required = false)
    private LocalDateTime lastLoginBefore;
    
    /**
     * 排序字段 (e.g., 'userId', 'createdAt', 'lastLoginAt', 'nickname')
     */
    @Schema(description = "排序字段", required = false, defaultValue = "createdAt")
    private String sortBy = "createdAt";
    
    /**
     * 排序顺序 ('asc', 'desc')
     */
    @Schema(description = "排序顺序", required = false, defaultValue = "desc")
    private String sortOrder = "desc";
}
