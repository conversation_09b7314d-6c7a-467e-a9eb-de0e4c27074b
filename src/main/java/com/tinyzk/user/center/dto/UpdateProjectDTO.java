package com.tinyzk.user.center.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.URL;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.Past;
import java.time.LocalDate;

/**
 * 更新用户项目经历数据传输对象
 */
@Data
@Schema(description = "更新用户项目经历请求")
public class UpdateProjectDTO {

    @Length(max = 255, message = "项目名称长度不能超过255个字符")
    @Schema(description = "项目名称", example = "智能用户中心开发")
    private String projectName;

    @Length(max = 100, message = "项目角色长度不能超过100个字符")
    @Schema(description = "用户在项目中的角色/职责", example = "后端开发工程师")
    private String role;

    @Past(message = "项目开始日期必须是过去的日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "项目开始日期", example = "2022-01-01")
    private LocalDate startDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "项目结束日期(NULL表示进行中)", example = "2023-01-01")
    private LocalDate endDate;

    @Length(max = 1000, message = "项目描述长度不能超过1000个字符")
    @Schema(description = "项目描述(目标,用户贡献,使用技术,成果等)", example = "使用Spring Boot开发了一套用户资料管理系统，实现了用户基本信息、教育经历、工作经历等数据的管理")
    private String description;

    @URL(message = "项目链接必须是有效的URL")
    @Length(max = 512, message = "项目链接长度不能超过512个字符")
    @Schema(description = "项目链接(如: 代码仓库, 演示地址, 官网)", example = "https://github.com/username/project")
    private String projectUrl;

    @Length(max = 255, message = "关联组织/公司长度不能超过255个字符")
    @Schema(description = "关联组织/公司(可选)", example = "XXX科技有限公司")
    private String associatedOrganization;

    @Schema(description = "可见性(PUBLIC-公开, FRIENDS-好友可见, PRIVATE-私密)", example = "PUBLIC")
    private String visibility;
} 