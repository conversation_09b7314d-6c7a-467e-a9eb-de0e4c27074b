package com.tinyzk.user.center.dto;

import com.tinyzk.user.center.common.enums.IdentityType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 注册请求数据传输对象
 */
@Data
public class RegisterDTO {
    
    /**
     * 身份类型
     */
    @NotNull(message = "身份类型不能为空")
    @Schema(description = "身份类型")
    private IdentityType identityType;
    
    /**
     * 身份标识（如：openid、手机号、用户名等）
     */
    @NotBlank(message = "身份标识不能为空")
    @Schema(description = "身份标识")
    private String identifier;
    
    /**
     * 凭证（如：密码、验证码等）
     * 对于第三方登录，不需要凭证
     */
    @Schema(description = "凭证")
    private String credential;
    
    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickname;
    
    /**
     * 头像URL
     */
    @Schema(description = "头像URL")
    private String avatarUrl;
    
    /**
     * 外部用户ID
     */
    @Schema(description = "外部用户ID，可选，如果提供将创建用户与外部系统的映射关系")
    private String externalUserId;
}
