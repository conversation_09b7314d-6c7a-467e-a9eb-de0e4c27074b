package com.tinyzk.user.center.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 简历解析消息DTO
 */
@Data
public class ResumeParseMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 批次ID
     */
    private String batchId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * OSS对象键
     */
    private String ossKey;

    /**
     * 文件URL
     */
    private String fileUrl;

    /**
     * 优先级（1-10，数字越大优先级越高）
     */
    private Integer priority = 5;

    /**
     * 解析参数
     */
    private ParseParams parseParams;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 用户ID（可选）
     */
    private Long userId;

    /**
     * 租户ID（可选）
     */
    private String tenantId;

    /**
     * 解析参数
     */
    @Data
    public static class ParseParams implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 是否解析原始文本
         */
        private Boolean rawtext = true;

        /**
         * 是否处理图片
         */
        private Boolean handleImage = true;

        /**
         * 是否提取头像
         */
        private Boolean avatar = true;

        /**
         * 解析模式：fast/accurate
         */
        private String parseMode = "fast";

        /**
         * OCR模式：accurate/fast
         */
        private String ocrMode = "accurate";

        /**
         * OCR服务
         */
        private String ocrService = "OCR";
    }
}
