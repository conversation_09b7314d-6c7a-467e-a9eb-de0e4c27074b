package com.tinyzk.user.center.dto;

import com.tinyzk.user.center.common.enums.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 创建用户资料数据传输对象
 */
@Data
@Schema(description = "创建用户资料请求")
public class CreateProfileDTO {

    @Schema(description = "昵称", example = "小明")
    @Length(max = 100, message = "昵称长度不能超过100个字符")
    private String nickname;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    @Length(max = 512, message = "头像URL长度不能超过512个字符")
    private String avatarUrl;

    @Schema(description = "性别(0:未知,1:男,2:女)", example = "1")
    private Integer gender;

    @Schema(description = "出生日期", example = "1990-01-01")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;

    @Schema(description = "国籍", example = "中国")
    @Length(max = 100, message = "国籍长度不能超过100个字符")
    private String nationality;

    @Schema(description = "民族", example = "汉族")
    @Length(max = 100, message = "民族长度不能超过100个字符")
    private String ethnicity;

    @Schema(description = "特殊身份: SOLDIER-军人, POLICE-警察, DOCTOR-医生, TEACHER-教师, DISABLED-残疾人, OTHER-其他", example = "SOLDIER")
    private SpecialStatus specialStatus;

    @Schema(description = "政治面貌: CPC_MEMBER-中共党员, CPC_PREPARED_MEMBER-中共预备党员, YOUNG_PIONEER-共青团员, PUBLIC-群众", example = "CPC_MEMBER")
    private PoliticalStatus politicalStatus;

    @Schema(description = "婚姻状况: UNMARRIED-未婚, MARRIED-已婚, DIVORCED-离异, WIDOWER-丧偶", example = "UNMARRIED")
    private MaritalStatus maritalStatus;

    @Schema(description = "生育情况: UNBORN-未育, BORN-已育, BORN_ONE_CHILD-已育一孩, BORN_TWO_CHILDREN_OR_MORE-已育两孩及以上", example = "UNBORN")
    private FertilityStatus fertilityStatus;

    @Schema(description = "健康状况: HEALTHY-健康, GOOD-良好, AVERAGE-一般, POOR-较差", example = "HEALTHY")
    private HealthStatus healthStatus;

    @Schema(description = "地区编码", example = "110101")
    private String regionCode;

    @Schema(description = "地区名称", example = "北京市东城区")
    @Length(max = 100, message = "地区名称长度不能超过100个字符")
    private String regionName;

    @Schema(description = "详细地址", example = "xx街道xx小区")
    @Length(max = 255, message = "详细地址长度不能超过255个字符")
    private String address;

    @Schema(description = "个人简介", example = "热爱生活，积极向上")
    private String bio;
} 