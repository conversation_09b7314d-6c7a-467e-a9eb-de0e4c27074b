package com.tinyzk.user.center.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 更新用户联系方式DTO
 */
@Data
@Schema(description = "更新用户联系方式DTO")
public class UpdateContactDTO {

    @Schema(description = "联系方式的值(邮箱地址, 电话号码, URL, ID等)", example = "<EMAIL>")
    private String contactValue;

    @Schema(description = "用户自定义标签(1-备用邮箱, 2-工作电话, 3-紧急联系人, 4-社交账号)", example = "1")
    private Integer label;

    @Schema(description = "可见性(PUBLIC-公开, CONNECTIONS-仅限好友, PRIVATE-仅限自己)", example = "PUBLIC")
    private String visibility;

    @Schema(description = "是否已验证", example = "false")
    private Boolean isVerified;
} 