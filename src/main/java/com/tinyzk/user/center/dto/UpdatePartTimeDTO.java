package com.tinyzk.user.center.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 更新用户兼职经历DTO
 */
@Data
@Schema(description = "更新用户兼职经历DTO")
public class UpdatePartTimeDTO {

    @Schema(description = "兼职名称", example = "前端开发兼职")
    private String partTimeName;

    @Schema(description = "兼职类型(1-咨询, 2-设计, 3-开发, 4-运营, 5-销售, 6-客服, 7-服务员, 8-其他)", example = "3")
    private Integer partTimeType;

    @Schema(description = "兼职提供者/机构", example = "某科技公司")
    private String partTimeProvider;

    @Schema(description = "兼职地点", example = "北京市")
    private String partTimeLocation;

    @Schema(description = "兼职薪资", example = "5000")
    private BigDecimal partTimeSalary;

    @Schema(description = "服务周期（如：1个月, 3个月, 6个月, 1年）", example = "3个月")
    private String servicePeriod;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "开始日期（格式：yyyy-MM-dd）", example = "2023-01-01")
    private LocalDate startDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "结束日期（格式：yyyy-MM-dd，NULL表示进行中）", example = "2023-06-30")
    private LocalDate endDate;

    @Schema(description = "兼职描述", example = "负责公司官网前端页面开发和维护")
    private String description;

    @Schema(description = "可见性（PUBLIC, FRIENDS, PRIVATE）", example = "PUBLIC")
    private String visibility;
} 