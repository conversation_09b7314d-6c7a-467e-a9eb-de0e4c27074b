package com.tinyzk.user.center.scheduler;

import com.tinyzk.user.center.config.CacheWarmupConfig;
import com.tinyzk.user.center.service.CacheWarmupService;
import com.tinyzk.user.center.service.CacheWarmupMonitorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 缓存预热定时调度器
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "cache.warmup.schedule", name = "enabled", havingValue = "true", matchIfMissing = true)
public class CacheWarmupScheduler {
    
    private final CacheWarmupConfig warmupConfig;
    private final CacheWarmupService cacheWarmupService;
    private final CacheWarmupMonitorService monitorService;
    
    /**
     * 定时执行缓存预热
     * 使用配置文件中的cron表达式
     */
    @Scheduled(cron = "${cache.warmup.schedule.cron:0 0 2 * * ?}")
    public void scheduledWarmup() {
        if (!warmupConfig.isEnabled() || !warmupConfig.getSchedule().isEnabled()) {
            log.debug("定时缓存预热已禁用");
            return;
        }
        
        log.info("开始执行定时缓存预热...");
        
        try {
            cacheWarmupService.executeFullWarmup()
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.error("定时缓存预热失败", throwable);
                    } else {
                        log.info("定时缓存预热完成");
                    }
                });
                
        } catch (Exception e) {
            log.error("定时缓存预热执行异常", e);
        }
    }
    
    /**
     * 智能预热：基于缓存过期时间进行预热
     * 每5分钟检查一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void intelligentWarmup() {
        if (!warmupConfig.isEnabled()) {
            return;
        }
        
        try {
            // 检查热点数据的缓存状态
            checkAndWarmupHotData();
            
        } catch (Exception e) {
            log.error("智能预热执行异常", e);
        }
    }
    
    /**
     * 清理过期统计数据
     * 每天凌晨3点执行
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void cleanupExpiredStats() {
        try {
            log.info("开始清理过期统计数据...");
            monitorService.cleanupExpiredStats();
            log.info("清理过期统计数据完成");
            
        } catch (Exception e) {
            log.error("清理过期统计数据失败", e);
        }
    }
    
    /**
     * 检查并预热热点数据
     */
    private void checkAndWarmupHotData() {
        try {
            // 获取热点用户详情数据
            var hotUserDetails = monitorService.getHotData("userDetail", 50);
            
            for (String userId : hotUserDetails.keySet()) {
                String cacheKey = "userDetailCache::" + userId;
                
                // 检查缓存是否即将过期
                if (cacheWarmupService.isCacheNearExpiry(cacheKey)) {
                    log.debug("预热即将过期的用户详情缓存: userId={}", userId);
                    
                    // 异步预热
                    try {
                        Long userIdLong = Long.parseLong(userId);
                        cacheWarmupService.warmupSingleUserDetail(userIdLong);
                    } catch (NumberFormatException e) {
                        log.warn("无效的用户ID: {}", userId);
                    }
                }
            }
            
            // 获取热点用户列表数据
            var hotUserLists = monitorService.getHotData("userList", 20);
            
            for (String listKey : hotUserLists.keySet()) {
                String cacheKey = "userListCache::" + listKey;
                
                if (cacheWarmupService.isCacheNearExpiry(cacheKey)) {
                    log.debug("检测到即将过期的用户列表缓存: key={}", listKey);
                    // 这里可以根据需要实现用户列表的智能预热
                }
            }
            
        } catch (Exception e) {
            log.error("检查热点数据缓存状态失败", e);
        }
    }
    
    /**
     * 预热效果监控报告
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void warmupEffectivenessReport() {
        try {
            var stats = monitorService.getWarmupStats();
            var recommendations = monitorService.getWarmupRecommendations();
            
            log.info("缓存预热效果报告: stats={}", stats);
            log.debug("预热优化建议: {}", recommendations);
            
        } catch (Exception e) {
            log.error("生成预热效果报告失败", e);
        }
    }
}
