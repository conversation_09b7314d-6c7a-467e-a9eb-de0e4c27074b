package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.common.Result;
import com.tinyzk.user.center.common.util.AuthUtil;
import com.tinyzk.user.center.dto.CreateEducationDTO;
import com.tinyzk.user.center.dto.UpdateEducationDTO;
import com.tinyzk.user.center.service.UserEducationHistoryService;
import com.tinyzk.user.center.vo.UserEducationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 当前用户教育经历控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/me/educations")
@RequiredArgsConstructor
@Tag(name = "当前用户教育经历", description = "当前用户教育经历查询与管理相关接口")
public class UserEducationController {

    private final UserEducationHistoryService userEducationHistoryService;

    /**
     * 获取当前用户教育经历列表
     *
     * @return 教育经历列表
     */
    @GetMapping
    @Operation(summary = "获取当前用户教育经历列表", description = "获取当前登录用户的所有教育经历列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<List<UserEducationVO>> getCurrentUserEducations() {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("获取当前用户教育经历列表请求: userId={}", userId);
        List<UserEducationVO> educationVOs = userEducationHistoryService.getUserEducations(userId);
        return Result.success(educationVOs, "查询成功");
    }

    /**
     * 获取当前用户特定教育经历
     *
     * @param educationId 教育经历ID
     * @return 教育经历详情
     */
    @GetMapping("/{educationId}")
    @Operation(summary = "获取当前用户特定教育经历", description = "获取当前登录用户的某条特定教育经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "404", description = "教育经历不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserEducationVO> getCurrentUserEducation(
            @Parameter(description = "教育经历ID", required = true)
            @PathVariable Long educationId) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("获取当前用户特定教育经历请求: userId={}, educationId={}", userId, educationId);
        UserEducationVO educationVO = userEducationHistoryService.getUserEducation(userId, educationId);
        return Result.success(educationVO, "查询成功");
    }

    /**
     * 创建当前用户教育经历
     *
     * @param createEducationDTO 创建教育经历信息
     * @return 创建后的教育经历
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "创建当前用户教育经历", description = "为当前登录用户添加一条新的教育经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "创建成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserEducationVO> createCurrentUserEducation(
            @Parameter(description = "创建的教育经历信息", required = true)
            @Validated @RequestBody CreateEducationDTO createEducationDTO) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("创建当前用户教育经历请求: userId={}, data={}", userId, createEducationDTO);
        UserEducationVO createdEducation = userEducationHistoryService.createUserEducation(userId, createEducationDTO);
        return Result.success(createdEducation, "创建成功");
    }

    /**
     * 更新当前用户教育经历
     *
     * @param educationId        教育经历ID
     * @param updateEducationDTO 更新信息
     * @return 更新后的教育经历
     */
    @PutMapping("/{educationId}")
    @Operation(summary = "更新当前用户教育经历", description = "更新当前登录用户的某条特定教育经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "更新成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "教育经历不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserEducationVO> updateCurrentUserEducation(
            @Parameter(description = "教育经历ID", required = true)
            @PathVariable Long educationId,
            @Parameter(description = "更新的教育经历信息", required = true)
            @Validated @RequestBody UpdateEducationDTO updateEducationDTO) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("更新当前用户教育经历请求: userId={}, educationId={}, data={}", userId, educationId, updateEducationDTO);
        UserEducationVO updatedEducation = userEducationHistoryService.updateUserEducation(userId, educationId, updateEducationDTO);
        return Result.success(updatedEducation, "更新成功");
    }

    /**
     * 删除当前用户教育经历
     *
     * @param educationId 教育经历ID
     * @return 删除结果
     */
    @DeleteMapping("/{educationId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Operation(summary = "删除当前用户教育经历", description = "删除当前登录用户的某条特定教育经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "删除成功"),
            @ApiResponse(responseCode = "404", description = "教育经历不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Void> deleteCurrentUserEducation(
            @Parameter(description = "教育经历ID", required = true)
            @PathVariable Long educationId) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("删除当前用户教育经历请求: userId={}, educationId={}", userId, educationId);
        userEducationHistoryService.deleteUserEducation(userId, educationId);
        return Result.success(null, "删除成功");
    }
} 