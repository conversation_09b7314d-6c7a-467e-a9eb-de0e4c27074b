package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.common.Result;
import com.tinyzk.user.center.common.util.AuthUtil;
import com.tinyzk.user.center.dto.CreateContactDTO;
import com.tinyzk.user.center.dto.UpdateContactDTO;
import com.tinyzk.user.center.service.UserContactService;
import com.tinyzk.user.center.vo.UserContactVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 当前用户联系方式控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/me/contacts")
@RequiredArgsConstructor
@Tag(name = "当前用户联系方式", description = "当前用户联系方式查询与管理相关接口")
public class UserContactController {

    private final UserContactService userContactService;

    /**
     * 获取当前用户联系方式列表
     *
     * @return 联系方式列表
     */
    @GetMapping
    @Operation(summary = "获取当前用户联系方式列表", description = "获取当前登录用户的所有联系方式列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<List<UserContactVO>> getCurrentUserContacts() {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("获取当前用户联系方式列表请求: userId={}", userId);
        List<UserContactVO> contactVOs = userContactService.getUserContacts(userId);
        return Result.success(contactVOs, "查询成功");
    }

    /**
     * 获取当前用户特定联系方式
     *
     * @param contactId 联系方式ID
     * @return 联系方式详情
     */
    @GetMapping("/{contactId}")
    @Operation(summary = "获取当前用户特定联系方式", description = "获取当前登录用户的某条特定联系方式")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "404", description = "联系方式不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserContactVO> getCurrentUserContact(
            @Parameter(description = "联系方式ID", required = true)
            @PathVariable Long contactId) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("获取当前用户特定联系方式请求: userId={}, contactId={}", userId, contactId);
        UserContactVO contactVO = userContactService.getUserContact(userId, contactId);
        return Result.success(contactVO, "查询成功");
    }

    /**
     * 创建当前用户联系方式
     *
     * @param createContactDTO 创建联系方式信息
     * @return 创建后的联系方式
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "创建当前用户联系方式", description = "为当前登录用户添加一条新的联系方式")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "创建成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserContactVO> createCurrentUserContact(
            @Parameter(description = "创建的联系方式信息", required = true)
            @Validated @RequestBody CreateContactDTO createContactDTO) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("创建当前用户联系方式请求: userId={}, data={}", userId, createContactDTO);
        UserContactVO createdContact = userContactService.createUserContact(userId, createContactDTO);
        return Result.success(createdContact, "创建成功");
    }

    /**
     * 更新当前用户联系方式
     *
     * @param contactId       联系方式ID
     * @param updateContactDTO 更新信息
     * @return 更新后的联系方式
     */
    @PutMapping("/{contactId}")
    @Operation(summary = "更新当前用户联系方式", description = "更新当前登录用户的某条特定联系方式")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "更新成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "联系方式不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserContactVO> updateCurrentUserContact(
            @Parameter(description = "联系方式ID", required = true)
            @PathVariable Long contactId,
            @Parameter(description = "更新的联系方式信息", required = true)
            @Validated @RequestBody UpdateContactDTO updateContactDTO) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("更新当前用户联系方式请求: userId={}, contactId={}, data={}", userId, contactId, updateContactDTO);
        UserContactVO updatedContact = userContactService.updateUserContact(userId, contactId, updateContactDTO);
        return Result.success(updatedContact, "更新成功");
    }

    /**
     * 删除当前用户联系方式
     *
     * @param contactId 联系方式ID
     * @return 删除结果
     */
    @DeleteMapping("/{contactId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Operation(summary = "删除当前用户联系方式", description = "删除当前登录用户的某条特定联系方式")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "删除成功"),
            @ApiResponse(responseCode = "404", description = "联系方式不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Void> deleteCurrentUserContact(
            @Parameter(description = "联系方式ID", required = true)
            @PathVariable Long contactId) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("删除当前用户联系方式请求: userId={}, contactId={}", userId, contactId);
        userContactService.deleteUserContact(userId, contactId);
        return Result.success(null, "删除成功");
    }
} 