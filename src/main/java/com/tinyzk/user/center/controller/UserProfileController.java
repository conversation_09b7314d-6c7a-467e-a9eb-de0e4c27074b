package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.common.Result;
import com.tinyzk.user.center.common.annotation.DataPermission;
import com.tinyzk.user.center.common.util.AuthUtil;
import com.tinyzk.user.center.dto.CreateProfileDTO;
import com.tinyzk.user.center.dto.UpdateProfileDTO;
import com.tinyzk.user.center.service.UserProfileService;
import com.tinyzk.user.center.vo.UserProfileVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户个人资料控制器 (当前登录用户操作)
 * 使用用户端权限验证模式
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/me/profile")
@RequiredArgsConstructor
@Tag(name = "当前用户个人资料", description = "当前登录用户个人资料查询与管理相关接口")
public class UserProfileController {

    private final UserProfileService userProfileService;

    /**
     * 查询当前用户个人资料
     *
     * @return 用户个人资料
     */
    @GetMapping
    @DataPermission(mode = DataPermission.Mode.USER)  // 用户端模式
    @Operation(summary = "查询当前用户个人资料", description = "查询当前登录用户的详细个人资料")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = UserProfileVO.class))),
            @ApiResponse(responseCode = "403", description = "数据访问权限不足"),
            @ApiResponse(responseCode = "404", description = "用户资料不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserProfileVO> getCurrentUserProfile() {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("查询当前用户个人资料请求: userId={}", userId);
        UserProfileVO userProfileVO = userProfileService.getUserProfile(userId);
        if (userProfileVO == null) {
            return Result.error("用户资料不存在");
        }
        return Result.success(userProfileVO, "查询成功");
    }

    /**
     * 创建当前用户个人资料
     *
     * @param createProfileDTO 创建资料信息
     * @return 创建后的用户个人资料
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @DataPermission(mode = DataPermission.Mode.USER)  // 用户端模式
    @Operation(summary = "创建当前用户个人资料", description = "为当前登录用户创建个人资料信息(幂等操作)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "创建成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = UserProfileVO.class))),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "403", description = "数据访问权限不足"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserProfileVO> createCurrentUserProfile(
            @Parameter(description = "创建的个人资料信息", required = true) 
            @Validated @RequestBody CreateProfileDTO createProfileDTO) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("创建当前用户个人资料请求: userId={}, data={}", userId, createProfileDTO);
        UserProfileVO createdProfile = userProfileService.createUserProfile(userId, createProfileDTO);
        return Result.success(createdProfile, "创建成功");
    }

    /**
     * 更新当前用户个人资料
     *
     * @param updateProfileDTO 更新信息
     * @return 更新后的用户个人资料
     */
    @PutMapping
    @DataPermission(mode = DataPermission.Mode.USER)  // 用户端模式
    @Operation(summary = "更新当前用户个人资料", description = "更新当前登录用户的个人资料信息(幂等操作)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "更新成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = UserProfileVO.class))),
            @ApiResponse(responseCode = "201", description = "首次创建成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = UserProfileVO.class))),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "403", description = "数据访问权限不足"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserProfileVO> updateCurrentUserProfile(
            @Parameter(description = "更新的个人资料信息", required = true) 
            @Validated @RequestBody UpdateProfileDTO updateProfileDTO) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("更新当前用户个人资料请求: userId={}, data={}", userId, updateProfileDTO);
        UserProfileVO updatedProfile = userProfileService.updateUserProfile(userId, updateProfileDTO);
        return Result.success(updatedProfile, "更新成功");
    }

    /**
     * 删除当前用户个人资料
     *
     * @return 删除结果
     */
    @DeleteMapping
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @DataPermission(mode = DataPermission.Mode.USER)  // 用户端模式
    @Operation(summary = "删除当前用户个人资料", description = "删除当前登录用户的个人资料信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "删除成功"),
            @ApiResponse(responseCode = "403", description = "数据访问权限不足"),
            @ApiResponse(responseCode = "404", description = "用户资料不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Void> deleteCurrentUserProfile() {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("删除当前用户个人资料请求: userId={}", userId);
        userProfileService.deleteUserProfile(userId);
        return Result.success(null, "删除成功");
    }
}