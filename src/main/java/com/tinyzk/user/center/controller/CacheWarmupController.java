package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.common.Result;
import com.tinyzk.user.center.service.CacheWarmupService;
import com.tinyzk.user.center.service.CacheWarmupMonitorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 缓存预热管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/cache/warmup")
@RequiredArgsConstructor
@Tag(name = "缓存预热管理", description = "缓存预热相关接口")
public class CacheWarmupController {
    
    private final CacheWarmupService cacheWarmupService;
    private final CacheWarmupMonitorService monitorService;
    
    /**
     * 手动触发完整缓存预热
     */
    @PostMapping("/execute")
    @Operation(summary = "执行完整缓存预热", description = "手动触发完整的缓存预热流程")
    public Result<String> executeWarmup() {
        log.info("手动触发缓存预热");
        
        try {
            CompletableFuture<Void> future = cacheWarmupService.executeFullWarmup();
            
            // 异步执行，立即返回
            future.whenComplete((result, throwable) -> {
                if (throwable != null) {
                    log.error("手动缓存预热失败", throwable);
                } else {
                    log.info("手动缓存预热完成");
                }
            });
            
            return Result.success("缓存预热已启动，请查看日志获取执行状态");
            
        } catch (Exception e) {
            log.error("启动缓存预热失败", e);
            return Result.error("启动缓存预热失败: " + e.getMessage());
        }
    }
    
    /**
     * 预热用户详情缓存
     */
    @PostMapping("/user-details")
    @Operation(summary = "预热用户详情缓存", description = "预热最近活跃用户的详情信息")
    public Result<String> warmupUserDetails() {
        log.info("手动触发用户详情缓存预热");
        
        try {
            CompletableFuture<Void> future = cacheWarmupService.warmupUserDetails();
            
            future.whenComplete((result, throwable) -> {
                if (throwable != null) {
                    log.error("用户详情缓存预热失败", throwable);
                } else {
                    log.info("用户详情缓存预热完成");
                }
            });
            
            return Result.success("用户详情缓存预热已启动");
            
        } catch (Exception e) {
            log.error("启动用户详情缓存预热失败", e);
            return Result.error("启动用户详情缓存预热失败: " + e.getMessage());
        }
    }
    
    /**
     * 预热用户列表缓存
     */
    @PostMapping("/user-lists")
    @Operation(summary = "预热用户列表缓存", description = "预热常用的用户列表查询结果")
    public Result<String> warmupUserLists() {
        log.info("手动触发用户列表缓存预热");
        
        try {
            CompletableFuture<Void> future = cacheWarmupService.warmupUserLists();
            
            future.whenComplete((result, throwable) -> {
                if (throwable != null) {
                    log.error("用户列表缓存预热失败", throwable);
                } else {
                    log.info("用户列表缓存预热完成");
                }
            });
            
            return Result.success("用户列表缓存预热已启动");
            
        } catch (Exception e) {
            log.error("启动用户列表缓存预热失败", e);
            return Result.error("启动用户列表缓存预热失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取预热统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取预热统计信息", description = "获取缓存预热的统计数据和效果分析")
    public Result<Map<String, Object>> getWarmupStats() {
        try {
            Map<String, Object> stats = monitorService.getWarmupStats();
            return Result.success(stats);
            
        } catch (Exception e) {
            log.error("获取预热统计信息失败", e);
            return Result.error("获取预热统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取缓存命中率统计
     */
    @GetMapping("/hit-rate")
    @Operation(summary = "获取缓存命中率", description = "获取缓存的命中率统计信息")
    public Result<Map<String, Object>> getCacheHitRate() {
        try {
            Map<String, Object> hitRateStats = monitorService.getCacheHitRateStats();
            return Result.success(hitRateStats);
            
        } catch (Exception e) {
            log.error("获取缓存命中率统计失败", e);
            return Result.error("获取缓存命中率统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取热点数据
     */
    @GetMapping("/hot-data")
    @Operation(summary = "获取热点数据", description = "获取访问频率最高的缓存数据")
    public Result<Map<String, Object>> getHotData(
            @RequestParam(defaultValue = "userDetail") String cacheType,
            @RequestParam(defaultValue = "50") int limit) {
        try {
            Map<String, Double> hotData = monitorService.getHotData(cacheType, limit);
            
            Map<String, Object> result = Map.of(
                "cache_type", cacheType,
                "limit", limit,
                "hot_data", hotData
            );
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取热点数据失败", e);
            return Result.error("获取热点数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取预热建议
     */
    @GetMapping("/recommendations")
    @Operation(summary = "获取预热建议", description = "基于访问模式分析的预热优化建议")
    public Result<Map<String, Object>> getWarmupRecommendations() {
        try {
            Map<String, Object> recommendations = monitorService.getWarmupRecommendations();
            return Result.success(recommendations);
            
        } catch (Exception e) {
            log.error("获取预热建议失败", e);
            return Result.error("获取预热建议失败: " + e.getMessage());
        }
    }
    
    /**
     * 清理过期统计数据
     */
    @DeleteMapping("/cleanup")
    @Operation(summary = "清理过期数据", description = "清理过期的预热统计数据")
    public Result<String> cleanupExpiredStats() {
        try {
            monitorService.cleanupExpiredStats();
            return Result.success("过期统计数据清理完成");
            
        } catch (Exception e) {
            log.error("清理过期统计数据失败", e);
            return Result.error("清理过期统计数据失败: " + e.getMessage());
        }
    }
}
