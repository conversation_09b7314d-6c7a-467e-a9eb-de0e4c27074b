package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.common.Result;
import com.tinyzk.user.center.common.util.AuthUtil;
import com.tinyzk.user.center.dto.CreatePartTimeDTO;
import com.tinyzk.user.center.dto.UpdatePartTimeDTO;
import com.tinyzk.user.center.service.UserPartTimeService;
import com.tinyzk.user.center.vo.UserPartTimeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 当前用户兼职经历控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/me/part_times")
@RequiredArgsConstructor
@Tag(name = "当前用户兼职经历", description = "当前用户兼职经历查询与管理相关接口")
public class UserPartTimeController {

    private final UserPartTimeService userPartTimeService;

    /**
     * 获取当前用户兼职经历列表
     *
     * @return 兼职经历列表
     */
    @GetMapping
    @Operation(summary = "获取当前用户兼职经历列表", description = "获取当前登录用户的所有兼职经历列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<List<UserPartTimeVO>> getCurrentUserPartTimes() {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("获取当前用户兼职经历列表请求: userId={}", userId);
        List<UserPartTimeVO> partTimeVOs = userPartTimeService.getUserPartTimes(userId);
        return Result.success(partTimeVOs, "查询成功");
    }

    /**
     * 获取当前用户特定兼职经历
     *
     * @param partTimeId 兼职经历ID
     * @return 兼职经历详情
     */
    @GetMapping("/{partTimeId}")
    @Operation(summary = "获取当前用户特定兼职经历", description = "获取当前登录用户的某条特定兼职经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "404", description = "兼职经历不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserPartTimeVO> getCurrentUserPartTime(
            @Parameter(description = "兼职经历ID", required = true)
            @PathVariable Long partTimeId) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("获取当前用户特定兼职经历请求: userId={}, partTimeId={}", userId, partTimeId);
        UserPartTimeVO partTimeVO = userPartTimeService.getUserPartTime(userId, partTimeId);
        return Result.success(partTimeVO, "查询成功");
    }

    /**
     * 创建当前用户兼职经历
     *
     * @param createPartTimeDTO 创建兼职经历信息
     * @return 创建后的兼职经历
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "创建当前用户兼职经历", description = "为当前登录用户添加一条新的兼职经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "创建成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserPartTimeVO> createCurrentUserPartTime(
            @Parameter(description = "创建的兼职经历信息", required = true)
            @Validated @RequestBody CreatePartTimeDTO createPartTimeDTO) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("创建当前用户兼职经历请求: userId={}, data={}", userId, createPartTimeDTO);
        UserPartTimeVO createdPartTime = userPartTimeService.createUserPartTime(userId, createPartTimeDTO);
        return Result.success(createdPartTime, "创建成功");
    }

    /**
     * 更新当前用户兼职经历
     *
     * @param partTimeId        兼职经历ID
     * @param updatePartTimeDTO 更新信息
     * @return 更新后的兼职经历
     */
    @PutMapping("/{partTimeId}")
    @Operation(summary = "更新当前用户兼职经历", description = "更新当前登录用户的某条特定兼职经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "更新成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "兼职经历不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserPartTimeVO> updateCurrentUserPartTime(
            @Parameter(description = "兼职经历ID", required = true)
            @PathVariable Long partTimeId,
            @Parameter(description = "更新的兼职经历信息", required = true)
            @Validated @RequestBody UpdatePartTimeDTO updatePartTimeDTO) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("更新当前用户兼职经历请求: userId={}, partTimeId={}, data={}", userId, partTimeId, updatePartTimeDTO);
        UserPartTimeVO updatedPartTime = userPartTimeService.updateUserPartTime(userId, partTimeId, updatePartTimeDTO);
        return Result.success(updatedPartTime, "更新成功");
    }

    /**
     * 删除当前用户兼职经历
     *
     * @param partTimeId 兼职经历ID
     * @return 删除结果
     */
    @DeleteMapping("/{partTimeId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Operation(summary = "删除当前用户兼职经历", description = "删除当前登录用户的某条特定兼职经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "删除成功"),
            @ApiResponse(responseCode = "404", description = "兼职经历不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Void> deleteCurrentUserPartTime(
            @Parameter(description = "兼职经历ID", required = true)
            @PathVariable Long partTimeId) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("删除当前用户兼职经历请求: userId={}, partTimeId={}", userId, partTimeId);
        userPartTimeService.deleteUserPartTime(userId, partTimeId);
        return Result.success(null, "删除成功");
    }
} 