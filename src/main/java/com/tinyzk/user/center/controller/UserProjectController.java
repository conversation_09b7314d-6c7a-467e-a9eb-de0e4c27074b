package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.common.Result;
import com.tinyzk.user.center.common.util.AuthUtil;
import com.tinyzk.user.center.dto.CreateProjectDTO;
import com.tinyzk.user.center.dto.UpdateProjectDTO;
import com.tinyzk.user.center.service.UserProjectHistoryService;
import com.tinyzk.user.center.vo.UserProjectVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 当前用户项目经历控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/me/projects")
@RequiredArgsConstructor
@Tag(name = "当前用户项目经历", description = "当前用户项目经历查询与管理相关接口")
public class UserProjectController {

    private final UserProjectHistoryService userProjectHistoryService;

    /**
     * 获取当前用户项目经历列表
     *
     * @return 项目经历列表
     */
    @GetMapping
    @Operation(summary = "获取当前用户项目经历列表", description = "获取当前登录用户的所有项目经历列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<List<UserProjectVO>> getCurrentUserProjects() {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("获取当前用户项目经历列表请求: userId={}", userId);
        List<UserProjectVO> projectVOs = userProjectHistoryService.getUserProjects(userId);
        return Result.success(projectVOs, "查询成功");
    }

    /**
     * 获取当前用户特定项目经历
     *
     * @param projectId 项目经历ID
     * @return 项目经历详情
     */
    @GetMapping("/{projectId}")
    @Operation(summary = "获取当前用户特定项目经历", description = "获取当前登录用户的某条特定项目经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "404", description = "项目经历不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserProjectVO> getCurrentUserProject(
            @Parameter(description = "项目经历ID", required = true)
            @PathVariable Long projectId) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("获取当前用户特定项目经历请求: userId={}, projectId={}", userId, projectId);
        UserProjectVO projectVO = userProjectHistoryService.getUserProject(userId, projectId);
        return Result.success(projectVO, "查询成功");
    }

    /**
     * 创建当前用户项目经历
     *
     * @param createProjectDTO 创建项目经历信息
     * @return 创建后的项目经历
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "创建当前用户项目经历", description = "为当前登录用户添加一条新的项目经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "创建成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserProjectVO> createCurrentUserProject(
            @Parameter(description = "创建的项目经历信息", required = true)
            @Validated @RequestBody CreateProjectDTO createProjectDTO) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("创建当前用户项目经历请求: userId={}, data={}", userId, createProjectDTO);
        UserProjectVO createdProject = userProjectHistoryService.createUserProject(userId, createProjectDTO);
        return Result.success(createdProject, "创建成功");
    }

    /**
     * 更新当前用户项目经历
     *
     * @param projectId       项目经历ID
     * @param updateProjectDTO 更新信息
     * @return 更新后的项目经历
     */
    @PutMapping("/{projectId}")
    @Operation(summary = "更新当前用户项目经历", description = "更新当前登录用户的某条特定项目经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "更新成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "项目经历不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserProjectVO> updateCurrentUserProject(
            @Parameter(description = "项目经历ID", required = true)
            @PathVariable Long projectId,
            @Parameter(description = "更新的项目经历信息", required = true)
            @Validated @RequestBody UpdateProjectDTO updateProjectDTO) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("更新当前用户项目经历请求: userId={}, projectId={}, data={}", userId, projectId, updateProjectDTO);
        UserProjectVO updatedProject = userProjectHistoryService.updateUserProject(userId, projectId, updateProjectDTO);
        return Result.success(updatedProject, "更新成功");
    }

    /**
     * 删除当前用户项目经历
     *
     * @param projectId 项目经历ID
     * @return 删除结果
     */
    @DeleteMapping("/{projectId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Operation(summary = "删除当前用户项目经历", description = "删除当前登录用户的某条特定项目经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "删除成功"),
            @ApiResponse(responseCode = "404", description = "项目经历不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Void> deleteCurrentUserProject(
            @Parameter(description = "项目经历ID", required = true)
            @PathVariable Long projectId) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("删除当前用户项目经历请求: userId={}, projectId={}", userId, projectId);
        userProjectHistoryService.deleteUserProject(userId, projectId);
        return Result.success(null, "删除成功");
    }
} 