package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.common.Result;
import com.tinyzk.user.center.common.annotation.DataPermission;
import com.tinyzk.user.center.common.util.AuthUtil;
import com.tinyzk.user.center.dto.ResumeParseRequestDTO;
import com.tinyzk.user.center.service.ResumeParseService;
import com.tinyzk.user.center.service.EnhancedResumeParseService;
import com.tinyzk.user.center.service.BatchProcessingTrackingService;
import com.tinyzk.user.center.vo.ResumeParseResultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 简历解析控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/me/resume")
@RequiredArgsConstructor
@Validated
@Tag(name = "简历解析管理", description = "用户简历文件上传和解析相关接口")
public class ResumeParseController {

    private final ResumeParseService resumeParseService;
    private final EnhancedResumeParseService enhancedResumeParseService;
    private final BatchProcessingTrackingService trackingService;

    /**
     * 上传并解析简历文件
     *
     * @param file 简历文件
     * @param overwriteExisting 是否覆盖现有数据
     * @param parseBasicInfo 是否解析基本信息
     * @param parseContactInfo 是否解析联系方式
     * @param parseEducation 是否解析教育经历
     * @param parseWorkExperience 是否解析工作经历
     * @param parseProjectExperience 是否解析项目经历
     * @param parseSkills 是否解析技能信息
     * @param parseTraining 是否解析培训经历
     * @param parseLanguages 是否解析语言能力
     * @param parseCertificates 是否解析证书信息
     * @param parseAwards 是否解析获奖记录
     * @return 解析结果
     */
    @PostMapping(value = "/parse", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    @DataPermission(mode = DataPermission.Mode.USER)
    @Operation(summary = "上传并解析简历文件", 
               description = "上传简历文件(支持doc、docx、pdf格式)并调用第三方服务进行解析，将解析结果存储到用户资料中")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "解析成功",
                    content = @Content(mediaType = "application/json", 
                                     schema = @Schema(implementation = ResumeParseResultVO.class))),
            @ApiResponse(responseCode = "400", description = "参数错误或文件格式不支持"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "413", description = "文件过大"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<ResumeParseResultVO> parseResume(
            @Parameter(description = "简历文件", required = true)
            @RequestParam("file") @NotNull MultipartFile file,
            
            @Parameter(description = "是否覆盖现有数据", example = "false")
            @RequestParam(value = "overwriteExisting", defaultValue = "false") Boolean overwriteExisting,
            
            @Parameter(description = "是否解析基本信息", example = "true")
            @RequestParam(value = "parseBasicInfo", defaultValue = "true") Boolean parseBasicInfo,
            
            @Parameter(description = "是否解析联系方式", example = "true")
            @RequestParam(value = "parseContactInfo", defaultValue = "true") Boolean parseContactInfo,
            
            @Parameter(description = "是否解析教育经历", example = "true")
            @RequestParam(value = "parseEducation", defaultValue = "true") Boolean parseEducation,
            
            @Parameter(description = "是否解析工作经历", example = "true")
            @RequestParam(value = "parseWorkExperience", defaultValue = "true") Boolean parseWorkExperience,
            
            @Parameter(description = "是否解析项目经历", example = "true")
            @RequestParam(value = "parseProjectExperience", defaultValue = "true") Boolean parseProjectExperience,
            
            @Parameter(description = "是否解析技能信息", example = "true")
            @RequestParam(value = "parseSkills", defaultValue = "true") Boolean parseSkills,
            
            @Parameter(description = "是否解析培训经历", example = "true")
            @RequestParam(value = "parseTraining", defaultValue = "true") Boolean parseTraining,
            
            @Parameter(description = "是否解析语言能力", example = "true")
            @RequestParam(value = "parseLanguages", defaultValue = "true") Boolean parseLanguages,
            
            @Parameter(description = "是否解析证书信息", example = "true")
            @RequestParam(value = "parseCertificates", defaultValue = "true") Boolean parseCertificates,
            
            @Parameter(description = "是否解析获奖记录", example = "true")
            @RequestParam(value = "parseAwards", defaultValue = "true") Boolean parseAwards) {
        
        Long userId = AuthUtil.getCurrentUserId();
        log.info("用户 {} 上传简历文件进行解析: filename={}, size={}", 
                userId, file.getOriginalFilename(), file.getSize());

        // 构建请求DTO
        ResumeParseRequestDTO requestDTO = new ResumeParseRequestDTO();
        requestDTO.setFile(file);
        requestDTO.setOverwriteExisting(overwriteExisting);
        requestDTO.setParseBasicInfo(parseBasicInfo);
        requestDTO.setParseContactInfo(parseContactInfo);
        requestDTO.setParseEducation(parseEducation);
        requestDTO.setParseWorkExperience(parseWorkExperience);
        requestDTO.setParseProjectExperience(parseProjectExperience);
        requestDTO.setParseSkills(parseSkills);
        requestDTO.setParseTraining(parseTraining);
        requestDTO.setParseLanguages(parseLanguages);
        requestDTO.setParseCertificates(parseCertificates);
        requestDTO.setParseAwards(parseAwards);

        ResumeParseResultVO result = resumeParseService.parseResume(userId, requestDTO);
        return Result.success(result, "简历解析完成");
    }

    /**
     * 获取当前用户的简历解析记录列表
     *
     * @return 解析记录列表
     */
    @GetMapping("/parse-records")
    @DataPermission(mode = DataPermission.Mode.USER)
    @Operation(summary = "获取简历解析记录", description = "获取当前用户的所有简历解析记录")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<List<ResumeParseResultVO>> getParseRecords() {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("获取用户 {} 的简历解析记录", userId);
        
        List<ResumeParseResultVO> records = resumeParseService.getParseRecords(userId);
        return Result.success(records, "查询成功");
    }

    /**
     * 获取特定解析记录详情
     *
     * @param recordId 解析记录ID
     * @return 解析记录详情
     */
    @GetMapping("/parse-records/{recordId}")
    @DataPermission(mode = DataPermission.Mode.USER)
    @Operation(summary = "获取解析记录详情", description = "获取特定简历解析记录的详细信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "404", description = "记录不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<ResumeParseResultVO> getParseRecord(
            @Parameter(description = "解析记录ID", required = true)
            @PathVariable Long recordId) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("获取用户 {} 的解析记录详情: recordId={}", userId, recordId);
        
        ResumeParseResultVO record = resumeParseService.getParseRecord(userId, recordId);
        return Result.success(record, "查询成功");
    }

    /**
     * 异步上传并解析简历文件（新架构）
     */
    @PostMapping(value = "/parse-async", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseStatus(HttpStatus.ACCEPTED)
    @DataPermission(mode = DataPermission.Mode.USER)
    @Operation(summary = "异步上传并解析简历文件",
               description = "使用新的异步架构上传简历文件并进行解析，返回任务ID用于查询进度")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "202", description = "任务已提交"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<AsyncParseResultVO> parseResumeAsync(
            @Parameter(description = "简历文件", required = true)
            @RequestParam("file") @NotNull MultipartFile file,

            @Parameter(description = "是否覆盖现有数据", example = "false")
            @RequestParam(value = "overwriteExisting", defaultValue = "false") Boolean overwriteExisting) {

        Long userId = AuthUtil.getCurrentUserId();
        log.info("用户 {} 异步上传简历文件进行解析: filename={}, size={}",
                userId, file.getOriginalFilename(), file.getSize());

        try {
            // 生成任务ID
            String taskId = trackingService.generateBatchId();

            // 初始化进度跟踪
            trackingService.initializeBatchProgress(taskId, 1, "RESUME_PARSE");

            // 创建文件上传请求（作为单元素列表）
            List<EnhancedResumeParseService.FileUploadRequest> requests = List.of(
                new EnhancedResumeParseService.FileUploadRequest(
                    file.getOriginalFilename(),
                    file.getBytes(),
                    userId));

            // 提交异步处理任务
            enhancedResumeParseService.batchUploadAndParse(requests, taskId);

            AsyncParseResultVO result = new AsyncParseResultVO();
            result.setTaskId(taskId);
            result.setStatus("SUBMITTED");
            result.setMessage("简历解析任务已提交，请使用任务ID查询进度");

            return Result.success(result, "任务已提交");

        } catch (Exception e) {
            log.error("异步简历解析任务提交失败: userId={}", userId, e);
            return Result.error("任务提交失败: " + e.getMessage());
        }
    }

    /**
     * 查询异步解析任务进度
     */
    @GetMapping("/parse-progress/{taskId}")
    @DataPermission(mode = DataPermission.Mode.USER)
    @Operation(summary = "查询解析任务进度", description = "根据任务ID查询异步解析任务的进度")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "404", description = "任务不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<TaskProgressVO> getParseProgress(
            @Parameter(description = "任务ID", required = true)
            @PathVariable String taskId) {

        Long userId = AuthUtil.getCurrentUserId();
        log.debug("用户 {} 查询解析任务进度: taskId={}", userId, taskId);

        try {
            BatchProcessingTrackingService.BatchProgress progress = trackingService.getBatchProgress(taskId);

            if (progress == null) {
                return Result.error("任务不存在", 404);
            }

            TaskProgressVO progressVO = new TaskProgressVO();
            progressVO.setTaskId(taskId);
            progressVO.setStatus(progress.getStatus().name());
            progressVO.setProgressPercentage(progress.getProgressPercentage());
            progressVO.setProcessedCount(progress.getProcessedCount());
            progressVO.setTotalCount(progress.getTotalCount());
            progressVO.setSuccessCount(progress.getSuccessCount());
            progressVO.setFailureCount(progress.getFailureCount());
            progressVO.setStartTime(progress.getStartTime());
            progressVO.setEndTime(progress.getEndTime());
            progressVO.setDurationMillis(progress.getDurationMillis());
            progressVO.setErrorMessages(progress.getErrorMessages());

            return Result.success(progressVO, "查询成功");

        } catch (Exception e) {
            log.error("查询解析任务进度失败: taskId={}", taskId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 异步解析结果VO
     */
    public static class AsyncParseResultVO {
        private String taskId;
        private String status;
        private String message;

        // Getters and Setters
        public String getTaskId() { return taskId; }
        public void setTaskId(String taskId) { this.taskId = taskId; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    /**
     * 任务进度VO
     */
    public static class TaskProgressVO {
        private String taskId;
        private String status;
        private double progressPercentage;
        private int processedCount;
        private int totalCount;
        private int successCount;
        private int failureCount;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private Long durationMillis;
        private List<String> errorMessages;

        // Getters and Setters
        public String getTaskId() { return taskId; }
        public void setTaskId(String taskId) { this.taskId = taskId; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public double getProgressPercentage() { return progressPercentage; }
        public void setProgressPercentage(double progressPercentage) { this.progressPercentage = progressPercentage; }
        public int getProcessedCount() { return processedCount; }
        public void setProcessedCount(int processedCount) { this.processedCount = processedCount; }
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        public int getFailureCount() { return failureCount; }
        public void setFailureCount(int failureCount) { this.failureCount = failureCount; }
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        public Long getDurationMillis() { return durationMillis; }
        public void setDurationMillis(Long durationMillis) { this.durationMillis = durationMillis; }
        public List<String> getErrorMessages() { return errorMessages; }
        public void setErrorMessages(List<String> errorMessages) { this.errorMessages = errorMessages; }
    }
}
