package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.common.Result;
import com.tinyzk.user.center.common.util.AuthUtil;
import com.tinyzk.user.center.dto.CreateWorkDTO;
import com.tinyzk.user.center.dto.UpdateWorkDTO;
import com.tinyzk.user.center.service.UserWorkHistoryService;
import com.tinyzk.user.center.vo.UserWorkVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 当前用户工作经历控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/me/works")
@RequiredArgsConstructor
@Tag(name = "当前用户工作经历", description = "当前用户工作经历查询与管理相关接口")
public class UserWorkController {

    private final UserWorkHistoryService userWorkHistoryService;

    /**
     * 获取当前用户工作经历列表
     *
     * @return 工作经历列表
     */
    @GetMapping
    @Operation(summary = "获取当前用户工作经历列表", description = "获取当前登录用户的所有工作经历列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<List<UserWorkVO>> getCurrentUserWorks() {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("获取当前用户工作经历列表请求: userId={}", userId);
        List<UserWorkVO> workVOs = userWorkHistoryService.getUserWorks(userId);
        return Result.success(workVOs, "查询成功");
    }

    /**
     * 获取当前用户特定工作经历
     *
     * @param workId 工作经历ID
     * @return 工作经历详情
     */
    @GetMapping("/{workId}")
    @Operation(summary = "获取当前用户特定工作经历", description = "获取当前登录用户的某条特定工作经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "404", description = "工作经历不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserWorkVO> getCurrentUserWork(
            @Parameter(description = "工作经历ID", required = true)
            @PathVariable Long workId) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("获取当前用户特定工作经历请求: userId={}, workId={}", userId, workId);
        UserWorkVO workVO = userWorkHistoryService.getUserWork(userId, workId);
        return Result.success(workVO, "查询成功");
    }

    /**
     * 创建当前用户工作经历
     *
     * @param createWorkDTO 创建工作经历信息
     * @return 创建后的工作经历
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "创建当前用户工作经历", description = "为当前登录用户添加一条新的工作经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "创建成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserWorkVO> createCurrentUserWork(
            @Parameter(description = "创建的工作经历信息", required = true)
            @Validated @RequestBody CreateWorkDTO createWorkDTO) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("创建当前用户工作经历请求: userId={}, data={}", userId, createWorkDTO);
        UserWorkVO createdWork = userWorkHistoryService.createUserWork(userId, createWorkDTO);
        return Result.success(createdWork, "创建成功");
    }

    /**
     * 更新当前用户工作经历
     *
     * @param workId       工作经历ID
     * @param updateWorkDTO 更新信息
     * @return 更新后的工作经历
     */
    @PutMapping("/{workId}")
    @Operation(summary = "更新当前用户工作经历", description = "更新当前登录用户的某条特定工作经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "更新成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "工作经历不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserWorkVO> updateCurrentUserWork(
            @Parameter(description = "工作经历ID", required = true)
            @PathVariable Long workId,
            @Parameter(description = "更新的工作经历信息", required = true)
            @Validated @RequestBody UpdateWorkDTO updateWorkDTO) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("更新当前用户工作经历请求: userId={}, workId={}, data={}", userId, workId, updateWorkDTO);
        UserWorkVO updatedWork = userWorkHistoryService.updateUserWork(userId, workId, updateWorkDTO);
        return Result.success(updatedWork, "更新成功");
    }

    /**
     * 删除当前用户工作经历
     *
     * @param workId 工作经历ID
     * @return 删除结果
     */
    @DeleteMapping("/{workId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Operation(summary = "删除当前用户工作经历", description = "删除当前登录用户的某条特定工作经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "删除成功"),
            @ApiResponse(responseCode = "404", description = "工作经历不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Void> deleteCurrentUserWork(
            @Parameter(description = "工作经历ID", required = true)
            @PathVariable Long workId) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("删除当前用户工作经历请求: userId={}, workId={}", userId, workId);
        userWorkHistoryService.deleteUserWork(userId, workId);
        return Result.success(null, "删除成功");
    }
} 