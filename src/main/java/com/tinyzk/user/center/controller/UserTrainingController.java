package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.common.Result;
import com.tinyzk.user.center.common.util.AuthUtil;
import com.tinyzk.user.center.dto.CreateTrainingDTO;
import com.tinyzk.user.center.dto.UpdateTrainingDTO;
import com.tinyzk.user.center.service.UserTrainingService;
import com.tinyzk.user.center.vo.UserTrainingVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 当前用户培训经历控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/me/trainings")
@RequiredArgsConstructor
@Tag(name = "当前用户培训经历", description = "当前用户培训经历查询与管理相关接口")
public class UserTrainingController {

    private final UserTrainingService userTrainingService;

    /**
     * 获取当前用户培训经历列表
     *
     * @return 培训经历列表
     */
    @GetMapping
    @Operation(summary = "获取当前用户培训经历列表", description = "获取当前登录用户的所有培训经历列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<List<UserTrainingVO>> getCurrentUserTrainings() {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("获取当前用户培训经历列表请求: userId={}", userId);
        List<UserTrainingVO> trainingVOs = userTrainingService.getUserTrainings(userId);
        return Result.success(trainingVOs, "查询成功");
    }

    /**
     * 获取当前用户特定培训经历
     *
     * @param trainingId 培训经历ID
     * @return 培训经历详情
     */
    @GetMapping("/{trainingId}")
    @Operation(summary = "获取当前用户特定培训经历", description = "获取当前登录用户的某条特定培训经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "404", description = "培训经历不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserTrainingVO> getCurrentUserTraining(
            @Parameter(description = "培训经历ID", required = true)
            @PathVariable Long trainingId) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("获取当前用户特定培训经历请求: userId={}, trainingId={}", userId, trainingId);
        UserTrainingVO trainingVO = userTrainingService.getUserTraining(userId, trainingId);
        return Result.success(trainingVO, "查询成功");
    }

    /**
     * 创建当前用户培训经历
     *
     * @param createTrainingDTO 创建培训经历信息
     * @return 创建后的培训经历
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "创建当前用户培训经历", description = "为当前登录用户添加一条新的培训经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "创建成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserTrainingVO> createCurrentUserTraining(
            @Parameter(description = "创建的培训经历信息", required = true)
            @Validated @RequestBody CreateTrainingDTO createTrainingDTO) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("创建当前用户培训经历请求: userId={}, data={}", userId, createTrainingDTO);
        UserTrainingVO createdTraining = userTrainingService.createUserTraining(userId, createTrainingDTO);
        return Result.success(createdTraining, "创建成功");
    }

    /**
     * 更新当前用户培训经历
     *
     * @param trainingId        培训经历ID
     * @param updateTrainingDTO 更新信息
     * @return 更新后的培训经历
     */
    @PutMapping("/{trainingId}")
    @Operation(summary = "更新当前用户培训经历", description = "更新当前登录用户的某条特定培训经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "更新成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "培训经历不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<UserTrainingVO> updateCurrentUserTraining(
            @Parameter(description = "培训经历ID", required = true)
            @PathVariable Long trainingId,
            @Parameter(description = "更新的培训经历信息", required = true)
            @Validated @RequestBody UpdateTrainingDTO updateTrainingDTO) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("更新当前用户培训经历请求: userId={}, trainingId={}, data={}", userId, trainingId, updateTrainingDTO);
        UserTrainingVO updatedTraining = userTrainingService.updateUserTraining(userId, trainingId, updateTrainingDTO);
        return Result.success(updatedTraining, "更新成功");
    }

    /**
     * 删除当前用户培训经历
     *
     * @param trainingId 培训经历ID
     * @return 删除结果
     */
    @DeleteMapping("/{trainingId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Operation(summary = "删除当前用户培训经历", description = "删除当前登录用户的某条特定培训经历")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "删除成功"),
            @ApiResponse(responseCode = "404", description = "培训经历不存在"),
            @ApiResponse(responseCode = "401", description = "未认证"),
            @ApiResponse(responseCode = "500", description = "系统错误")
    })
    public Result<Void> deleteCurrentUserTraining(
            @Parameter(description = "培训经历ID", required = true)
            @PathVariable Long trainingId) {
        Long userId = AuthUtil.getCurrentUserId();
        log.info("删除当前用户培训经历请求: userId={}, trainingId={}", userId, trainingId);
        userTrainingService.deleteUserTraining(userId, trainingId);
        return Result.success(null, "删除成功");
    }
} 