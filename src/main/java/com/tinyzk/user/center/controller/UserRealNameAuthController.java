package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.common.Result;
import com.tinyzk.user.center.common.annotation.UserAudit;
import com.tinyzk.user.center.common.enums.OperationType;
import com.tinyzk.user.center.dto.RealNameAuthDTO;
import com.tinyzk.user.center.service.UserRealNameAuthService;
import com.tinyzk.user.center.vo.RealNameAuthVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 用户实名认证 Controller
 */
@RestController
@RequestMapping("/api/v1/auth/real-name")
@RequiredArgsConstructor
@Validated
@Tag(name = "用户实名认证管理", description = "提供用户提交和查询实名认证信息的接口")
public class UserRealNameAuthController {

    private final UserRealNameAuthService userRealNameAuthService;

    /**
     * 提交实名认证信息
     *
     * @param realNameAuthDTO 实名认证信息 DTO
     * @return 认证结果
     */
    @PostMapping
    @Operation(summary = "提交实名认证", description = "用户提交真实姓名和身份证号进行认证")
    // UserAudit 注解已在 Service 层添加，Controller 层通常不需要重复添加，除非有特殊的审计需求
    public Result<RealNameAuthVO> submitRealNameAuth(
            @Valid @RequestBody RealNameAuthDTO realNameAuthDTO) {
        RealNameAuthVO result = userRealNameAuthService.submit(realNameAuthDTO);
        return Result.success(result);
    }

    /**
     * 查询用户实名认证信息
     *
     * @param userId 用户ID
     * @return 认证信息
     */
    @GetMapping("/{userId}")
    @Operation(summary = "查询实名认证信息", description = "根据用户ID查询其最新的实名认证状态和信息")
    @UserAudit(type = OperationType.REALNAME_AUTH_QUERY, detail = "查询实名认证信息") // 查询操作可以在 Controller 层审计
    public Result<RealNameAuthVO> getRealNameAuth(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long userId) {
        RealNameAuthVO result = userRealNameAuthService.getByUserId(userId);
        return Result.success(result);
    }
}
