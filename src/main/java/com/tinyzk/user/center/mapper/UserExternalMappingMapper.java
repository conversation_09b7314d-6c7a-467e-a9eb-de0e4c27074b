package com.tinyzk.user.center.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tinyzk.user.center.entity.UserExternalMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户外部系统映射数据访问接口
 */
@Mapper
public interface UserExternalMappingMapper extends BaseMapper<UserExternalMapping> {
    
    /**
     * 根据外部用户ID和客户端ID查询用户中心用户ID
     *
     * @param externalUserId 外部用户ID
     * @param clientId 客户端ID
     * @return 用户中心用户ID，如果不存在返回null
     */
    @Select("SELECT user_id FROM user_external_mapping WHERE external_user_id = #{externalUserId} AND external_system_client_id = #{clientId} LIMIT 1")
    Long getUserIdByExternalId(@Param("externalUserId") String externalUserId, @Param("clientId") String clientId);
    
    /**
     * 根据用户中心用户ID和客户端ID查询外部用户ID
     *
     * @param userId 用户中心用户ID
     * @param clientId 客户端ID
     * @return 外部用户ID，如果不存在返回null
     */
    @Select("SELECT external_user_id FROM user_external_mapping WHERE user_id = #{userId} AND external_system_client_id = #{clientId} LIMIT 1")
    String getExternalIdByUserId(@Param("userId") Long userId, @Param("clientId") String clientId);
} 