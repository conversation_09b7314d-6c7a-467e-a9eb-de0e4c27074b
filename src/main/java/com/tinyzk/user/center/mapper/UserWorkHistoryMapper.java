package com.tinyzk.user.center.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tinyzk.user.center.entity.UserWorkHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户工作经历 Mapper 接口
 */
@Mapper
public interface UserWorkHistoryMapper extends BaseMapper<UserWorkHistory> {

    /**
     * 根据用户ID、公司名称和职位查询工作经历
     *
     * @param userId 用户ID
     * @param companyName 公司名称
     * @param positionName 职位名称
     * @return 工作经历信息
     */
    @Select("SELECT * FROM user_work_history WHERE user_id = #{userId} AND company_name = #{companyName} AND position_name = #{positionName} AND deleted_at IS NULL LIMIT 1")
    UserWorkHistory selectByUserIdAndCompanyAndPosition(@Param("userId") Long userId,
                                                      @Param("companyName") String companyName,
                                                      @Param("positionName") String positionName);
}