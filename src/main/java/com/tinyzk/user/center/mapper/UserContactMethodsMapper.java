package com.tinyzk.user.center.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tinyzk.user.center.entity.UserContactMethods;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户附加联系方式 Mapper 接口
 */
@Mapper
public interface UserContactMethodsMapper extends BaseMapper<UserContactMethods> {

    /**
     * 根据用户ID、联系方式类型和值查询联系方式
     *
     * @param userId 用户ID
     * @param contactType 联系方式类型
     * @param contactValue 联系方式值
     * @return 联系方式信息
     */
    @Select("SELECT * FROM user_contact_methods WHERE user_id = #{userId} AND contact_type = #{contactType} AND contact_value = #{contactValue} AND deleted_at IS NULL LIMIT 1")
    UserContactMethods selectByUserIdAndTypeAndValue(@Param("userId") Long userId,
                                                   @Param("contactType") Integer contactType,
                                                   @Param("contactValue") String contactValue);
}