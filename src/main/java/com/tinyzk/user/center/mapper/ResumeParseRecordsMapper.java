package com.tinyzk.user.center.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tinyzk.user.center.entity.ResumeParseRecords;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 简历解析记录Mapper接口
 */
@Mapper
public interface ResumeParseRecordsMapper extends BaseMapper<ResumeParseRecords> {

    /**
     * 根据用户ID查询解析记录
     *
     * @param userId 用户ID
     * @return 解析记录列表
     */
    List<ResumeParseRecords> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据文件哈希查询解析记录
     *
     * @param fileHash 文件哈希
     * @return 解析记录
     */
    ResumeParseRecords selectByFileHash(@Param("fileHash") String fileHash);

    /**
     * 根据用户ID和记录ID查询解析记录
     *
     * @param userId 用户ID
     * @param recordId 记录ID
     * @return 解析记录
     */
    ResumeParseRecords selectByUserIdAndRecordId(@Param("userId") Long userId, @Param("recordId") Long recordId);

    /**
     * 按日期范围统计记录数
     */
    @Select("SELECT COUNT(*) FROM resume_parse_records WHERE created_at BETWEEN #{startTime} AND #{endTime}")
    int countByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 按状态统计记录数
     */
    @Select("SELECT COUNT(*) FROM resume_parse_records WHERE parse_status = #{status}")
    int countByStatus(@Param("status") Integer status);
}
