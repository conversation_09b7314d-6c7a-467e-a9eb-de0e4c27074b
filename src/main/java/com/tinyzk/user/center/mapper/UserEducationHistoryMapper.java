package com.tinyzk.user.center.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tinyzk.user.center.entity.UserEducationHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户教育经历 Mapper 接口
 */
@Mapper
public interface UserEducationHistoryMapper extends BaseMapper<UserEducationHistory> {

    /**
     * 根据用户ID、学校名称和专业查询教育经历
     *
     * @param userId 用户ID
     * @param schoolName 学校名称
     * @param major 专业
     * @return 教育经历信息
     */
    @Select("SELECT * FROM user_education_history WHERE user_id = #{userId} AND school_name = #{schoolName} AND major = #{major} AND deleted_at IS NULL LIMIT 1")
    UserEducationHistory selectByUserIdAndSchoolAndMajor(@Param("userId") Long userId,
                                                       @Param("schoolName") String schoolName,
                                                       @Param("major") String major);
}