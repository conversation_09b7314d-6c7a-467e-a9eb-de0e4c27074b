package com.tinyzk.user.center.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tinyzk.user.center.entity.UserProjectHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户项目经历 Mapper 接口
 */
@Mapper
public interface UserProjectHistoryMapper extends BaseMapper<UserProjectHistory> {

    /**
     * 根据用户ID和项目名称查询项目经历
     *
     * @param userId 用户ID
     * @param projectName 项目名称
     * @return 项目经历信息
     */
    @Select("SELECT * FROM user_project_history WHERE user_id = #{userId} AND project_name = #{projectName} AND deleted_at IS NULL LIMIT 1")
    UserProjectHistory selectByUserIdAndProjectName(@Param("userId") Long userId,
                                                   @Param("projectName") String projectName);
}