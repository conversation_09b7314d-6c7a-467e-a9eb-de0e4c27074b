package com.tinyzk.user.center.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tinyzk.user.center.entity.UserAwards;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户获奖记录Mapper接口
 */
@Mapper
public interface UserAwardsMapper extends BaseMapper<UserAwards> {

    /**
     * 根据用户ID查询获奖记录列表
     *
     * @param userId 用户ID
     * @return 获奖记录列表
     */
    @Select("SELECT * FROM user_awards WHERE user_id = #{userId} AND deleted_at IS NULL ORDER BY award_date DESC")
    List<UserAwards> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和奖项名称查询获奖记录
     *
     * @param userId 用户ID
     * @param awardName 奖项名称
     * @return 获奖记录
     */
    @Select("SELECT * FROM user_awards WHERE user_id = #{userId} AND award_name = #{awardName} AND deleted_at IS NULL LIMIT 1")
    UserAwards selectByUserIdAndAwardName(@Param("userId") Long userId, @Param("awardName") String awardName);
}
