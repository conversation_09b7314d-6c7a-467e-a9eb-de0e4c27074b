package com.tinyzk.user.center.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tinyzk.user.center.entity.UserBase;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户基础信息 Mapper 接口
 */
public interface UserBaseMapper extends BaseMapper<UserBase> {

    /**
     * 获取最近活跃的用户ID列表
     * 基于用户认证表的最后登录时间
     *
     * @param since 开始时间
     * @param limit 限制数量
     * @return 用户ID列表
     */
    @Select("SELECT ub.user_id FROM user_base ub " +
            "INNER JOIN user_auth ua ON ub.user_id = ua.user_id " +
            "WHERE ua.last_login_at >= #{since} AND ub.status = 1 " +
            "GROUP BY ub.user_id " +
            "ORDER BY MAX(ua.last_login_at) DESC LIMIT #{limit}")
    List<Long> selectActiveUserIds(@Param("since") LocalDateTime since, @Param("limit") int limit);

    /**
     * 获取最近注册的用户ID列表
     * 当没有登录记录时，可以基于注册时间获取用户
     *
     * @param since 开始时间
     * @param limit 限制数量
     * @return 用户ID列表
     */
    @Select("SELECT user_id FROM user_base " +
            "WHERE created_at >= #{since} AND status = 1 " +
            "ORDER BY created_at DESC LIMIT #{limit}")
    List<Long> selectRecentUserIds(@Param("since") LocalDateTime since, @Param("limit") int limit);

    /**
     * 获取所有正常状态的用户ID列表（用于兜底）
     * 按创建时间倒序，优先获取新用户
     *
     * @param limit 限制数量
     * @return 用户ID列表
     */
    @Select("SELECT user_id FROM user_base " +
            "WHERE status = 1 " +
            "ORDER BY created_at DESC LIMIT #{limit}")
    List<Long> selectNormalUserIds(@Param("limit") int limit);
}
