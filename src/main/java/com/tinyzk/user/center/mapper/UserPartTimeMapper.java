package com.tinyzk.user.center.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tinyzk.user.center.entity.UserPartTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户兼职经历Mapper接口
 */
@Mapper
public interface UserPartTimeMapper extends BaseMapper<UserPartTime> {

    /**
     * 根据用户ID查询兼职经历列表
     *
     * @param userId 用户ID
     * @return 兼职经历列表
     */
    @Select("SELECT * FROM user_part_time_history WHERE user_id = #{userId} AND deleted_at IS NULL")
    List<UserPartTime> selectByUserId(@Param("userId") Long userId);
} 