package com.tinyzk.user.center.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tinyzk.user.center.entity.DeadLetterRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 死信记录Mapper接口
 */
@Mapper
public interface DeadLetterRecordMapper extends BaseMapper<DeadLetterRecord> {

    /**
     * 统计总数
     */
    @Select("SELECT COUNT(*) FROM dead_letter_records WHERE deleted_at IS NULL")
    long countTotal();

    /**
     * 按状态统计
     */
    @Select("SELECT COUNT(*) FROM dead_letter_records WHERE status = #{status} AND deleted_at IS NULL")
    long countByStatus(@Param("status") Integer status);

    /**
     * 按主题统计
     */
    @Select("SELECT original_topic, COUNT(*) as count FROM dead_letter_records " +
            "WHERE deleted_at IS NULL GROUP BY original_topic")
    Map<String, Long> countByTopic();

    /**
     * 删除指定时间之前已处理的记录
     */
    @Delete("DELETE FROM dead_letter_records WHERE status = 2 AND processed_at < #{cutoffTime}")
    int deleteProcessedBefore(@Param("cutoffTime") LocalDateTime cutoffTime);
}
