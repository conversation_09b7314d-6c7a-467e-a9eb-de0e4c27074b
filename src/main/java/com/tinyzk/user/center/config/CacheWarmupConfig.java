package com.tinyzk.user.center.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.List;

/**
 * 缓存预热配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "cache.warmup")
public class CacheWarmupConfig {
    
    /**
     * 是否启用缓存预热
     */
    private boolean enabled = true;
    
    /**
     * 应用启动时是否进行预热
     */
    private boolean startupWarmup = true;
    
    /**
     * 定时预热配置
     */
    private Schedule schedule = new Schedule();
    
    /**
     * 预热策略配置
     */
    private Strategy strategy = new Strategy();
    
    /**
     * 资源控制配置
     */
    private Resource resource = new Resource();
    
    @Data
    public static class Schedule {
        /**
         * 是否启用定时预热
         */
        private boolean enabled = true;
        
        /**
         * 预热执行时间（cron表达式）
         * 默认每天凌晨2点执行
         */
        private String cron = "0 0 2 * * ?";
        
        /**
         * 缓存过期前预热时间
         * 默认在缓存过期前2分钟进行预热
         */
        private Duration beforeExpire = Duration.ofMinutes(2);
    }
    
    @Data
    public static class Strategy {
        /**
         * 用户详情预热数量
         * 预热最近活跃的用户数量
         */
        private int userDetailCount = 1000;
        
        /**
         * 用户列表预热页数
         * 预热用户列表的前几页
         */
        private int userListPages = 5;
        
        /**
         * 每页大小
         */
        private int pageSize = 20;
        
        /**
         * 热点数据识别天数
         * 基于最近N天的访问数据识别热点
         */
        private int hotDataDays = 7;
        
        /**
         * 预热优先级列表
         */
        private List<String> priorities = List.of(
            "userDetail",      // 用户详情
            "userList",        // 用户列表
            "clientDetails",   // 客户端详情
            "userMapping"      // 用户映射关系
        );
    }
    
    @Data
    public static class Resource {
        /**
         * 预热线程池大小
         */
        private int threadPoolSize = 5;
        
        /**
         * 预热超时时间
         */
        private Duration timeout = Duration.ofMinutes(30);
        
        /**
         * 批处理大小
         */
        private int batchSize = 100;
        
        /**
         * 预热间隔时间（避免对数据库造成压力）
         */
        private Duration interval = Duration.ofMillis(100);
    }
}
