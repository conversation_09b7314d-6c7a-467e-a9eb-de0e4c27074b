package com.tinyzk.user.center.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 批量简历上传配置
 */
@Configuration
@ConfigurationProperties(prefix = "batch.resume.upload")
@Data
public class BatchResumeUploadConfig {

    /**
     * 最大文件数量
     */
    private Integer maxFileCount = 20;

    /**
     * 单个文件最大大小（MB）
     */
    private Integer maxFileSizeMb = 10;

    /**
     * 批量总大小限制（MB）
     */
    private Integer maxTotalSizeMb = 100;

    /**
     * 最大并发处理数量
     */
    private Integer maxConcurrency = 5;

    /**
     * 默认超时时间（秒）
     */
    private Integer defaultTimeoutSeconds = 60;

    /**
     * 最大超时时间（秒）
     */
    private Integer maxTimeoutSeconds = 300;

    /**
     * 支持的文件类型
     */
    private String[] supportedFileTypes = {"pdf", "doc", "docx", "txt"};

    /**
     * 支持的MIME类型
     */
    private String[] supportedMimeTypes = {
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "text/plain"
    };

    /**
     * 是否启用文件类型验证
     */
    private Boolean enableFileTypeValidation = true;

    /**
     * 是否启用文件大小验证
     */
    private Boolean enableFileSizeValidation = true;

    /**
     * 是否启用病毒扫描
     */
    private Boolean enableVirusScan = false;

    /**
     * 临时文件存储路径
     */
    private String tempFileStoragePath = "/tmp/batch-resume-upload";

    /**
     * 处理结果保留时间（小时）
     */
    private Integer resultRetentionHours = 24;

    /**
     * 是否启用处理进度通知
     */
    private Boolean enableProgressNotification = true;

    /**
     * 进度通知间隔（处理文件数）
     */
    private Integer progressNotificationInterval = 10;

    /**
     * 重试配置
     */
    private RetryConfig retry = new RetryConfig();

    /**
     * 重试配置
     */
    @Data
    public static class RetryConfig {
        /**
         * 是否启用重试
         */
        private Boolean enabled = true;

        /**
         * 最大重试次数
         */
        private Integer maxAttempts = 3;

        /**
         * 重试间隔（毫秒）
         */
        private Long retryIntervalMs = 1000L;

        /**
         * 重试的异常类型
         */
        private String[] retryableExceptions = {
            "java.net.SocketTimeoutException",
            "java.io.IOException",
            "org.springframework.web.client.ResourceAccessException"
        };
    }

    /**
     * 验证配置是否有效
     */
    public boolean isValid() {
        return maxFileCount > 0 && maxFileCount <= 100 &&
               maxFileSizeMb > 0 && maxFileSizeMb <= 50 &&
               maxTotalSizeMb > 0 && maxTotalSizeMb <= 500 &&
               maxConcurrency > 0 && maxConcurrency <= 20 &&
               defaultTimeoutSeconds > 0 && defaultTimeoutSeconds <= maxTimeoutSeconds;
    }

    /**
     * 获取单个文件最大大小（字节）
     */
    public long getMaxFileSizeBytes() {
        return maxFileSizeMb * 1024L * 1024L;
    }

    /**
     * 获取批量总大小限制（字节）
     */
    public long getMaxTotalSizeBytes() {
        return maxTotalSizeMb * 1024L * 1024L;
    }
}
