package com.tinyzk.user.center.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.BasicPolymorphicTypeValidator;
import com.fasterxml.jackson.databind.jsontype.PolymorphicTypeValidator;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.tinyzk.user.center.dto.UserListRequestDTO;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;

@Configuration
@EnableCaching // 启用缓存功能
public class CacheConfig {

    /**
     * 全局 ObjectMapper (不启用 activateDefaultTyping)
     * Spring Boot MVC 和其他需要标准 JSON 的地方会使用这个。
     */
    @Bean
    @Primary // 标记为主要的 ObjectMapper
    public ObjectMapper globalObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        // 这里不配置 activateDefaultTyping
        return objectMapper;
    }

    /**
     * 专用于 Redis 缓存的 ObjectMapper (启用 activateDefaultTyping)
     */
    @Bean("redisCacheObjectMapper") // 给它一个特定的名字
    public ObjectMapper redisCacheObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());

        PolymorphicTypeValidator ptv = BasicPolymorphicTypeValidator.builder()
                .allowIfSubType("com.tinyzk.user.center")
                .allowIfSubType(java.util.List.class)
                .allowIfSubType(java.util.Map.class)
                .build();
        objectMapper.activateDefaultTyping(ptv, ObjectMapper.DefaultTyping.NON_FINAL, com.fasterxml.jackson.annotation.JsonTypeInfo.As.PROPERTY);

        return objectMapper;
    }

    @Bean
    public RedisCacheManager cacheManager(RedisConnectionFactory redisConnectionFactory,
                                        @Qualifier("redisCacheObjectMapper") ObjectMapper redisCacheObjectMapper) { // 注入专用的 ObjectMapper
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(10))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer(redisCacheObjectMapper))) // 使用专用的 ObjectMapper
                .disableCachingNullValues();

        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(config)
                .build();
    }

    @Bean("userListKeyGenerator")
    public KeyGenerator userListKeyGenerator() {
        return (target, method, params) -> {
            StringBuilder sb = new StringBuilder();
            sb.append(target.getClass().getSimpleName()).append(":");
            sb.append(method.getName()).append(":");
            if (params.length > 0 && params[0] instanceof UserListRequestDTO) {
                UserListRequestDTO dto = (UserListRequestDTO) params[0];
                sb.append("pageN=").append(dto.getPageNumber());
                sb.append("_pageS=").append(dto.getPageSize());
                if (dto.getUserId() != null) sb.append("_uid=").append(dto.getUserId());
                if (dto.getNickname() != null && !dto.getNickname().isEmpty()) sb.append("_nick=").append(dto.getNickname());
                if (dto.getIdentifier() != null && !dto.getIdentifier().isEmpty()) sb.append("_idfr=").append(dto.getIdentifier());
                if (dto.getStatus() != null) sb.append("_st=").append(dto.getStatus());
                if (dto.getRealNameVerified() != null) sb.append("_rv=").append(dto.getRealNameVerified());
                if (dto.getRegisteredAfter() != null) sb.append("_ra=").append(dto.getRegisteredAfter().toString());
                if (dto.getRegisteredBefore() != null) sb.append("_rb=").append(dto.getRegisteredBefore().toString());
                if (dto.getLastLoginAfter() != null) sb.append("_lla=").append(dto.getLastLoginAfter().toString());
                if (dto.getLastLoginBefore() != null) sb.append("_llb=").append(dto.getLastLoginBefore().toString());
                if (dto.getSortBy() != null && !dto.getSortBy().isEmpty()) sb.append("_sort=").append(dto.getSortBy());
                if (dto.getSortOrder() != null && !dto.getSortOrder().isEmpty()) sb.append("_order=").append(dto.getSortOrder());
            } else {
                for (Object param : params) {
                    if (param != null) {
                        sb.append(param.toString());
                    }
                }
            }
            return sb.toString();
        };
    }
} 