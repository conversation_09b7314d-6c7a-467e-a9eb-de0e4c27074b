package com.tinyzk.user.center.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * 简历解析服务配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "resume.parse")
public class ResumeParseConfig {

    /**
     * 第三方解析服务API地址
     */
    private String apiUrl = "http://192.168.0.124:8000/parse_file";

    /**
     * API超时时间(毫秒)
     */
    private Integer timeout = 30000;

    /**
     * 最大重试次数
     */
    private Integer maxRetries = 3;

    /**
     * 支持的文件类型
     */
    private String[] supportedFileTypes = {"doc", "docx", "pdf"};

    /**
     * 最大文件大小(字节) - 默认10MB
     */
    private Long maxFileSize = 10 * 1024 * 1024L;

    /**
     * 是否启用文件缓存
     */
    private Boolean enableFileCache = true;

    /**
     * 文件缓存目录
     */
    private String fileCacheDir = "/tmp/resume-cache";

    /**
     * 是否启用解析结果缓存
     */
    private Boolean enableResultCache = true;

    /**
     * 解析结果缓存过期时间(秒) - 默认1小时
     */
    private Integer resultCacheExpire = 3600;

    /**
     * 配置RestTemplate Bean用于HTTP请求
     */
    @Bean("resumeParseRestTemplate")
    public RestTemplate resumeParseRestTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(timeout);
        factory.setReadTimeout(timeout);
        return new RestTemplate(factory);
    }
}
