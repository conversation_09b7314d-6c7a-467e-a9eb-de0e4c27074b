package com.tinyzk.user.center.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步任务执行器配置
 */
@Configuration
@EnableAsync
@ConfigurationProperties(prefix = "async.task.executor")
@Slf4j
public class AsyncTaskExecutorConfig {

    /**
     * 核心线程数
     */
    private int corePoolSize = 5;

    /**
     * 最大线程数
     */
    private int maxPoolSize = 20;

    /**
     * 队列容量
     */
    private int queueCapacity = 100;

    /**
     * 线程空闲时间（秒）
     */
    private int keepAliveSeconds = 60;

    /**
     * 线程名前缀
     */
    private String threadNamePrefix = "AsyncTask-";

    /**
     * 是否等待任务完成后关闭
     */
    private boolean waitForTasksToCompleteOnShutdown = true;

    /**
     * 等待任务完成的超时时间（秒）
     */
    private int awaitTerminationSeconds = 60;

    /**
     * 批量简历解析专用线程池配置
     */
    private BatchResumeParseExecutor batchResumeParseExecutor = new BatchResumeParseExecutor();

    /**
     * 创建通用异步任务执行器
     */
    @Bean(name = "taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix(threadNamePrefix);
        executor.setWaitForTasksToCompleteOnShutdown(waitForTasksToCompleteOnShutdown);
        executor.setAwaitTerminationSeconds(awaitTerminationSeconds);
        
        // 拒绝策略：调用者运行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        executor.initialize();
        
        log.info("通用异步任务执行器初始化完成: corePoolSize={}, maxPoolSize={}, queueCapacity={}", 
                corePoolSize, maxPoolSize, queueCapacity);
        
        return executor;
    }

    /**
     * 创建批量简历解析专用执行器
     */
    @Bean(name = "batchResumeParseExecutor")
    public Executor batchResumeParseExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(batchResumeParseExecutor.getCorePoolSize());
        executor.setMaxPoolSize(batchResumeParseExecutor.getMaxPoolSize());
        executor.setQueueCapacity(batchResumeParseExecutor.getQueueCapacity());
        executor.setKeepAliveSeconds(batchResumeParseExecutor.getKeepAliveSeconds());
        executor.setThreadNamePrefix(batchResumeParseExecutor.getThreadNamePrefix());
        executor.setWaitForTasksToCompleteOnShutdown(batchResumeParseExecutor.isWaitForTasksToCompleteOnShutdown());
        executor.setAwaitTerminationSeconds(batchResumeParseExecutor.getAwaitTerminationSeconds());
        
        // 拒绝策略：调用者运行（确保任务不会丢失）
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        executor.initialize();
        
        log.info("批量简历解析执行器初始化完成: corePoolSize={}, maxPoolSize={}, queueCapacity={}", 
                batchResumeParseExecutor.getCorePoolSize(), 
                batchResumeParseExecutor.getMaxPoolSize(), 
                batchResumeParseExecutor.getQueueCapacity());
        
        return executor;
    }

    // Getter and Setter methods
    public int getCorePoolSize() {
        return corePoolSize;
    }

    public void setCorePoolSize(int corePoolSize) {
        this.corePoolSize = corePoolSize;
    }

    public int getMaxPoolSize() {
        return maxPoolSize;
    }

    public void setMaxPoolSize(int maxPoolSize) {
        this.maxPoolSize = maxPoolSize;
    }

    public int getQueueCapacity() {
        return queueCapacity;
    }

    public void setQueueCapacity(int queueCapacity) {
        this.queueCapacity = queueCapacity;
    }

    public int getKeepAliveSeconds() {
        return keepAliveSeconds;
    }

    public void setKeepAliveSeconds(int keepAliveSeconds) {
        this.keepAliveSeconds = keepAliveSeconds;
    }

    public String getThreadNamePrefix() {
        return threadNamePrefix;
    }

    public void setThreadNamePrefix(String threadNamePrefix) {
        this.threadNamePrefix = threadNamePrefix;
    }

    public boolean isWaitForTasksToCompleteOnShutdown() {
        return waitForTasksToCompleteOnShutdown;
    }

    public void setWaitForTasksToCompleteOnShutdown(boolean waitForTasksToCompleteOnShutdown) {
        this.waitForTasksToCompleteOnShutdown = waitForTasksToCompleteOnShutdown;
    }

    public int getAwaitTerminationSeconds() {
        return awaitTerminationSeconds;
    }

    public void setAwaitTerminationSeconds(int awaitTerminationSeconds) {
        this.awaitTerminationSeconds = awaitTerminationSeconds;
    }

    public BatchResumeParseExecutor getBatchResumeParseExecutor() {
        return batchResumeParseExecutor;
    }

    public void setBatchResumeParseExecutor(BatchResumeParseExecutor batchResumeParseExecutor) {
        this.batchResumeParseExecutor = batchResumeParseExecutor;
    }

    /**
     * 批量简历解析执行器配置
     */
    public static class BatchResumeParseExecutor {
        private int corePoolSize = 3;
        private int maxPoolSize = 10;
        private int queueCapacity = 50;
        private int keepAliveSeconds = 300;
        private String threadNamePrefix = "BatchResumeParser-";
        private boolean waitForTasksToCompleteOnShutdown = true;
        private int awaitTerminationSeconds = 120;

        // Getter and Setter methods
        public int getCorePoolSize() {
            return corePoolSize;
        }

        public void setCorePoolSize(int corePoolSize) {
            this.corePoolSize = corePoolSize;
        }

        public int getMaxPoolSize() {
            return maxPoolSize;
        }

        public void setMaxPoolSize(int maxPoolSize) {
            this.maxPoolSize = maxPoolSize;
        }

        public int getQueueCapacity() {
            return queueCapacity;
        }

        public void setQueueCapacity(int queueCapacity) {
            this.queueCapacity = queueCapacity;
        }

        public int getKeepAliveSeconds() {
            return keepAliveSeconds;
        }

        public void setKeepAliveSeconds(int keepAliveSeconds) {
            this.keepAliveSeconds = keepAliveSeconds;
        }

        public String getThreadNamePrefix() {
            return threadNamePrefix;
        }

        public void setThreadNamePrefix(String threadNamePrefix) {
            this.threadNamePrefix = threadNamePrefix;
        }

        public boolean isWaitForTasksToCompleteOnShutdown() {
            return waitForTasksToCompleteOnShutdown;
        }

        public void setWaitForTasksToCompleteOnShutdown(boolean waitForTasksToCompleteOnShutdown) {
            this.waitForTasksToCompleteOnShutdown = waitForTasksToCompleteOnShutdown;
        }

        public int getAwaitTerminationSeconds() {
            return awaitTerminationSeconds;
        }

        public void setAwaitTerminationSeconds(int awaitTerminationSeconds) {
            this.awaitTerminationSeconds = awaitTerminationSeconds;
        }
    }
}
