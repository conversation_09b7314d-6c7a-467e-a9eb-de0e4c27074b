package com.tinyzk.user.center.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Spring Doc 配置
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class SpringDocConfig {

	@Bean
	public OpenAPI customOpenApi() {
		log.info("Config OpenAPI Doc");
		SecurityScheme securityScheme = new SecurityScheme()
				.type(SecurityScheme.Type.HTTP)
				.scheme("basic");

		Components components = new Components()
				.addSecuritySchemes("basicScheme", securityScheme);

		License license = new License()
				.name("Apache 2.0")
				.url("http://springdoc.org");

		Info api = new Info().title("User Center API")
				.version("v1")
				.license(license);

		return new OpenAPI()
				.components(components)
				.info(api);
	}
}
