package com.tinyzk.user.center.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 优化的线程池配置
 * 整合现有的AsyncTaskExecutorConfig和AsyncConfig，避免重复定义
 */
@Configuration
@EnableAsync
@ConfigurationProperties(prefix = "async.task.executor")
@Slf4j
public class OptimizedThreadPoolConfig {

    /**
     * 通用异步任务线程池配置
     */
    private int corePoolSize = 5;
    private int maxPoolSize = 15; // 降低最大线程数，避免过度竞争
    private int queueCapacity = 200; // 增加队列容量
    private int keepAliveSeconds = 60;
    private String threadNamePrefix = "AsyncTask-";
    private boolean waitForTasksToCompleteOnShutdown = true;
    private int awaitTerminationSeconds = 60;

    /**
     * 批量简历解析专用线程池配置
     */
    private BatchResumeParseExecutor batchResumeParseExecutor = new BatchResumeParseExecutor();

    /**
     * 缓存预热专用线程池配置
     */
    private CacheWarmupExecutor cacheWarmupExecutor = new CacheWarmupExecutor();

    /**
     * 第三方API调用专用线程池配置
     */
    private ApiCallExecutor apiCallExecutor = new ApiCallExecutor();

    /**
     * OSS上传专用线程池配置
     */
    private OssUploadExecutor ossUploadExecutor = new OssUploadExecutor();

    /**
     * 通用异步任务线程池（整合现有taskExecutor）
     */
    @Bean("taskExecutor")
    @Primary
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix(threadNamePrefix);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(waitForTasksToCompleteOnShutdown);
        executor.setAwaitTerminationSeconds(awaitTerminationSeconds);
        executor.initialize();
        
        log.info("通用异步任务执行器初始化完成: corePoolSize={}, maxPoolSize={}, queueCapacity={}", 
                corePoolSize, maxPoolSize, queueCapacity);
        
        return executor;
    }

    /**
     * 简历解析专用线程池（优化现有batchResumeParseExecutor）
     */
    @Bean("batchResumeParseExecutor")
    public ThreadPoolTaskExecutor batchResumeParseExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(batchResumeParseExecutor.getCorePoolSize());
        executor.setMaxPoolSize(batchResumeParseExecutor.getMaxPoolSize());
        executor.setQueueCapacity(batchResumeParseExecutor.getQueueCapacity());
        executor.setKeepAliveSeconds(batchResumeParseExecutor.getKeepAliveSeconds());
        executor.setThreadNamePrefix(batchResumeParseExecutor.getThreadNamePrefix());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(batchResumeParseExecutor.isWaitForTasksToCompleteOnShutdown());
        executor.setAwaitTerminationSeconds(batchResumeParseExecutor.getAwaitTerminationSeconds());
        executor.initialize();
        
        log.info("批量简历解析执行器初始化完成: corePoolSize={}, maxPoolSize={}, queueCapacity={}", 
                batchResumeParseExecutor.getCorePoolSize(), 
                batchResumeParseExecutor.getMaxPoolSize(), 
                batchResumeParseExecutor.getQueueCapacity());
        
        return executor;
    }

    /**
     * 第三方API调用专用线程池
     */
    @Bean("apiCallExecutor")
    public ThreadPoolTaskExecutor apiCallExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(apiCallExecutor.getCorePoolSize());
        executor.setMaxPoolSize(apiCallExecutor.getMaxPoolSize());
        executor.setQueueCapacity(apiCallExecutor.getQueueCapacity());
        executor.setKeepAliveSeconds(apiCallExecutor.getKeepAliveSeconds());
        executor.setThreadNamePrefix(apiCallExecutor.getThreadNamePrefix());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(apiCallExecutor.isWaitForTasksToCompleteOnShutdown());
        executor.setAwaitTerminationSeconds(apiCallExecutor.getAwaitTerminationSeconds());
        executor.initialize();
        
        log.info("第三方API调用执行器初始化完成: corePoolSize={}, maxPoolSize={}, queueCapacity={}", 
                apiCallExecutor.getCorePoolSize(), 
                apiCallExecutor.getMaxPoolSize(), 
                apiCallExecutor.getQueueCapacity());
        
        return executor;
    }

    /**
     * 缓存预热专用线程池（保留现有配置）
     */
    @Bean("cacheWarmupExecutor")
    public ThreadPoolTaskExecutor cacheWarmupExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(cacheWarmupExecutor.getCorePoolSize());
        executor.setMaxPoolSize(cacheWarmupExecutor.getMaxPoolSize());
        executor.setQueueCapacity(cacheWarmupExecutor.getQueueCapacity());
        executor.setKeepAliveSeconds(cacheWarmupExecutor.getKeepAliveSeconds());
        executor.setThreadNamePrefix(cacheWarmupExecutor.getThreadNamePrefix());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(cacheWarmupExecutor.isWaitForTasksToCompleteOnShutdown());
        executor.setAwaitTerminationSeconds(cacheWarmupExecutor.getAwaitTerminationSeconds());
        executor.initialize();
        
        log.info("缓存预热执行器初始化完成: corePoolSize={}, maxPoolSize={}, queueCapacity={}", 
                cacheWarmupExecutor.getCorePoolSize(), 
                cacheWarmupExecutor.getMaxPoolSize(), 
                cacheWarmupExecutor.getQueueCapacity());
        
        return executor;
    }

    /**
     * OSS上传专用线程池
     */
    @Bean("ossUploadExecutor")
    public ThreadPoolTaskExecutor ossUploadExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(ossUploadExecutor.getCorePoolSize());
        executor.setMaxPoolSize(ossUploadExecutor.getMaxPoolSize());
        executor.setQueueCapacity(ossUploadExecutor.getQueueCapacity());
        executor.setKeepAliveSeconds(ossUploadExecutor.getKeepAliveSeconds());
        executor.setThreadNamePrefix(ossUploadExecutor.getThreadNamePrefix());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(ossUploadExecutor.isWaitForTasksToCompleteOnShutdown());
        executor.setAwaitTerminationSeconds(ossUploadExecutor.getAwaitTerminationSeconds());
        executor.initialize();
        
        log.info("OSS上传执行器初始化完成: corePoolSize={}, maxPoolSize={}, queueCapacity={}", 
                ossUploadExecutor.getCorePoolSize(), 
                ossUploadExecutor.getMaxPoolSize(), 
                ossUploadExecutor.getQueueCapacity());
        
        return executor;
    }

    // Getter and Setter methods for main configuration
    public int getCorePoolSize() { return corePoolSize; }
    public void setCorePoolSize(int corePoolSize) { this.corePoolSize = corePoolSize; }
    public int getMaxPoolSize() { return maxPoolSize; }
    public void setMaxPoolSize(int maxPoolSize) { this.maxPoolSize = maxPoolSize; }
    public int getQueueCapacity() { return queueCapacity; }
    public void setQueueCapacity(int queueCapacity) { this.queueCapacity = queueCapacity; }
    public int getKeepAliveSeconds() { return keepAliveSeconds; }
    public void setKeepAliveSeconds(int keepAliveSeconds) { this.keepAliveSeconds = keepAliveSeconds; }
    public String getThreadNamePrefix() { return threadNamePrefix; }
    public void setThreadNamePrefix(String threadNamePrefix) { this.threadNamePrefix = threadNamePrefix; }
    public boolean isWaitForTasksToCompleteOnShutdown() { return waitForTasksToCompleteOnShutdown; }
    public void setWaitForTasksToCompleteOnShutdown(boolean waitForTasksToCompleteOnShutdown) { 
        this.waitForTasksToCompleteOnShutdown = waitForTasksToCompleteOnShutdown; 
    }
    public int getAwaitTerminationSeconds() { return awaitTerminationSeconds; }
    public void setAwaitTerminationSeconds(int awaitTerminationSeconds) { 
        this.awaitTerminationSeconds = awaitTerminationSeconds; 
    }
    public BatchResumeParseExecutor getBatchResumeParseExecutor() { return batchResumeParseExecutor; }
    public void setBatchResumeParseExecutor(BatchResumeParseExecutor batchResumeParseExecutor) { 
        this.batchResumeParseExecutor = batchResumeParseExecutor; 
    }
    public CacheWarmupExecutor getCacheWarmupExecutor() { return cacheWarmupExecutor; }
    public void setCacheWarmupExecutor(CacheWarmupExecutor cacheWarmupExecutor) { 
        this.cacheWarmupExecutor = cacheWarmupExecutor; 
    }
    public ApiCallExecutor getApiCallExecutor() { return apiCallExecutor; }
    public void setApiCallExecutor(ApiCallExecutor apiCallExecutor) { this.apiCallExecutor = apiCallExecutor; }
    public OssUploadExecutor getOssUploadExecutor() { return ossUploadExecutor; }
    public void setOssUploadExecutor(OssUploadExecutor ossUploadExecutor) { this.ossUploadExecutor = ossUploadExecutor; }

    /**
     * 批量简历解析执行器配置
     */
    public static class BatchResumeParseExecutor {
        private int corePoolSize = 3; // I/O密集型，适中的核心线程数
        private int maxPoolSize = 8; // 降低最大线程数
        private int queueCapacity = 100; // 有界队列防止OOM
        private int keepAliveSeconds = 300;
        private String threadNamePrefix = "BatchResumeParser-";
        private boolean waitForTasksToCompleteOnShutdown = true;
        private int awaitTerminationSeconds = 120;

        // Getter and Setter methods
        public int getCorePoolSize() { return corePoolSize; }
        public void setCorePoolSize(int corePoolSize) { this.corePoolSize = corePoolSize; }
        public int getMaxPoolSize() { return maxPoolSize; }
        public void setMaxPoolSize(int maxPoolSize) { this.maxPoolSize = maxPoolSize; }
        public int getQueueCapacity() { return queueCapacity; }
        public void setQueueCapacity(int queueCapacity) { this.queueCapacity = queueCapacity; }
        public int getKeepAliveSeconds() { return keepAliveSeconds; }
        public void setKeepAliveSeconds(int keepAliveSeconds) { this.keepAliveSeconds = keepAliveSeconds; }
        public String getThreadNamePrefix() { return threadNamePrefix; }
        public void setThreadNamePrefix(String threadNamePrefix) { this.threadNamePrefix = threadNamePrefix; }
        public boolean isWaitForTasksToCompleteOnShutdown() { return waitForTasksToCompleteOnShutdown; }
        public void setWaitForTasksToCompleteOnShutdown(boolean waitForTasksToCompleteOnShutdown) {
            this.waitForTasksToCompleteOnShutdown = waitForTasksToCompleteOnShutdown;
        }
        public int getAwaitTerminationSeconds() { return awaitTerminationSeconds; }
        public void setAwaitTerminationSeconds(int awaitTerminationSeconds) {
            this.awaitTerminationSeconds = awaitTerminationSeconds;
        }
    }

    /**
     * 第三方API调用专用线程池配置
     */
    public static class ApiCallExecutor {
        private int corePoolSize = 2; // 匹配第三方API限流
        private int maxPoolSize = 5;
        private int queueCapacity = 50;
        private int keepAliveSeconds = 60;
        private String threadNamePrefix = "ApiCall-";
        private boolean waitForTasksToCompleteOnShutdown = true;
        private int awaitTerminationSeconds = 30;

        // Getter and Setter methods
        public int getCorePoolSize() { return corePoolSize; }
        public void setCorePoolSize(int corePoolSize) { this.corePoolSize = corePoolSize; }
        public int getMaxPoolSize() { return maxPoolSize; }
        public void setMaxPoolSize(int maxPoolSize) { this.maxPoolSize = maxPoolSize; }
        public int getQueueCapacity() { return queueCapacity; }
        public void setQueueCapacity(int queueCapacity) { this.queueCapacity = queueCapacity; }
        public int getKeepAliveSeconds() { return keepAliveSeconds; }
        public void setKeepAliveSeconds(int keepAliveSeconds) { this.keepAliveSeconds = keepAliveSeconds; }
        public String getThreadNamePrefix() { return threadNamePrefix; }
        public void setThreadNamePrefix(String threadNamePrefix) { this.threadNamePrefix = threadNamePrefix; }
        public boolean isWaitForTasksToCompleteOnShutdown() { return waitForTasksToCompleteOnShutdown; }
        public void setWaitForTasksToCompleteOnShutdown(boolean waitForTasksToCompleteOnShutdown) {
            this.waitForTasksToCompleteOnShutdown = waitForTasksToCompleteOnShutdown;
        }
        public int getAwaitTerminationSeconds() { return awaitTerminationSeconds; }
        public void setAwaitTerminationSeconds(int awaitTerminationSeconds) {
            this.awaitTerminationSeconds = awaitTerminationSeconds;
        }
    }

    /**
     * 缓存预热专用线程池配置
     */
    public static class CacheWarmupExecutor {
        private int corePoolSize = 3;
        private int maxPoolSize = 8;
        private int queueCapacity = 200;
        private int keepAliveSeconds = 60;
        private String threadNamePrefix = "CacheWarmup-";
        private boolean waitForTasksToCompleteOnShutdown = true;
        private int awaitTerminationSeconds = 30;

        // Getter and Setter methods
        public int getCorePoolSize() { return corePoolSize; }
        public void setCorePoolSize(int corePoolSize) { this.corePoolSize = corePoolSize; }
        public int getMaxPoolSize() { return maxPoolSize; }
        public void setMaxPoolSize(int maxPoolSize) { this.maxPoolSize = maxPoolSize; }
        public int getQueueCapacity() { return queueCapacity; }
        public void setQueueCapacity(int queueCapacity) { this.queueCapacity = queueCapacity; }
        public int getKeepAliveSeconds() { return keepAliveSeconds; }
        public void setKeepAliveSeconds(int keepAliveSeconds) { this.keepAliveSeconds = keepAliveSeconds; }
        public String getThreadNamePrefix() { return threadNamePrefix; }
        public void setThreadNamePrefix(String threadNamePrefix) { this.threadNamePrefix = threadNamePrefix; }
        public boolean isWaitForTasksToCompleteOnShutdown() { return waitForTasksToCompleteOnShutdown; }
        public void setWaitForTasksToCompleteOnShutdown(boolean waitForTasksToCompleteOnShutdown) {
            this.waitForTasksToCompleteOnShutdown = waitForTasksToCompleteOnShutdown;
        }
        public int getAwaitTerminationSeconds() { return awaitTerminationSeconds; }
        public void setAwaitTerminationSeconds(int awaitTerminationSeconds) {
            this.awaitTerminationSeconds = awaitTerminationSeconds;
        }
    }

    /**
     * OSS上传专用线程池配置
     */
    public static class OssUploadExecutor {
        private int corePoolSize = 8;
        private int maxPoolSize = 16;
        private int queueCapacity = 200;
        private int keepAliveSeconds = 300;
        private String threadNamePrefix = "OSS-Upload-";
        private boolean waitForTasksToCompleteOnShutdown = true;
        private int awaitTerminationSeconds = 60;

        // Getter and Setter methods
        public int getCorePoolSize() { return corePoolSize; }
        public void setCorePoolSize(int corePoolSize) { this.corePoolSize = corePoolSize; }
        public int getMaxPoolSize() { return maxPoolSize; }
        public void setMaxPoolSize(int maxPoolSize) { this.maxPoolSize = maxPoolSize; }
        public int getQueueCapacity() { return queueCapacity; }
        public void setQueueCapacity(int queueCapacity) { this.queueCapacity = queueCapacity; }
        public int getKeepAliveSeconds() { return keepAliveSeconds; }
        public void setKeepAliveSeconds(int keepAliveSeconds) { this.keepAliveSeconds = keepAliveSeconds; }
        public String getThreadNamePrefix() { return threadNamePrefix; }
        public void setThreadNamePrefix(String threadNamePrefix) { this.threadNamePrefix = threadNamePrefix; }
        public boolean isWaitForTasksToCompleteOnShutdown() { return waitForTasksToCompleteOnShutdown; }
        public void setWaitForTasksToCompleteOnShutdown(boolean waitForTasksToCompleteOnShutdown) {
            this.waitForTasksToCompleteOnShutdown = waitForTasksToCompleteOnShutdown;
        }
        public int getAwaitTerminationSeconds() { return awaitTerminationSeconds; }
        public void setAwaitTerminationSeconds(int awaitTerminationSeconds) {
            this.awaitTerminationSeconds = awaitTerminationSeconds;
        }
    }
}
