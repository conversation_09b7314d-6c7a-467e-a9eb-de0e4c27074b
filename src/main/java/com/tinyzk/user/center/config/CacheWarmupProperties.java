package com.tinyzk.user.center.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 缓存预热配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "cache.warmup")
public class CacheWarmupProperties {
    
    /**
     * 是否启用缓存预热
     */
    private boolean enabled = true;
    
    /**
     * 是否在应用启动时进行缓存预热
     */
    private boolean startupWarmup = true;
}
