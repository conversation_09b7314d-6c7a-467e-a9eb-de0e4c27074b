package com.tinyzk.user.center.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RocketMQ生产者配置
 */
@Configuration
@Slf4j
public class RocketMQProducerConfig {

    @Value("${rocketmq.name-server:127.0.0.1:9876}")
    private String nameServer;

    @Value("${rocketmq.producer.group:user-center-producer-group}")
    private String producerGroup;

    @Value("${rocketmq.producer.send-message-timeout:3000}")
    private int sendMessageTimeout;

    @Value("${rocketmq.producer.retry-times-when-send-failed:2}")
    private int retryTimesWhenSendFailed;

    @Value("${rocketmq.producer.max-message-size:4194304}")
    private int maxMessageSize;

    /**
     * 简历解析专用生产者
     */
    @Bean("resumeParseProducer")
    public DefaultMQProducer resumeParseProducer() {
        DefaultMQProducer producer = new DefaultMQProducer("resume-parse-producer-group");
        producer.setNamesrvAddr(nameServer);

        // 优化配置
        producer.setRetryTimesWhenSendFailed(retryTimesWhenSendFailed);
        producer.setSendMsgTimeout(sendMessageTimeout);
        producer.setMaxMessageSize(maxMessageSize);

        // 批量发送配置
        producer.setDefaultTopicQueueNums(4); // 默认队列数

        try {
            producer.start();
            log.info("RocketMQ简历解析生产者启动成功: nameServer={}, group={}", 
                    nameServer, "resume-parse-producer-group");
        } catch (MQClientException e) {
            log.error("RocketMQ简历解析生产者启动失败", e);
            throw new RuntimeException("RocketMQ生产者启动失败", e);
        }

        return producer;
    }

    /**
     * 文件上传专用生产者
     */
    @Bean("fileUploadProducer")
    public DefaultMQProducer fileUploadProducer() {
        DefaultMQProducer producer = new DefaultMQProducer("file-upload-producer-group");
        producer.setNamesrvAddr(nameServer);

        // 文件上传专用配置
        producer.setRetryTimesWhenSendFailed(3);
        producer.setSendMsgTimeout(10000); // 文件上传允许更长超时
        producer.setMaxMessageSize(1024); // 只传递文件元信息，不传文件内容

        try {
            producer.start();
            log.info("RocketMQ文件上传生产者启动成功: nameServer={}, group={}", 
                    nameServer, "file-upload-producer-group");
        } catch (MQClientException e) {
            log.error("RocketMQ文件上传生产者启动失败", e);
            throw new RuntimeException("RocketMQ生产者启动失败", e);
        }

        return producer;
    }

    /**
     * 通用生产者
     */
    @Bean("generalProducer")
    public DefaultMQProducer generalProducer() {
        DefaultMQProducer producer = new DefaultMQProducer(producerGroup);
        producer.setNamesrvAddr(nameServer);

        // 通用配置
        producer.setRetryTimesWhenSendFailed(retryTimesWhenSendFailed);
        producer.setSendMsgTimeout(sendMessageTimeout);
        producer.setMaxMessageSize(maxMessageSize);

        try {
            producer.start();
            log.info("RocketMQ通用生产者启动成功: nameServer={}, group={}", 
                    nameServer, producerGroup);
        } catch (MQClientException e) {
            log.error("RocketMQ通用生产者启动失败", e);
            throw new RuntimeException("RocketMQ生产者启动失败", e);
        }

        return producer;
    }
}
