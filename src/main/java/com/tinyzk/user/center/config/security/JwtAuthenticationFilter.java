package com.tinyzk.user.center.config.security;

import com.tinyzk.user.center.common.util.JwtUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Slf4j
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private static final String TOKEN_PREFIX = "Bearer ";
    private static final String HEADER_STRING = "Authorization";

    public JwtAuthenticationFilter() {
        // If there were other final fields, they would need initialization here or a new constructor
    }

    @Override
    protected void doFilterInternal(
            @NonNull HttpServletRequest request,
            @NonNull HttpServletResponse response,
            @NonNull FilterChain filterChain) throws ServletException, IOException {

        String header = request.getHeader(HEADER_STRING);
        String authToken = null;

        if (StringUtils.hasText(header) && header.startsWith(TOKEN_PREFIX)) {
            authToken = header.substring(TOKEN_PREFIX.length());
            try {
                Long userId = JwtUtil.getUserIdFromToken(authToken);
                if (userId != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                    
                    UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                            userId, null, null); // principal 为 userId, credentials 为 null, authorities 为 null
                    authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                    log.debug("JWT Token 验证通过, userId: {}", userId);
                }
            } catch (Exception e) {
                log.warn("JWT Token 解析或验证失败: {}", e.getMessage());
            }
        } else {
            log.trace("请求头中未找到有效的JWT Token");
        }

        filterChain.doFilter(request, response);
    }
} 