package com.tinyzk.user.center.service;

import com.tinyzk.user.center.dto.RealNameAuthDTO;
import com.tinyzk.user.center.vo.RealNameAuthVO;

/**
 * 用户实名认证服务接口
 */
public interface UserRealNameAuthService {
    
    /**
     * 提交实名认证信息
     *
     * @param realNameAuthDTO 实名认证信息 DTO
     * @return 实名认证结果 VO
     */
    RealNameAuthVO submit(RealNameAuthDTO realNameAuthDTO);
    
    /**
     * 根据用户ID查询实名认证信息
     *
     * @param userId 用户ID
     * @return 实名认证结果 VO
     */
    RealNameAuthVO getByUserId(Long userId);

    /**
     * 处理账户合并逻辑
     * 当用户完成实名认证后，检查并将相同身份信息的账户合并到目标账户
     *
     * @param sourceUserId 当前完成实名认证的用户ID
     * @param encryptedRealName 加密后的真实姓名
     * @param encryptedIdNumber 加密后的身份证号
     * @return 如果执行了合并操作，返回合并后的认证VO；如果没有合并，返回null
     */
    RealNameAuthVO processAccountMerge(Long sourceUserId, String encryptedRealName, String encryptedIdNumber);
}
