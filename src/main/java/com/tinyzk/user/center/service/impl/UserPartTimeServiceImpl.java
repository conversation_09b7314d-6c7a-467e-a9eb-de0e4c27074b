package com.tinyzk.user.center.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tinyzk.user.center.common.annotation.UserAudit;
import com.tinyzk.user.center.common.enums.OperationType;
import com.tinyzk.user.center.common.exception.BusinessException;
import com.tinyzk.user.center.common.exception.ErrorCode;
import com.tinyzk.user.center.dto.CreatePartTimeDTO;
import com.tinyzk.user.center.dto.UpdatePartTimeDTO;
import com.tinyzk.user.center.entity.UserPartTime;
import com.tinyzk.user.center.mapper.UserPartTimeMapper;
import com.tinyzk.user.center.service.UserPartTimeService;
import com.tinyzk.user.center.vo.UserPartTimeVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户兼职经历服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserPartTimeServiceImpl implements UserPartTimeService {

    private final UserPartTimeMapper userPartTimeMapper;

    @Override
    public List<UserPartTimeVO> getUserPartTimes(Long userId) {
        // 查询用户所有兼职经历
        List<UserPartTime> partTimes = userPartTimeMapper.selectList(new LambdaQueryWrapper<UserPartTime>()
                .eq(UserPartTime::getUserId, userId));
        
        // 转换为VO
        return partTimes.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public UserPartTimeVO getUserPartTime(Long userId, Long partTimeId) {
        // 查询特定兼职经历
        UserPartTime partTime = userPartTimeMapper.selectById(partTimeId);
        
        // 检查是否存在
        if (partTime == null || !partTime.getUserId().equals(userId)) {
            throw new BusinessException(ErrorCode.NOT_FOUND, "兼职经历不存在");
        }
        
        // 转换为VO
        return convertToVO(partTime);
    }

    @Override
    @Transactional
    @UserAudit(type = OperationType.PART_TIME_CREATE,failType = OperationType.PART_TIME_CREATE_FAIL, detail = "创建用户兼职经历")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public UserPartTimeVO createUserPartTime(Long userId, CreatePartTimeDTO createPartTimeDTO) {
        // 创建实体对象
        UserPartTime partTime = new UserPartTime();
        
        // 拷贝属性
        BeanUtils.copyProperties(createPartTimeDTO, partTime);
        partTime.setUserId(userId);
        partTime.setCreatedAt(LocalDateTime.now());
        partTime.setUpdatedAt(LocalDateTime.now());
        
        // 插入数据库
        userPartTimeMapper.insert(partTime);
        
        // 返回VO
        return convertToVO(partTime);
    }

    @Override
    @Transactional
    @UserAudit(type = OperationType.PART_TIME_UPDATE,failType = OperationType.PART_TIME_UPDATE_FAIL, detail = "更新用户兼职经历")
    public UserPartTimeVO updateUserPartTime(Long userId, Long partTimeId, UpdatePartTimeDTO updatePartTimeDTO) {
        // 查询原兼职经历
        UserPartTime partTime = userPartTimeMapper.selectById(partTimeId);
        
        // 检查是否存在
        if (partTime == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND, "兼职经历不存在");
        }
        
        // 检查权限
        if (!partTime.getUserId().equals(userId)) {
            throw new BusinessException(ErrorCode.FORBIDDEN, "无权修改该兼职经历");
        }
        
        // 更新属性
        BeanUtils.copyProperties(updatePartTimeDTO, partTime);
        partTime.setUpdatedAt(LocalDateTime.now());
        
        // 更新数据库
        userPartTimeMapper.updateById(partTime);
        
        // 返回VO
        return convertToVO(partTime);
    }

    @Override
    @Transactional
    @UserAudit(type = OperationType.PART_TIME_DELETE,failType = OperationType.PART_TIME_DELETE_FAIL, detail = "删除用户兼职经历")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public void deleteUserPartTime(Long userId, Long partTimeId) {
        // 查询原兼职经历
        UserPartTime partTime = userPartTimeMapper.selectById(partTimeId);
        
        // 检查是否存在
        if (partTime == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND, "兼职经历不存在");
        }
        
        // 检查权限
        if (!partTime.getUserId().equals(userId)) {
            throw new BusinessException(ErrorCode.FORBIDDEN, "无权删除该兼职经历");
        }
        
        // 删除兼职经历（逻辑删除）
        userPartTimeMapper.deleteById(partTimeId);
    }
    
    /**
     * 将实体对象转换为VO
     */
    private UserPartTimeVO convertToVO(UserPartTime partTime) {
        UserPartTimeVO vo = new UserPartTimeVO();
        BeanUtils.copyProperties(partTime, vo);
        return vo;
    }
} 