package com.tinyzk.user.center.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tinyzk.user.center.dto.CreateProfileDTO;
import com.tinyzk.user.center.dto.UpdateProfileDTO;
import com.tinyzk.user.center.entity.UserProfile;
import com.tinyzk.user.center.vo.UserProfileVO;

/**
 * 用户个人资料服务接口
 */
public interface UserProfileService extends IService<UserProfile> {

    /**
     * 查询用户个人资料
     *
     * @param userId 用户ID
     * @return 用户个人资料视图对象
     */
    UserProfileVO getUserProfile(Long userId);

    /**
     * 创建用户资料
     *
     * @param userId 用户ID
     * @param createProfileDTO 创建资料请求
     * @return 创建后的用户资料
     */
    UserProfileVO createUserProfile(Long userId, CreateProfileDTO createProfileDTO);

    /**
     * 更新用户个人资料
     *
     * @param userId 用户ID
     * @param updateProfileDTO 更新信息
     * @return 更新后的用户个人资料视图对象
     */
    UserProfileVO updateUserProfile(Long userId, UpdateProfileDTO updateProfileDTO);

    /**
     * 删除用户资料
     *
     * @param userId 用户ID
     */
    void deleteUserProfile(Long userId);

}