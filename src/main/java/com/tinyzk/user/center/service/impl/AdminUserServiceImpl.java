package com.tinyzk.user.center.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tinyzk.user.center.dto.UpdateUserRequestDTO;
import com.tinyzk.user.center.dto.UserListRequestDTO;
import com.tinyzk.user.center.entity.UserBase;
import com.tinyzk.user.center.mapper.AdminUserMapper;
import com.tinyzk.user.center.service.AdminUserService;
import com.tinyzk.user.center.common.util.SensitiveInfoUtil;
import com.tinyzk.user.center.vo.PageResponseVO;
import com.tinyzk.user.center.vo.UserDetailVO;
import com.tinyzk.user.center.vo.UserListVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.cache.annotation.Caching;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 管理员用户服务实现类
 */
@Service
public class AdminUserServiceImpl implements AdminUserService {

    private static final Logger logger = LoggerFactory.getLogger(AdminUserServiceImpl.class);

    @Autowired
    private AdminUserMapper adminUserMapper;
    
    /**
     * 获取用户列表
     *
     * @param requestDTO 请求参数
     * @return 用户列表分页数据
     */
    @Override
    @Cacheable(value = "userListCache", keyGenerator = "userListKeyGenerator", unless = "#result == null || #result.getData() == null || #result.getData().isEmpty()")
    public PageResponseVO<UserListVO> getUserList(UserListRequestDTO requestDTO) {
        logger.info("Fetching user list from DB for request: {}", requestDTO);
        // 创建分页对象
        Page<UserListVO> page = new Page<>(requestDTO.getPageNumber(), requestDTO.getPageSize());
        
        // 执行分页连接查询
        IPage<UserListVO> userListPage = adminUserMapper.selectUserListPage(page, requestDTO);
        
        // 如果没有数据，直接返回，避免后续处理和可能的空指针
        if (userListPage == null || userListPage.getRecords() == null || userListPage.getRecords().isEmpty()) {
            return new PageResponseVO<>(new ArrayList<>(), requestDTO.getPageNumber(), 
                    requestDTO.getPageSize(), userListPage != null ? userListPage.getTotal() : 0);
        }
        
        // 获取用户ID列表
        List<Long> userIds = userListPage.getRecords().stream()
                .map(UserListVO::getUserId)
                .collect(Collectors.toList());
        
        // 查询用户标识信息
        List<UserListVO.IdentifierVO> allIdentifiers = adminUserMapper.selectUserIdentifiers(userIds);
        
        // 按用户ID分组标识信息
        Map<Long, List<UserListVO.IdentifierVO>> identifierMap = new HashMap<>();
        if (allIdentifiers != null) { // 添加非空检查
            for (UserListVO.IdentifierVO identifier : allIdentifiers) {
                Long userId = identifier.getUserId();
                if (userId != null) {
                    identifierMap.computeIfAbsent(userId, k -> new ArrayList<>()).add(identifier);
                }
            }
        }
        
        // 处理每个用户的标识信息（脱敏）
        for (UserListVO userListVO : userListPage.getRecords()) {
            List<UserListVO.IdentifierVO> identifiers = identifierMap.get(userListVO.getUserId());
            if (identifiers != null && !identifiers.isEmpty()) {
                // 对标识进行脱敏处理
                identifiers.forEach(identifier -> {
                    identifier.setValue(SensitiveInfoUtil.maskIdentifier(
                            identifier.getType(), identifier.getValue()));
                });
                userListVO.setIdentifiers(identifiers);
            } else {
                userListVO.setIdentifiers(new ArrayList<>());
            }
        }
        
        // 返回分页结果
        return new PageResponseVO<>(userListPage.getRecords(), requestDTO.getPageNumber(), 
                requestDTO.getPageSize(), userListPage.getTotal());
    }
    
    /**
     * 获取用户详情
     *
     * @param userId 用户ID
     * @return 用户详情信息
     */
    @Override
    @Cacheable(value = "userDetailCache", key = "#userId", unless = "#result == null")
    public UserDetailVO getUserDetail(Long userId) {
        // 创建用户详情对象
        UserDetailVO userDetailVO = new UserDetailVO();
        
        // 查询用户基础信息
        UserDetailVO.UserBaseInfo userBaseInfo = adminUserMapper.selectUserBaseInfo(userId);
        if (userBaseInfo == null) {
            return null; // 用户不存在
        }
        
        // 对敏感信息进行脱敏处理
        if (userBaseInfo.getRealName() != null) {
            // 对真实姓名进行脱敏处理，保留第一个字，其余用*代替
            String realName = userBaseInfo.getRealName();
            if (realName.length() > 1) {
                String maskedName = realName.substring(0, 1);
                for (int i = 1; i < realName.length(); i++) {
                    maskedName += "*";
                }
                userBaseInfo.setRealName(maskedName);
            }
        }
        
        if (userBaseInfo.getIdCardNumber() != null) {
            // 对身份证号进行脱敏处理，保留前6位和后4位，中间用*代替
            String idCardNumber = userBaseInfo.getIdCardNumber();
            if (idCardNumber.length() >= 11) { // 身份证号应为18位
                String maskedIdCard = idCardNumber.substring(0, 6) + "********" + 
                        idCardNumber.substring(idCardNumber.length() - 4);
                userBaseInfo.setIdCardNumber(maskedIdCard);
            }
        }
        
        userDetailVO.setUserBase(userBaseInfo);
        
        // 查询用户资料信息
        UserDetailVO.UserProfileInfo userProfileInfo = adminUserMapper.selectUserProfileInfo(userId);
        userDetailVO.setUserProfile(userProfileInfo);
        
        // 查询用户认证信息
        List<UserDetailVO.UserAuthInfo> userAuthInfos = adminUserMapper.selectUserAuthInfo(userId);
        if (userAuthInfos != null && !userAuthInfos.isEmpty()) {
            // 对认证信息进行脱敏处理
            userAuthInfos.forEach(authInfo -> {
                String identityType = authInfo.getIdentityType();
                String identifier = authInfo.getIdentifier();
                if (identifier != null) {
                    authInfo.setIdentifier(SensitiveInfoUtil.maskIdentifier(identityType, identifier));
                }
            });
        }
        userDetailVO.setUserAuths(userAuthInfos);
        
        // 查询用户联系方式信息
        List<UserDetailVO.UserContactInfo> userContactInfos = adminUserMapper.selectUserContactInfo(userId);
        if (userContactInfos != null && !userContactInfos.isEmpty()) {
            // 对联系方式进行脱敏处理
            userContactInfos.forEach(contactInfo -> {
                String contactValue = contactInfo.getContactValue();
                if (contactValue != null) {
                    // 简单脱敏处理，保留前后各四分之一的字符，中间用*代替
                    int length = contactValue.length();
                    int prefixLength = Math.max(1, length / 4);
                    int suffixLength = Math.max(1, length / 4);
                    
                    if (length > prefixLength + suffixLength) {
                        String prefix = contactValue.substring(0, prefixLength);
                        String suffix = contactValue.substring(length - suffixLength);
                        String masked = prefix + "****" + suffix;
                        contactInfo.setContactValue(masked);
                    }
                }
            });
        }
        userDetailVO.setUserContactMethods(userContactInfos);
        
        // 查询用户教育经历信息
        List<UserDetailVO.UserEducationInfo> userEducationInfos = adminUserMapper.selectUserEducationInfo(userId);
        userDetailVO.setUserEducationHistories(userEducationInfos);
        
        // 查询用户工作经历信息
        List<UserDetailVO.UserWorkInfo> userWorkInfos = adminUserMapper.selectUserWorkInfo(userId);
        userDetailVO.setUserWorkHistories(userWorkInfos);
        
        // 查询用户项目经历信息
        List<UserDetailVO.UserProjectInfo> userProjectInfos = adminUserMapper.selectUserProjectInfo(userId);
        userDetailVO.setUserProjectHistories(userProjectInfos);
        
        // 查询用户培训经历信息
        List<UserDetailVO.UserTrainingInfo> userTrainingInfos = adminUserMapper.selectUserTrainingInfo(userId);
        userDetailVO.setUserTrainingHistories(userTrainingInfos);
        
        // 查询用户兼职经历信息
        List<UserDetailVO.UserPartTimeInfo> userPartTimeInfos = adminUserMapper.selectUserPartTimeInfo(userId);
        userDetailVO.setUserPartTimeHistories(userPartTimeInfos);
        
        return userDetailVO;
    }
    
    /**
     * 更新用户信息
     *
     * @param userId 用户ID
     * @param requestDTO 更新用户信息请求DTO
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
        @CacheEvict(value = "userListCache", allEntries = true),
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public boolean updateUser(Long userId, UpdateUserRequestDTO requestDTO) {
        logger.info("Updating user {} and evicting userListCache", userId);
        // 检查用户是否存在
        UserDetailVO.UserBaseInfo userBaseInfo = adminUserMapper.selectUserBaseInfo(userId);
        if (userBaseInfo == null) {
            return false; // 用户不存在
        }
        
        boolean updated = false;
        
        // 更新用户基础信息
        if (requestDTO.getUserBase() != null) {
            int baseResult = adminUserMapper.updateUserBase(userId, requestDTO.getUserBase());
            updated = updated || baseResult > 0;
        }
        
        // 更新用户资料信息
        if (requestDTO.getUserProfile() != null) {
            int profileResult = adminUserMapper.updateUserProfile(userId, requestDTO.getUserProfile());
            updated = updated || profileResult > 0;
        }
        
        return updated;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
        @CacheEvict(value = "userListCache", allEntries = true),
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public boolean disableUser(Long userId, String reason) {
        logger.info("Disabling user {} with reason: {}", userId, reason);
        
        // 检查用户是否存在
        UserBase userBase = adminUserMapper.selectById(userId);
        if (userBase == null) {
            logger.warn("User not found: {}", userId);
            return false;
        }
        
        // 如果用户已经是禁用状态，直接返回成功
        if (userBase.getStatus() != null && userBase.getStatus() == 2) {
            logger.info("User {} is already disabled", userId);
            return true;
        }
        
        // 更新用户状态为禁用
        int result = adminUserMapper.updateUserStatus(userId, 2);
        boolean success = result > 0;
        
        if (success) {
            logger.info("User {} disabled successfully", userId);
            // TODO: 记录操作日志
            // auditLogService.logUserStatusChange(userId, "DISABLE", reason, getCurrentAdminId());
        } else {
            logger.error("Failed to disable user: {}", userId);
        }
        
        return success;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
        @CacheEvict(value = "userListCache", allEntries = true),
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public boolean enableUser(Long userId) {
        logger.info("Enabling user {}", userId);
        
        // 检查用户是否存在
        UserBase userBase = adminUserMapper.selectById(userId);
        if (userBase == null) {
            logger.warn("User not found: {}", userId);
            return false;
        }
        
        // 如果用户已经是启用状态，直接返回成功
        if (userBase.getStatus() != null && userBase.getStatus() == 1) {
            logger.info("User {} is already enabled", userId);
            return true;
        }
        
        // 更新用户状态为启用
        int result = adminUserMapper.updateUserStatus(userId, 1);
        boolean success = result > 0;
        
        if (success) {
            logger.info("User {} enabled successfully", userId);
            // TODO: 记录操作日志
            // auditLogService.logUserStatusChange(userId, "ENABLE", null, getCurrentAdminId());
        } else {
            logger.error("Failed to enable user: {}", userId);
        }
        
        return success;
    }
}
