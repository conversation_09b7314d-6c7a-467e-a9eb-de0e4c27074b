package com.tinyzk.user.center.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tinyzk.user.center.common.annotation.UserAudit;
import com.tinyzk.user.center.common.enums.OperationType;
import com.tinyzk.user.center.common.exception.BusinessException;
import com.tinyzk.user.center.common.exception.ErrorCode;
import com.tinyzk.user.center.dto.CreateEducationDTO;
import com.tinyzk.user.center.dto.UpdateEducationDTO;
import com.tinyzk.user.center.entity.UserEducationHistory;
import com.tinyzk.user.center.mapper.UserEducationHistoryMapper;
import com.tinyzk.user.center.service.UserEducationHistoryService;
import com.tinyzk.user.center.vo.UserEducationVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户教育经历服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserEducationHistoryServiceImpl extends ServiceImpl<UserEducationHistoryMapper, UserEducationHistory> implements UserEducationHistoryService {

    private final UserEducationHistoryMapper userEducationHistoryMapper;

    @Override
    public List<UserEducationVO> getUserEducations(Long userId) {
        log.info("获取用户教育经历列表: userId={}", userId);
        LambdaQueryWrapper<UserEducationHistory> queryWrapper = new LambdaQueryWrapper<UserEducationHistory>()
                .eq(UserEducationHistory::getUserId, userId)
                .orderByDesc(UserEducationHistory::getCreatedAt);
                
        List<UserEducationHistory> educationHistories = userEducationHistoryMapper.selectList(queryWrapper);
        
        List<UserEducationVO> educationVOs = educationHistories.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
                
        log.info("获取用户教育经历列表成功: userId={}, count={}", userId, educationVOs.size());
        return educationVOs;
    }

    @Override
    public UserEducationVO getUserEducation(Long userId, Long educationId) {
        log.info("获取用户教育经历: userId={}, educationId={}", userId, educationId);
        UserEducationHistory educationHistory = getUserEducationOrThrow(userId, educationId);
        UserEducationVO educationVO = convertToVO(educationHistory);
        log.info("获取用户教育经历成功: userId={}, educationId={}", userId, educationId);
        return educationVO;
    }

    @Override
    @UserAudit(type = OperationType.EDUCATION_CREATE,failType = OperationType.EDUCATION_CREATE_FAIL, detail = "创建用户教育经历")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public UserEducationVO createUserEducation(Long userId, CreateEducationDTO createEducationDTO) {
        log.info("创建用户教育经历: userId={}", userId);
        
        UserEducationHistory educationHistory = new UserEducationHistory();
        BeanUtils.copyProperties(createEducationDTO, educationHistory);
        educationHistory.setUserId(userId);
        
        userEducationHistoryMapper.insert(educationHistory);
        
        UserEducationVO educationVO = convertToVO(educationHistory);
        log.info("创建用户教育经历成功: userId={}, educationId={}", userId, educationVO.getEduId());
        return educationVO;
    }

    @Override
    @UserAudit(type = OperationType.EDUCATION_UPDATE,failType = OperationType.EDUCATION_UPDATE_FAIL, detail = "更新用户教育经历")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public UserEducationVO updateUserEducation(Long userId, Long educationId, UpdateEducationDTO updateEducationDTO) {
        log.info("更新用户教育经历: userId={}, educationId={}", userId, educationId);
        
        UserEducationHistory educationHistory = getUserEducationOrThrow(userId, educationId);
        
        // 只更新非null字段
        if (updateEducationDTO.getSchoolName() != null) {
            educationHistory.setSchoolName(updateEducationDTO.getSchoolName());
        }
        if (updateEducationDTO.getDegree() != null) {
            educationHistory.setDegree(updateEducationDTO.getDegree());
        }
        if (updateEducationDTO.getDegreeLevel() != null) {
            educationHistory.setDegreeLevel(updateEducationDTO.getDegreeLevel());
        }
        if (updateEducationDTO.getMajor() != null) {
            educationHistory.setMajor(updateEducationDTO.getMajor());
        }
        if (updateEducationDTO.getSecondaryMajor() != null) {
            educationHistory.setSecondaryMajor(updateEducationDTO.getSecondaryMajor());
        }
        if (updateEducationDTO.getMajorArea() != null) {
            educationHistory.setMajorArea(updateEducationDTO.getMajorArea());
        }
        if (updateEducationDTO.getMajorGpa() != null) {
            educationHistory.setMajorGpa(updateEducationDTO.getMajorGpa());
        }
        if (updateEducationDTO.getStartDate() != null) {
            educationHistory.setStartDate(updateEducationDTO.getStartDate());
        }
        if (updateEducationDTO.getEndDate() != null) {
            educationHistory.setEndDate(updateEducationDTO.getEndDate());
        }
        if (updateEducationDTO.getDescription() != null) {
            educationHistory.setDescription(updateEducationDTO.getDescription());
        }
        if (updateEducationDTO.getClubExperience() != null) {
            educationHistory.setClubExperience(updateEducationDTO.getClubExperience());
        }
        if (updateEducationDTO.getVisibility() != null) {
            educationHistory.setVisibility(updateEducationDTO.getVisibility());
        }
        
        userEducationHistoryMapper.updateById(educationHistory);
        
        UserEducationVO educationVO = convertToVO(educationHistory);
        log.info("更新用户教育经历成功: userId={}, educationId={}", userId, educationId);
        return educationVO;
    }

    @Override
    @UserAudit(type = OperationType.EDUCATION_DELETE,failType = OperationType.EDUCATION_DELETE_FAIL, detail = "删除用户教育经历")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public void deleteUserEducation(Long userId, Long educationId) {
        log.info("删除用户教育经历: userId={}, educationId={}", userId, educationId);
        
        UserEducationHistory educationHistory = getUserEducationOrThrow(userId, educationId);
        
        userEducationHistoryMapper.deleteById(educationId);
        
        log.info("删除用户教育经历成功: userId={}, educationId={}", userId, educationId);
    }
    
    /**
     * 获取用户教育经历，如果不存在则抛出异常
     */
    private UserEducationHistory getUserEducationOrThrow(Long userId, Long educationId) {
        LambdaQueryWrapper<UserEducationHistory> queryWrapper = new LambdaQueryWrapper<UserEducationHistory>()
                .eq(UserEducationHistory::getUserId, userId)
                .eq(UserEducationHistory::getEduId, educationId);
                
        UserEducationHistory educationHistory = userEducationHistoryMapper.selectOne(queryWrapper);
        
        if (educationHistory == null) {
            log.warn("用户教育经历不存在: userId={}, educationId={}", userId, educationId);
            throw new BusinessException(ErrorCode.NOT_FOUND, "教育经历不存在");
        }
        
        return educationHistory;
    }
    
    /**
     * 将实体转换为VO对象
     */
    private UserEducationVO convertToVO(UserEducationHistory educationHistory) {
        UserEducationVO educationVO = new UserEducationVO();
        BeanUtils.copyProperties(educationHistory, educationVO);
        return educationVO;
    }
} 