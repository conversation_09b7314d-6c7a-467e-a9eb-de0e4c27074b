package com.tinyzk.user.center.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tinyzk.user.center.dto.CreateProjectDTO;
import com.tinyzk.user.center.dto.UpdateProjectDTO;
import com.tinyzk.user.center.entity.UserProjectHistory;
import com.tinyzk.user.center.vo.UserProjectVO;

import java.util.List;

/**
 * 用户项目经历服务接口
 */
public interface UserProjectHistoryService extends IService<UserProjectHistory> {

    /**
     * 获取用户所有项目经历
     *
     * @param userId 用户ID
     * @return 项目经历列表
     */
    List<UserProjectVO> getUserProjects(Long userId);

    /**
     * 获取用户特定项目经历
     *
     * @param userId    用户ID
     * @param projectId 项目经历ID
     * @return 项目经历详情
     */
    UserProjectVO getUserProject(Long userId, Long projectId);

    /**
     * 创建用户项目经历
     *
     * @param userId         用户ID
     * @param createProjectDTO 创建项目经历请求
     * @return 创建后的项目经历
     */
    UserProjectVO createUserProject(Long userId, CreateProjectDTO createProjectDTO);

    /**
     * 更新用户项目经历
     *
     * @param userId         用户ID
     * @param projectId      项目经历ID
     * @param updateProjectDTO 更新项目经历请求
     * @return 更新后的项目经历
     */
    UserProjectVO updateUserProject(Long userId, Long projectId, UpdateProjectDTO updateProjectDTO);

    /**
     * 删除用户项目经历
     *
     * @param userId    用户ID
     * @param projectId 项目经历ID
     */
    void deleteUserProject(Long userId, Long projectId);
} 