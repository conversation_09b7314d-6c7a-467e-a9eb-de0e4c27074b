package com.tinyzk.user.center.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tinyzk.user.center.entity.OAuthClientDetails;
import com.tinyzk.user.center.mapper.OAuthClientDetailsMapper;
import com.tinyzk.user.center.service.ClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户端服务实现类
 */
@Service
public class ClientServiceImpl extends ServiceImpl<OAuthClientDetailsMapper, OAuthClientDetails> implements ClientService {

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * 验证客户端是否有效
     *
     * @param clientId 客户端ID
     * @return 客户端是否有效
     */
    @Override
    public boolean validateClient(String clientId) {
        if (clientId == null || clientId.isEmpty()) {
            return false;
        }
        
        LambdaQueryWrapper<OAuthClientDetails> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OAuthClientDetails::getClientId, clientId)
                .eq(OAuthClientDetails::getStatus, 1); // 状态为正常
        
        return this.count(queryWrapper) > 0;
    }

    /**
     * 获取客户端详情
     *
     * @param clientId 客户端ID
     * @return 客户端详情对象
     */
    @Override
    public OAuthClientDetails getClientDetails(String clientId) {
        if (clientId == null || clientId.isEmpty()) {
            return null;
        }
        
        return this.getById(clientId);
    }

    /**
     * 校验客户端ID和密钥是否匹配且有效
     *
     * @param clientId 客户端ID
     * @param clientSecret 客户端密钥
     * @return 是否有效
     */
    @Override
    public boolean validateClient(String clientId, String clientSecret) {
        if (clientId == null || clientId.isEmpty() || clientSecret == null || clientSecret.isEmpty()) {
            return false;
        }
        LambdaQueryWrapper<OAuthClientDetails> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OAuthClientDetails::getClientId, clientId)
                .eq(OAuthClientDetails::getStatus, 1); // 状态为正常
        OAuthClientDetails clientDetails = this.getOne(queryWrapper);
        if (clientDetails == null) {
            return false;
        }
        // 使用加密算法比对密钥
        return passwordEncoder.matches(clientSecret, clientDetails.getClientSecret());
    }

    /**
     * 保存或更新客户端
     *
     * @param clientDetails 客户端详情对象
     * @return 操作是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateClient(OAuthClientDetails clientDetails) {
        if (clientDetails == null || clientDetails.getClientId() == null || clientDetails.getClientId().isEmpty()) {
            return false;
        }

        // 加密 clientSecret（如果是明文且已变更）
        if (clientDetails.getClientSecret() != null && !clientDetails.getClientSecret().isEmpty()) {
            // 如果是新增，或 clientSecret 字段被修改，则加密
            OAuthClientDetails existing = this.getById(clientDetails.getClientId());
            if (existing == null || !passwordEncoder.matches(clientDetails.getClientSecret(), existing.getClientSecret())) {
                clientDetails.setClientSecret(passwordEncoder.encode(clientDetails.getClientSecret()));
            }
        }

        // 设置创建和更新时间
        if (this.getById(clientDetails.getClientId()) == null) {
            // 新增客户端
            clientDetails.setCreatedAt(LocalDateTime.now());
            clientDetails.setUpdatedAt(LocalDateTime.now());
            if (clientDetails.getStatus() == null) {
                clientDetails.setStatus(1); // 默认状态为正常
            }
        } else {
            // 更新客户端
            clientDetails.setUpdatedAt(LocalDateTime.now());
        }

        return this.saveOrUpdate(clientDetails);
    }

    /**
     * 禁用客户端
     *
     * @param clientId 客户端ID
     * @return 操作是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableClient(String clientId) {
        if (clientId == null || clientId.isEmpty()) {
            return false;
        }
        
        OAuthClientDetails clientDetails = this.getById(clientId);
        if (clientDetails == null) {
            return false;
        }
        
        clientDetails.setStatus(0); // 设置状态为禁用
        clientDetails.setUpdatedAt(LocalDateTime.now());
        
        return this.updateById(clientDetails);
    }

    /**
     * 启用客户端
     *
     * @param clientId 客户端ID
     * @return 操作是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableClient(String clientId) {
        if (clientId == null || clientId.isEmpty()) {
            return false;
        }
        
        OAuthClientDetails clientDetails = this.getById(clientId);
        if (clientDetails == null) {
            return false;
        }
        
        clientDetails.setStatus(1); // 设置状态为正常
        clientDetails.setUpdatedAt(LocalDateTime.now());
        
        return this.updateById(clientDetails);
    }

    /**
     * 查询所有有效的客户端
     *
     * @return 客户端详情列表
     */
    @Override
    public List<OAuthClientDetails> listActiveClients() {
        LambdaQueryWrapper<OAuthClientDetails> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OAuthClientDetails::getStatus, 1); // 状态为正常
        
        return this.list(queryWrapper);
    }
} 
