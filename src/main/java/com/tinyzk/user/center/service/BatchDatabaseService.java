package com.tinyzk.user.center.service;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

/**
 * 数据库批量操作服务
 * 提供智能分批处理和连接池监控
 */
@Service
@Slf4j
public class BatchDatabaseService {

    private final DataSource dataSource;
    private final ThreadPoolTaskExecutor batchResumeParseExecutor;
    private final MeterRegistry meterRegistry;

    // 批量操作配置
    private static final int DEFAULT_BATCH_SIZE = 1000;
    private static final int MAX_BATCH_SIZE = 5000;
    private static final int MIN_BATCH_SIZE = 100;

    public BatchDatabaseService(DataSource dataSource,
                               @Qualifier("batchResumeParseExecutor") ThreadPoolTaskExecutor executor,
                               MeterRegistry meterRegistry) {
        this.dataSource = dataSource;
        this.batchResumeParseExecutor = executor;
        this.meterRegistry = meterRegistry;
    }

    /**
     * 智能批量插入
     */
    public <T> CompletableFuture<Integer> batchInsert(List<T> entities, BaseMapper<T> mapper) {
        return batchInsert(entities, mapper, calculateOptimalBatchSize(entities.size()));
    }

    /**
     * 指定批次大小的批量插入
     */
    public <T> CompletableFuture<Integer> batchInsert(List<T> entities, BaseMapper<T> mapper, int batchSize) {
        return CompletableFuture.supplyAsync(() -> {
            Timer.Sample sample = Timer.start(meterRegistry);
            AtomicInteger totalInserted = new AtomicInteger(0);

            try {
                List<List<T>> batches = splitIntoBatches(entities, batchSize);
                
                log.info("开始批量插入: 总记录数={}, 批次数={}, 批次大小={}", 
                        entities.size(), batches.size(), batchSize);

                for (int i = 0; i < batches.size(); i++) {
                    List<T> batch = batches.get(i);
                    
                    try {
                        int inserted = insertBatch(batch, mapper);
                        totalInserted.addAndGet(inserted);
                        
                        log.debug("批次 {}/{} 插入完成: 插入数量={}", i + 1, batches.size(), inserted);
                        
                        // 记录批次指标
                        meterRegistry.counter("database.batch.insert.success",
                            "batch_size", String.valueOf(batch.size())).increment();
                            
                    } catch (Exception e) {
                        log.error("批次 {}/{} 插入失败", i + 1, batches.size(), e);
                        meterRegistry.counter("database.batch.insert.failure",
                            "batch_size", String.valueOf(batch.size())).increment();
                        throw e;
                    }
                }

                log.info("批量插入完成: 总插入数量={}", totalInserted.get());
                return totalInserted.get();

            } catch (Exception e) {
                log.error("批量插入失败: 总记录数={}", entities.size(), e);
                meterRegistry.counter("database.batch.insert.error").increment();
                throw new RuntimeException("批量插入失败", e);
            } finally {
                sample.stop(Timer.builder("database.batch.insert.duration")
                    .tag("total_records", String.valueOf(entities.size()))
                    .tag("batch_size", String.valueOf(batchSize))
                    .register(meterRegistry));
            }
        }, batchResumeParseExecutor);
    }

    /**
     * 智能批量更新
     */
    public <T> CompletableFuture<Integer> batchUpdate(List<T> entities, BaseMapper<T> mapper) {
        return batchUpdate(entities, mapper, calculateOptimalBatchSize(entities.size()));
    }

    /**
     * 指定批次大小的批量更新
     */
    public <T> CompletableFuture<Integer> batchUpdate(List<T> entities, BaseMapper<T> mapper, int batchSize) {
        return CompletableFuture.supplyAsync(() -> {
            Timer.Sample sample = Timer.start(meterRegistry);
            AtomicInteger totalUpdated = new AtomicInteger(0);

            try {
                List<List<T>> batches = splitIntoBatches(entities, batchSize);
                
                log.info("开始批量更新: 总记录数={}, 批次数={}, 批次大小={}", 
                        entities.size(), batches.size(), batchSize);

                for (int i = 0; i < batches.size(); i++) {
                    List<T> batch = batches.get(i);
                    
                    try {
                        int updated = updateBatch(batch, mapper);
                        totalUpdated.addAndGet(updated);
                        
                        log.debug("批次 {}/{} 更新完成: 更新数量={}", i + 1, batches.size(), updated);
                        
                        meterRegistry.counter("database.batch.update.success",
                            "batch_size", String.valueOf(batch.size())).increment();
                            
                    } catch (Exception e) {
                        log.error("批次 {}/{} 更新失败", i + 1, batches.size(), e);
                        meterRegistry.counter("database.batch.update.failure",
                            "batch_size", String.valueOf(batch.size())).increment();
                        throw e;
                    }
                }

                log.info("批量更新完成: 总更新数量={}", totalUpdated.get());
                return totalUpdated.get();

            } catch (Exception e) {
                log.error("批量更新失败: 总记录数={}", entities.size(), e);
                meterRegistry.counter("database.batch.update.error").increment();
                throw new RuntimeException("批量更新失败", e);
            } finally {
                sample.stop(Timer.builder("database.batch.update.duration")
                    .tag("total_records", String.valueOf(entities.size()))
                    .tag("batch_size", String.valueOf(batchSize))
                    .register(meterRegistry));
            }
        }, batchResumeParseExecutor);
    }

    /**
     * 事务性批量操作
     */
    @Transactional(rollbackFor = Exception.class)
    public <T> void transactionalBatchOperation(List<T> entities, Consumer<List<T>> operation) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            log.info("开始事务性批量操作: 记录数={}", entities.size());
            
            operation.accept(entities);
            
            log.info("事务性批量操作完成: 记录数={}", entities.size());
            meterRegistry.counter("database.batch.transaction.success").increment();

        } catch (Exception e) {
            log.error("事务性批量操作失败: 记录数={}", entities.size(), e);
            meterRegistry.counter("database.batch.transaction.failure").increment();
            throw e;
        } finally {
            sample.stop(Timer.builder("database.batch.transaction.duration")
                .tag("total_records", String.valueOf(entities.size()))
                .register(meterRegistry));
        }
    }

    /**
     * 计算最优批次大小
     */
    private int calculateOptimalBatchSize(int totalRecords) {
        if (totalRecords <= MIN_BATCH_SIZE) {
            return totalRecords;
        }
        
        // 根据总记录数动态调整批次大小
        if (totalRecords <= 10000) {
            return DEFAULT_BATCH_SIZE;
        } else if (totalRecords <= 100000) {
            return Math.min(DEFAULT_BATCH_SIZE * 2, MAX_BATCH_SIZE);
        } else {
            return MAX_BATCH_SIZE;
        }
    }

    /**
     * 将列表分割为批次
     */
    private <T> List<List<T>> splitIntoBatches(List<T> list, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        
        for (int i = 0; i < list.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, list.size());
            batches.add(new ArrayList<>(list.subList(i, endIndex)));
        }
        
        return batches;
    }

    /**
     * 执行批次插入
     */
    @Transactional(rollbackFor = Exception.class)
    protected <T> int insertBatch(List<T> batch, BaseMapper<T> mapper) {
        int insertedCount = 0;
        for (T entity : batch) {
            insertedCount += mapper.insert(entity);
        }
        return insertedCount;
    }

    /**
     * 执行批次更新
     */
    @Transactional(rollbackFor = Exception.class)
    protected <T> int updateBatch(List<T> batch, BaseMapper<T> mapper) {
        int updatedCount = 0;
        for (T entity : batch) {
            updatedCount += mapper.updateById(entity);
        }
        return updatedCount;
    }

    /**
     * 获取数据库连接池状态
     */
    public DatabaseConnectionPoolStatus getConnectionPoolStatus() {
        try {
            DatabaseConnectionPoolStatus status = new DatabaseConnectionPoolStatus();
            
            // 这里需要根据实际使用的连接池类型来获取状态
            // 例如：HikariCP, Druid等
            if (dataSource instanceof com.zaxxer.hikari.HikariDataSource) {
                com.zaxxer.hikari.HikariDataSource hikariDS = (com.zaxxer.hikari.HikariDataSource) dataSource;
                com.zaxxer.hikari.HikariPoolMXBean poolBean = hikariDS.getHikariPoolMXBean();
                
                status.setActiveConnections(poolBean.getActiveConnections());
                status.setIdleConnections(poolBean.getIdleConnections());
                status.setTotalConnections(poolBean.getTotalConnections());
                status.setMaximumPoolSize(hikariDS.getMaximumPoolSize());
                status.setMinimumIdle(hikariDS.getMinimumIdle());
            }
            
            return status;
            
        } catch (Exception e) {
            log.warn("获取连接池状态失败", e);
            return new DatabaseConnectionPoolStatus();
        }
    }

    /**
     * 检查数据库连接
     */
    public boolean checkDatabaseConnection() {
        try (Connection connection = dataSource.getConnection()) {
            return connection.isValid(5); // 5秒超时
        } catch (SQLException e) {
            log.error("数据库连接检查失败", e);
            return false;
        }
    }

    /**
     * 数据库连接池状态
     */
    public static class DatabaseConnectionPoolStatus {
        private int activeConnections;
        private int idleConnections;
        private int totalConnections;
        private int maximumPoolSize;
        private int minimumIdle;

        // Getters and Setters
        public int getActiveConnections() { return activeConnections; }
        public void setActiveConnections(int activeConnections) { this.activeConnections = activeConnections; }
        public int getIdleConnections() { return idleConnections; }
        public void setIdleConnections(int idleConnections) { this.idleConnections = idleConnections; }
        public int getTotalConnections() { return totalConnections; }
        public void setTotalConnections(int totalConnections) { this.totalConnections = totalConnections; }
        public int getMaximumPoolSize() { return maximumPoolSize; }
        public void setMaximumPoolSize(int maximumPoolSize) { this.maximumPoolSize = maximumPoolSize; }
        public int getMinimumIdle() { return minimumIdle; }
        public void setMinimumIdle(int minimumIdle) { this.minimumIdle = minimumIdle; }
        
        public double getConnectionUsageRatio() {
            return maximumPoolSize > 0 ? (double) activeConnections / maximumPoolSize : 0.0;
        }
    }
}
