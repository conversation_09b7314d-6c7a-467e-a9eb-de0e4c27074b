package com.tinyzk.user.center.service;

import com.tinyzk.user.center.dto.CreateTrainingDTO;
import com.tinyzk.user.center.dto.UpdateTrainingDTO;
import com.tinyzk.user.center.vo.UserTrainingVO;

import java.util.List;

/**
 * 用户培训经历服务接口
 */
public interface UserTrainingService {

    /**
     * 获取用户培训经历列表
     *
     * @param userId 用户ID
     * @return 培训经历列表
     */
    List<UserTrainingVO> getUserTrainings(Long userId);

    /**
     * 获取用户特定培训经历
     *
     * @param userId     用户ID
     * @param trainingId 培训经历ID
     * @return 培训经历详情
     */
    UserTrainingVO getUserTraining(Long userId, Long trainingId);

    /**
     * 创建用户培训经历
     *
     * @param userId          用户ID
     * @param createTrainingDTO 创建培训经历信息
     * @return 创建后的培训经历
     */
    UserTrainingVO createUserTraining(Long userId, CreateTrainingDTO createTrainingDTO);

    /**
     * 更新用户培训经历
     *
     * @param userId          用户ID
     * @param trainingId       培训经历ID
     * @param updateTrainingDTO 更新信息
     * @return 更新后的培训经历
     */
    UserTrainingVO updateUserTraining(Long userId, Long trainingId, UpdateTrainingDTO updateTrainingDTO);

    /**
     * 删除用户培训经历
     *
     * @param userId     用户ID
     * @param trainingId 培训经历ID
     */
    void deleteUserTraining(Long userId, Long trainingId);
} 