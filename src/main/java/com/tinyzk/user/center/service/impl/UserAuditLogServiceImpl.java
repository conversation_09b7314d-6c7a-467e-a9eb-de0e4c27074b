package com.tinyzk.user.center.service.impl;

import com.tinyzk.user.center.common.enums.OperationType;
import com.tinyzk.user.center.entity.UserAuditLog;
import com.tinyzk.user.center.mapper.UserAuditLogMapper;
import com.tinyzk.user.center.service.UserAuditLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 用户审计日志服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserAuditLogServiceImpl implements UserAuditLogService {
    
    private final UserAuditLogMapper userAuditLogMapper;
    
    @Override
    public UserAuditLog recordLog(Long userId, OperationType operationType, String detail, String ipAddress, String userAgent) {
        UserAuditLog auditLog = new UserAuditLog();
        auditLog.setUserId(userId);
        auditLog.setOperationType(operationType.name());
        auditLog.setOperationDetail(detail);
        auditLog.setIpAddress(ipAddress);
        auditLog.setUserAgent(userAgent);
        
        userAuditLogMapper.insert(auditLog);
        log.info("记录用户审计日志: userId={}, operationType={}", userId, operationType);
        
        return auditLog;
    }
}
