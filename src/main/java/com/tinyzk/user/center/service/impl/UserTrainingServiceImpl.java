package com.tinyzk.user.center.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tinyzk.user.center.common.annotation.UserAudit;
import com.tinyzk.user.center.common.enums.OperationType;
import com.tinyzk.user.center.common.exception.BusinessException;
import com.tinyzk.user.center.common.exception.ErrorCode;
import com.tinyzk.user.center.dto.CreateTrainingDTO;
import com.tinyzk.user.center.dto.UpdateTrainingDTO;
import com.tinyzk.user.center.entity.UserTraining;
import com.tinyzk.user.center.mapper.UserTrainingMapper;
import com.tinyzk.user.center.service.UserTrainingService;
import com.tinyzk.user.center.vo.UserTrainingVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户培训经历服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserTrainingServiceImpl implements UserTrainingService {

    private final UserTrainingMapper userTrainingMapper;

    @Override
    public List<UserTrainingVO> getUserTrainings(Long userId) {
        // 查询用户所有培训经历
        List<UserTraining> trainings = userTrainingMapper.selectList(new LambdaQueryWrapper<UserTraining>()
                .eq(UserTraining::getUserId, userId));
        
        // 转换为VO
        return trainings.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public UserTrainingVO getUserTraining(Long userId, Long trainingId) {
        // 查询特定培训经历
        UserTraining training = userTrainingMapper.selectById(trainingId);
        
        // 检查是否存在
        if (training == null || !training.getUserId().equals(userId)) {
            throw new BusinessException(ErrorCode.NOT_FOUND, "培训经历不存在");
        }
        
        // 转换为VO
        return convertToVO(training);
    }

    @Override
    @Transactional
    @UserAudit(type = OperationType.TRAINING_CREATE,failType = OperationType.TRAINING_CREATE_FAIL, detail = "创建用户培训经历")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public UserTrainingVO createUserTraining(Long userId, CreateTrainingDTO createTrainingDTO) {
        // 创建实体对象
        UserTraining training = new UserTraining();
        
        // 拷贝属性
        BeanUtils.copyProperties(createTrainingDTO, training);
        training.setUserId(userId);
        training.setCreatedAt(LocalDateTime.now());
        training.setUpdatedAt(LocalDateTime.now());
        
        // 插入数据库
        userTrainingMapper.insert(training);
        
        // 返回VO
        return convertToVO(training);
    }

    @Override
    @Transactional
    @UserAudit(type = OperationType.TRAINING_UPDATE,failType = OperationType.TRAINING_UPDATE_FAIL, detail = "更新用户培训经历")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public UserTrainingVO updateUserTraining(Long userId, Long trainingId, UpdateTrainingDTO updateTrainingDTO) {
        // 查询原培训经历
        UserTraining training = userTrainingMapper.selectById(trainingId);
        
        // 检查是否存在
        if (training == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND, "培训经历不存在");
        }
        
        // 检查权限
        if (!training.getUserId().equals(userId)) {
            throw new BusinessException(ErrorCode.FORBIDDEN, "无权修改该培训经历");
        }
        
        // 更新属性
        BeanUtils.copyProperties(updateTrainingDTO, training);
        training.setUpdatedAt(LocalDateTime.now());
        
        // 更新数据库
        userTrainingMapper.updateById(training);
        
        // 返回VO
        return convertToVO(training);
    }

    @Override
    @Transactional
    @UserAudit(type = OperationType.TRAINING_DELETE,failType = OperationType.TRAINING_DELETE_FAIL, detail = "删除用户培训经历")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public void deleteUserTraining(Long userId, Long trainingId) {
        // 查询原培训经历
        UserTraining training = userTrainingMapper.selectById(trainingId);
        
        // 检查是否存在
        if (training == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND, "培训经历不存在");
        }
        
        // 检查权限
        if (!training.getUserId().equals(userId)) {
            throw new BusinessException(ErrorCode.FORBIDDEN, "无权删除该培训经历");
        }
        
        // 删除培训经历（逻辑删除）
        userTrainingMapper.deleteById(trainingId);
    }
    
    /**
     * 将实体对象转换为VO
     */
    private UserTrainingVO convertToVO(UserTraining training) {
        UserTrainingVO vo = new UserTrainingVO();
        BeanUtils.copyProperties(training, vo);
        return vo;
    }
} 