package com.tinyzk.user.center.service;

import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 线程池监控服务
 */
@Component
@Slf4j
public class ThreadPoolMonitorService {

    private final MeterRegistry meterRegistry;
    private final Map<String, ThreadPoolTaskExecutor> threadPools;

    public ThreadPoolMonitorService(MeterRegistry meterRegistry,
                                   @Qualifier("taskExecutor") ThreadPoolTaskExecutor taskExecutor,
                                   @Qualifier("batchResumeParseExecutor") ThreadPoolTaskExecutor batchExecutor,
                                   @Qualifier("apiCallExecutor") ThreadPoolTaskExecutor apiExecutor,
                                   @Qualifier("cacheWarmupExecutor") ThreadPoolTaskExecutor cacheExecutor,
                                   @Qualifier("ossUploadExecutor") ThreadPoolTaskExecutor ossExecutor) {
        this.meterRegistry = meterRegistry;
        this.threadPools = Map.of(
            "taskExecutor", taskExecutor,
            "batchResumeParseExecutor", batchExecutor,
            "apiCallExecutor", apiExecutor,
            "cacheWarmupExecutor", cacheExecutor,
            "ossUploadExecutor", ossExecutor
        );

        // 注册线程池指标
        registerThreadPoolMetrics();
    }

    /**
     * 注册线程池监控指标
     */
    private void registerThreadPoolMetrics() {
        threadPools.forEach((name, executor) -> {
            // 活跃线程数
            Gauge.builder("threadpool.active.threads")
                .tag("pool", name)
                .description("Number of active threads in the thread pool")
                .register(meterRegistry, executor, e -> e.getActiveCount());

            // 队列大小
            Gauge.builder("threadpool.queue.size")
                .tag("pool", name)
                .description("Number of tasks in the thread pool queue")
                .register(meterRegistry, executor, e -> e.getQueueSize());

            // 线程池大小
            Gauge.builder("threadpool.pool.size")
                .tag("pool", name)
                .description("Current size of the thread pool")
                .register(meterRegistry, executor, e -> e.getPoolSize());

            // 最大线程池大小
            Gauge.builder("threadpool.max.pool.size")
                .tag("pool", name)
                .description("Maximum size of the thread pool")
                .register(meterRegistry, executor, e -> e.getMaxPoolSize());

            // 核心线程池大小
            Gauge.builder("threadpool.core.pool.size")
                .tag("pool", name)
                .description("Core size of the thread pool")
                .register(meterRegistry, executor, e -> e.getCorePoolSize());

            // 队列容量
            Gauge.builder("threadpool.queue.capacity")
                .tag("pool", name)
                .description("Capacity of the thread pool queue")
                .register(meterRegistry, executor, e -> e.getQueueCapacity());

            // 队列使用率
            Gauge.builder("threadpool.queue.usage.ratio")
                .tag("pool", name)
                .description("Usage ratio of the thread pool queue")
                .register(meterRegistry, executor, e -> {
                    int queueCapacity = e.getQueueCapacity();
                    if (queueCapacity <= 0) return 0.0;
                    return (double) e.getQueueSize() / queueCapacity;
                });

            // 线程池使用率
            Gauge.builder("threadpool.usage.ratio")
                .tag("pool", name)
                .description("Usage ratio of the thread pool")
                .register(meterRegistry, executor, e -> {
                    int maxPoolSize = e.getMaxPoolSize();
                    if (maxPoolSize <= 0) return 0.0;
                    return (double) e.getActiveCount() / maxPoolSize;
                });
        });

        log.info("线程池监控指标注册完成，监控线程池数量: {}", threadPools.size());
    }

    /**
     * 定期记录线程池状态
     */
    @Scheduled(fixedDelay = 60000) // 每分钟检查一次
    public void logThreadPoolStatus() {
        threadPools.forEach((name, executor) -> {
            int activeCount = executor.getActiveCount();
            int queueSize = executor.getQueueSize();
            int poolSize = executor.getPoolSize();
            int maxPoolSize = executor.getMaxPoolSize();
            int corePoolSize = executor.getCorePoolSize();
            int queueCapacity = executor.getQueueCapacity();

            // 计算使用率
            double poolUsageRatio = maxPoolSize > 0 ? (double) activeCount / maxPoolSize : 0.0;
            double queueUsageRatio = queueCapacity > 0 ? (double) queueSize / queueCapacity : 0.0;

            log.info("线程池状态 [{}]: 活跃={}/{}, 队列={}/{}, 池大小={}/{}, 核心={}, 池使用率={:.2f}%, 队列使用率={:.2f}%",
                name, activeCount, maxPoolSize, queueSize, queueCapacity, 
                poolSize, maxPoolSize, corePoolSize, 
                poolUsageRatio * 100, queueUsageRatio * 100);

            // 检查是否需要告警
            checkThreadPoolHealth(name, executor, poolUsageRatio, queueUsageRatio);
        });
    }

    /**
     * 检查线程池健康状态
     */
    private void checkThreadPoolHealth(String poolName, ThreadPoolTaskExecutor executor, 
                                     double poolUsageRatio, double queueUsageRatio) {
        // 线程池使用率过高告警
        if (poolUsageRatio > 0.9) {
            log.warn("线程池使用率过高告警: {} 使用率={:.2f}%, 活跃线程={}, 最大线程={}",
                poolName, poolUsageRatio * 100, executor.getActiveCount(), executor.getMaxPoolSize());
            
            // 记录告警指标
            meterRegistry.counter("threadpool.alert.high_usage", "pool", poolName).increment();
        }

        // 队列使用率过高告警
        if (queueUsageRatio > 0.8) {
            log.warn("线程池队列使用率过高告警: {} 队列使用率={:.2f}%, 队列大小={}, 队列容量={}",
                poolName, queueUsageRatio * 100, executor.getQueueSize(), executor.getQueueCapacity());
            
            // 记录告警指标
            meterRegistry.counter("threadpool.alert.high_queue_usage", "pool", poolName).increment();
        }

        // 线程池饱和告警（活跃线程数等于最大线程数且队列已满）
        if (executor.getActiveCount() >= executor.getMaxPoolSize() && 
            executor.getQueueSize() >= executor.getQueueCapacity()) {
            log.error("线程池饱和告警: {} 已达到最大容量，可能出现任务拒绝", poolName);
            
            // 记录告警指标
            meterRegistry.counter("threadpool.alert.saturated", "pool", poolName).increment();
        }
    }

    /**
     * 获取线程池状态信息
     */
    public Map<String, ThreadPoolStatus> getThreadPoolStatus() {
        return threadPools.entrySet().stream()
            .collect(java.util.stream.Collectors.toMap(
                Map.Entry::getKey,
                entry -> {
                    ThreadPoolTaskExecutor executor = entry.getValue();
                    return new ThreadPoolStatus(
                        executor.getActiveCount(),
                        executor.getQueueSize(),
                        executor.getPoolSize(),
                        executor.getMaxPoolSize(),
                        executor.getCorePoolSize(),
                        executor.getQueueCapacity()
                    );
                }
            ));
    }

    /**
     * 线程池状态信息
     */
    public static class ThreadPoolStatus {
        private final int activeCount;
        private final int queueSize;
        private final int poolSize;
        private final int maxPoolSize;
        private final int corePoolSize;
        private final int queueCapacity;

        public ThreadPoolStatus(int activeCount, int queueSize, int poolSize, 
                               int maxPoolSize, int corePoolSize, int queueCapacity) {
            this.activeCount = activeCount;
            this.queueSize = queueSize;
            this.poolSize = poolSize;
            this.maxPoolSize = maxPoolSize;
            this.corePoolSize = corePoolSize;
            this.queueCapacity = queueCapacity;
        }

        // Getters
        public int getActiveCount() { return activeCount; }
        public int getQueueSize() { return queueSize; }
        public int getPoolSize() { return poolSize; }
        public int getMaxPoolSize() { return maxPoolSize; }
        public int getCorePoolSize() { return corePoolSize; }
        public int getQueueCapacity() { return queueCapacity; }
        public double getPoolUsageRatio() { 
            return maxPoolSize > 0 ? (double) activeCount / maxPoolSize : 0.0; 
        }
        public double getQueueUsageRatio() { 
            return queueCapacity > 0 ? (double) queueSize / queueCapacity : 0.0; 
        }
    }
}
