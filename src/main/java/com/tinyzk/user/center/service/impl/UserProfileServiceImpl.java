package com.tinyzk.user.center.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tinyzk.user.center.common.annotation.UserAudit;
import com.tinyzk.user.center.common.enums.OperationType;
import com.tinyzk.user.center.common.exception.ErrorCode;
import com.tinyzk.user.center.common.exception.BusinessException;
import com.tinyzk.user.center.dto.CreateProfileDTO;
import com.tinyzk.user.center.dto.UpdateProfileDTO;
import com.tinyzk.user.center.entity.UserProfile;
import com.tinyzk.user.center.mapper.UserProfileMapper;
import com.tinyzk.user.center.service.UserProfileService;
import com.tinyzk.user.center.vo.UserProfileVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
/**
 * 用户个人资料服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserProfileServiceImpl extends ServiceImpl<UserProfileMapper, UserProfile> implements UserProfileService {

    private final UserProfileMapper userProfileMapper;

    @Override
    public UserProfileVO getUserProfile(Long userId) {
        log.info("查询用户个人资料: userId={}", userId);
        UserProfile userProfile = userProfileMapper.selectByUserId(userId);
        if (userProfile == null) {
            log.warn("用户个人资料不存在: userId={}", userId);
            // 可以选择抛出异常或返回null/空对象，这里返回null
            return null;
        }

        UserProfileVO userProfileVO = new UserProfileVO();
        BeanUtils.copyProperties(userProfile, userProfileVO);
        log.info("查询用户个人资料成功: userId={}", userId);
        return userProfileVO;
    }

    @Override
    @UserAudit(type = OperationType.PROFILE_CREATE,failType = OperationType.PROFILE_CREATE_FAIL, detail = "创建用户个人资料")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public UserProfileVO createUserProfile(Long userId, CreateProfileDTO createProfileDTO) {
        log.info("创建用户个人资料请求: userId={}, data={}", userId, createProfileDTO);
        
        // 检查用户资料是否已存在
        UserProfile existingProfile = userProfileMapper.selectByUserId(userId);
        if (existingProfile != null) {
            log.warn("用户资料已存在，无法创建: userId={}", userId);
            throw new BusinessException(ErrorCode.BUSINESS_ERROR, "用户资料已存在");
        }
        
        // 创建新的用户资料实体
        UserProfile userProfile = new UserProfile();
        BeanUtils.copyProperties(createProfileDTO, userProfile);
        userProfile.setUserId(userId);
        
        // 插入数据库
        int insertedRows = userProfileMapper.insert(userProfile);
        if (insertedRows > 0) {
            log.info("用户个人资料创建成功: userId={}", userId);
            // 查询刚创建的资料并返回
            UserProfile createdProfile = userProfileMapper.selectById(userProfile.getProfileId());
            UserProfileVO userProfileVO = new UserProfileVO();
            BeanUtils.copyProperties(createdProfile, userProfileVO);
            return userProfileVO;
        } else {
            log.error("用户个人资料创建失败: userId={}", userId);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "创建用户资料失败");
        }
    }

    @Override
    @UserAudit(type = OperationType.PROFILE_UPDATE,failType = OperationType.PROFILE_UPDATE_FAIL, detail = "更新用户个人资料")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public UserProfileVO updateUserProfile(Long userId, UpdateProfileDTO updateProfileDTO) {
        log.info("更新用户个人资料请求: userId={}, data={}", userId, updateProfileDTO);
        UserProfile userProfile = userProfileMapper.selectByUserId(userId);
        if (userProfile == null) {
            log.warn("尝试更新不存在的用户资料: userId={}", userId);
            throw new BusinessException(ErrorCode.USER_PROFILE_NOT_FOUND);
        }

        // 使用 BeanUtils 复制属性，null 值不会被复制
        BeanUtils.copyProperties(updateProfileDTO, userProfile);
        // 确保 userId 不会被覆盖（虽然 DTO 中没有，但以防万一）
        userProfile.setUserId(userId);

        int updatedRows = userProfileMapper.updateById(userProfile);
        if (updatedRows > 0) {
            log.info("用户个人资料更新成功: userId={}", userId);
            UserProfileVO userProfileVO = new UserProfileVO();
            BeanUtils.copyProperties(userProfile, userProfileVO);
            return userProfileVO;
        } else {
            log.error("用户个人资料更新失败: userId={}", userId);
            // 可以抛出异常或返回 null/错误信息
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "更新用户资料失败");
        }
    }
    
    @Override
    @UserAudit(type = OperationType.PROFILE_DELETE,failType = OperationType.PROFILE_DELETE_FAIL, detail = "删除用户个人资料")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public void deleteUserProfile(Long userId) {
        log.info("删除用户个人资料请求: userId={}", userId);
        
        // 检查用户资料是否存在
        UserProfile existingProfile = userProfileMapper.selectByUserId(userId);
        if (existingProfile == null) {
            log.warn("要删除的用户资料不存在: userId={}", userId);
            throw new BusinessException(ErrorCode.USER_PROFILE_NOT_FOUND);
        }
        
        // 执行逻辑删除（MyBatis-Plus会自动处理@TableLogic注解的字段）
        int deletedRows = userProfileMapper.deleteById(existingProfile.getProfileId());
        if (deletedRows > 0) {
            log.info("用户个人资料删除成功: userId={}", userId);
        } else {
            log.error("用户个人资料删除失败: userId={}", userId);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "删除用户资料失败");
        }
    }
}