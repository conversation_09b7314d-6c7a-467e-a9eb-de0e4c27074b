package com.tinyzk.user.center.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.*;
import com.tinyzk.user.center.config.OSSConfig;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * OSS文件存储服务
 */
@Service
@Slf4j
public class OSSFileStorageService {

    private final OSS ossClient;
    private final OSSConfig ossConfig;
    private final ThreadPoolTaskExecutor ossUploadExecutor;
    private final RedisTemplate<String, Object> redisTemplate;
    private final MeterRegistry meterRegistry;

    // Redis缓存键前缀
    private static final String OSS_URL_CACHE_PREFIX = "oss:url:";
    private static final String FILE_MD5_CACHE_PREFIX = "oss:md5:";

    public OSSFileStorageService(OSS ossClient,
                               OSSConfig ossConfig,
                               @Qualifier("ossUploadExecutor") ThreadPoolTaskExecutor executor,
                               RedisTemplate<String, Object> redisTemplate,
                               MeterRegistry meterRegistry) {
        this.ossClient = ossClient;
        this.ossConfig = ossConfig;
        this.ossUploadExecutor = executor;
        this.redisTemplate = redisTemplate;
        this.meterRegistry = meterRegistry;
    }

    /**
     * 智能文件上传
     */
    public CompletableFuture<String> uploadFileAsync(String fileName, byte[] content) {
        return CompletableFuture.supplyAsync(() -> {
            Timer.Sample sample = Timer.start(meterRegistry);

            try {
                // 计算文件MD5，实现去重
                String md5 = DigestUtils.md5DigestAsHex(content);
                String objectKey = generateObjectKey(fileName, md5);

                // 先检查Redis缓存中是否已有该文件
                String cachedUrl = getCachedFileUrl(md5);
                if (cachedUrl != null) {
                    log.debug("文件URL已缓存: {}", objectKey);
                    meterRegistry.counter("oss.upload.cache_hit").increment();
                    return cachedUrl;
                }

                // 检查文件是否已存在OSS中
                if (ossClient.doesObjectExist(ossConfig.getBucketName(), objectKey)) {
                    log.debug("文件已存在OSS中: {}", objectKey);
                    String fileUrl = generateFileUrl(objectKey);
                    cacheFileUrl(md5, fileUrl); // 缓存URL
                    meterRegistry.counter("oss.upload.duplicate").increment();
                    return fileUrl;
                }

                // 根据文件大小选择上传策略
                String fileUrl;
                if (content.length > 100 * 1024 * 1024) { // 100MB以上
                    fileUrl = multipartUploadWithRetry(objectKey, content);
                } else if (content.length > 5 * 1024 * 1024) { // 5MB以上
                    fileUrl = multipartUpload(objectKey, content);
                } else {
                    fileUrl = simpleUploadWithRetry(objectKey, content);
                }

                // 缓存上传成功的文件URL
                cacheFileUrl(md5, fileUrl);
                meterRegistry.counter("oss.upload.success").increment();
                return fileUrl;

            } catch (Exception e) {
                log.error("OSS文件上传失败: fileName={}", fileName, e);
                meterRegistry.counter("oss.upload.failure").increment();
                throw new RuntimeException("文件上传失败", e);
            } finally {
                sample.stop(Timer.builder("oss.upload.duration")
                    .tag("file_size_category", getFileSizeCategory(content.length))
                    .register(meterRegistry));
            }
        }, ossUploadExecutor);
    }

    /**
     * 简单上传（小文件）
     */
    private String simpleUploadWithRetry(String objectKey, byte[] content) {
        int maxRetries = 3;
        Exception lastException = null;

        for (int i = 0; i < maxRetries; i++) {
            try {
                ObjectMetadata metadata = new ObjectMetadata();
                metadata.setContentLength(content.length);
                metadata.setContentType(getContentType(objectKey));
                metadata.setServerSideEncryption("AES256"); // 服务端加密

                // 设置缓存控制
                metadata.setCacheControl("max-age=31536000"); // 1年缓存

                PutObjectRequest request = new PutObjectRequest(
                    ossConfig.getBucketName(),
                    objectKey,
                    new ByteArrayInputStream(content),
                    metadata
                );

                ossClient.putObject(request);
                return generateFileUrl(objectKey);

            } catch (Exception e) {
                lastException = e;
                if (i < maxRetries - 1) {
                    try {
                        Thread.sleep((i + 1) * 1000); // 指数退避
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        throw new RuntimeException("OSS上传重试失败", lastException);
    }

    /**
     * 分片上传（大文件）
     */
    private String multipartUpload(String objectKey, byte[] content) {
        String uploadId = null;
        try {
            // 初始化分片上传
            InitiateMultipartUploadRequest initRequest = new InitiateMultipartUploadRequest(
                ossConfig.getBucketName(), objectKey);

            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(getContentType(objectKey));
            metadata.setServerSideEncryption("AES256");
            initRequest.setObjectMetadata(metadata);

            InitiateMultipartUploadResult initResult = ossClient.initiateMultipartUpload(initRequest);
            uploadId = initResult.getUploadId();

            // 分片上传
            List<PartETag> partETags = uploadParts(objectKey, uploadId, content);

            // 完成分片上传
            CompleteMultipartUploadRequest completeRequest = new CompleteMultipartUploadRequest(
                ossConfig.getBucketName(), objectKey, uploadId, partETags);

            ossClient.completeMultipartUpload(completeRequest);
            return generateFileUrl(objectKey);

        } catch (Exception e) {
            // 上传失败，取消分片上传
            if (uploadId != null) {
                try {
                    AbortMultipartUploadRequest abortRequest = new AbortMultipartUploadRequest(
                        ossConfig.getBucketName(), objectKey, uploadId);
                    ossClient.abortMultipartUpload(abortRequest);
                } catch (Exception abortException) {
                    log.warn("取消分片上传失败: {}", objectKey, abortException);
                }
            }
            throw new RuntimeException("分片上传失败", e);
        }
    }

    /**
     * 上传分片
     */
    private List<PartETag> uploadParts(String objectKey, String uploadId, byte[] content) {
        List<PartETag> partETags = new java.util.ArrayList<>();
        int partSize = 5 * 1024 * 1024; // 5MB分片
        int partCount = (content.length + partSize - 1) / partSize;

        for (int i = 0; i < partCount; i++) {
            int startPos = i * partSize;
            int endPos = Math.min(startPos + partSize, content.length);
            byte[] partData = Arrays.copyOfRange(content, startPos, endPos);

            UploadPartRequest uploadPartRequest = new UploadPartRequest();
            uploadPartRequest.setBucketName(ossConfig.getBucketName());
            uploadPartRequest.setKey(objectKey);
            uploadPartRequest.setUploadId(uploadId);
            uploadPartRequest.setPartNumber(i + 1);
            uploadPartRequest.setInputStream(new ByteArrayInputStream(partData));
            uploadPartRequest.setPartSize(partData.length);

            UploadPartResult uploadPartResult = ossClient.uploadPart(uploadPartRequest);
            partETags.add(uploadPartResult.getPartETag());
        }

        return partETags;
    }

    /**
     * 下载文件
     */
    public byte[] downloadFile(String objectKey) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            OSSObject ossObject = ossClient.getObject(ossConfig.getBucketName(), objectKey);

            try (InputStream inputStream = ossObject.getObjectContent()) {
                byte[] content = inputStream.readAllBytes();
                meterRegistry.counter("oss.download.success").increment();
                return content;
            }

        } catch (Exception e) {
            log.error("OSS文件下载失败: objectKey={}", objectKey, e);
            meterRegistry.counter("oss.download.failure").increment();
            throw new RuntimeException("文件下载失败", e);
        } finally {
            sample.stop(Timer.builder("oss.download.duration")
                .register(meterRegistry));
        }
    }

    /**
     * 生成对象键（按日期分目录）
     */
    private String generateObjectKey(String fileName, String md5) {
        LocalDate today = LocalDate.now();
        String dateDir = today.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));

        String extension = getFileExtension(fileName);
        String newFileName = md5 + (extension.isEmpty() ? "" : "." + extension);

        return "resume-files/" + dateDir + "/" + newFileName;
    }

    /**
     * 生成文件访问URL
     */
    private String generateFileUrl(String objectKey) {
        // 生成带签名的URL，有效期24小时
        Date expiration = new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000);
        return ossClient.generatePresignedUrl(ossConfig.getBucketName(), objectKey, expiration).toString();
    }

    /**
     * 从Redis缓存获取文件URL
     */
    private String getCachedFileUrl(String md5) {
        try {
            return (String) redisTemplate.opsForValue().get(FILE_MD5_CACHE_PREFIX + md5);
        } catch (Exception e) {
            log.warn("获取缓存文件URL失败: md5={}", md5, e);
            return null;
        }
    }

    /**
     * 缓存文件URL到Redis
     */
    private void cacheFileUrl(String md5, String fileUrl) {
        try {
            // 缓存2小时
            redisTemplate.opsForValue().set(FILE_MD5_CACHE_PREFIX + md5, fileUrl, Duration.ofHours(2));
        } catch (Exception e) {
            log.warn("缓存文件URL失败: md5={}", md5, e);
        }
    }

    /**
     * 获取文件MIME类型
     */
    private String getContentType(String objectKey) {
        String extension = getFileExtension(objectKey).toLowerCase();
        switch (extension) {
            case "pdf": return "application/pdf";
            case "doc": return "application/msword";
            case "docx": return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "txt": return "text/plain";
            case "jpg":
            case "jpeg": return "image/jpeg";
            case "png": return "image/png";
            default: return "application/octet-stream";
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1) : "";
    }

    /**
     * 获取文件大小分类
     */
    private String getFileSizeCategory(long fileSize) {
        if (fileSize < 1024 * 1024) return "small"; // < 1MB
        else if (fileSize < 10 * 1024 * 1024) return "medium"; // < 10MB
        else return "large"; // >= 10MB
    }

    /**
     * 带重试的分片上传
     */
    private String multipartUploadWithRetry(String objectKey, byte[] content) {
        int maxRetries = 2;
        Exception lastException = null;

        for (int i = 0; i < maxRetries; i++) {
            try {
                return multipartUpload(objectKey, content);
            } catch (Exception e) {
                lastException = e;
                if (i < maxRetries - 1) {
                    log.warn("分片上传失败，第{}次重试: {}", i + 1, objectKey);
                    try {
                        Thread.sleep(2000 * (i + 1)); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        throw new RuntimeException("分片上传重试失败", lastException);
    }

    /**
     * 定期清理过期的临时文件
     */
    @Scheduled(cron = "0 0 3 * * ?") // 每天凌晨3点执行
    public void cleanupExpiredFiles() {
        try {
            // 清理7天前的临时文件
            String prefix = "temp-files/";
            LocalDate cutoffDate = LocalDate.now().minusDays(7);
            String cutoffPrefix = prefix + cutoffDate.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));

            // 列出并删除过期文件
            ListObjectsRequest listRequest = new ListObjectsRequest(ossConfig.getBucketName())
                .withPrefix(cutoffPrefix)
                .withMaxKeys(1000);

            ObjectListing objectListing = ossClient.listObjects(listRequest);

            if (!objectListing.getObjectSummaries().isEmpty()) {
                List<String> keysToDelete = objectListing.getObjectSummaries().stream()
                    .map(OSSObjectSummary::getKey)
                    .collect(Collectors.toList());

                DeleteObjectsRequest deleteRequest = new DeleteObjectsRequest(ossConfig.getBucketName())
                    .withKeys(keysToDelete);

                ossClient.deleteObjects(deleteRequest);
                log.info("清理OSS过期文件完成，删除文件数: {}", keysToDelete.size());
            }

        } catch (Exception e) {
            log.error("OSS文件清理异常", e);
        }
    }

    /**
     * 断点续传上传
     */
    public CompletableFuture<String> resumableUpload(String fileName, byte[] content, String uploadId) {
        return CompletableFuture.supplyAsync(() -> {
            Timer.Sample sample = Timer.start(meterRegistry);

            try {
                String objectKey = generateObjectKey(fileName, DigestUtils.md5DigestAsHex(content));

                if (uploadId == null) {
                    // 初始化断点续传
                    return initializeResumableUpload(objectKey, content);
                } else {
                    // 继续断点续传
                    return continueResumableUpload(objectKey, content, uploadId);
                }

            } catch (Exception e) {
                log.error("断点续传失败: fileName={}", fileName, e);
                meterRegistry.counter("oss.resumable_upload.failure").increment();
                throw new RuntimeException("断点续传失败", e);
            } finally {
                sample.stop(Timer.builder("oss.resumable_upload.duration")
                    .register(meterRegistry));
            }
        }, ossUploadExecutor);
    }

    /**
     * 初始化断点续传
     */
    private String initializeResumableUpload(String objectKey, byte[] content) {
        try {
            InitiateMultipartUploadRequest initRequest = new InitiateMultipartUploadRequest(
                ossConfig.getBucketName(), objectKey);

            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(getContentType(objectKey));
            metadata.setServerSideEncryption("AES256");
            metadata.setContentLength(content.length);
            initRequest.setObjectMetadata(metadata);

            InitiateMultipartUploadResult initResult = ossClient.initiateMultipartUpload(initRequest);
            String uploadId = initResult.getUploadId();

            // 缓存上传信息到Redis
            cacheUploadInfo(objectKey, uploadId, content.length);

            log.info("断点续传初始化成功: objectKey={}, uploadId={}", objectKey, uploadId);
            meterRegistry.counter("oss.resumable_upload.initialized").increment();

            return uploadId;

        } catch (Exception e) {
            log.error("初始化断点续传失败: objectKey={}", objectKey, e);
            throw new RuntimeException("初始化断点续传失败", e);
        }
    }

    /**
     * 继续断点续传
     */
    private String continueResumableUpload(String objectKey, byte[] content, String uploadId) {
        try {
            // 获取已上传的分片信息
            List<PartETag> uploadedParts = getUploadedParts(objectKey, uploadId);

            // 计算需要上传的分片
            List<PartETag> allParts = uploadRemainingParts(objectKey, uploadId, content, uploadedParts);

            // 完成分片上传
            CompleteMultipartUploadRequest completeRequest = new CompleteMultipartUploadRequest(
                ossConfig.getBucketName(), objectKey, uploadId, allParts);

            ossClient.completeMultipartUpload(completeRequest);

            // 清理缓存信息
            clearUploadInfo(objectKey);

            String fileUrl = generateFileUrl(objectKey);
            log.info("断点续传完成: objectKey={}, uploadId={}", objectKey, uploadId);
            meterRegistry.counter("oss.resumable_upload.completed").increment();

            return fileUrl;

        } catch (Exception e) {
            log.error("继续断点续传失败: objectKey={}, uploadId={}", objectKey, uploadId, e);
            throw new RuntimeException("继续断点续传失败", e);
        }
    }

    /**
     * 获取已上传的分片信息
     */
    private List<PartETag> getUploadedParts(String objectKey, String uploadId) {
        try {
            ListPartsRequest listPartsRequest = new ListPartsRequest(
                ossConfig.getBucketName(), objectKey, uploadId);

            PartListing partListing = ossClient.listParts(listPartsRequest);
            return partListing.getParts().stream()
                .map(part -> new PartETag(part.getPartNumber(), part.getETag()))
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.warn("获取已上传分片信息失败: objectKey={}, uploadId={}", objectKey, uploadId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 上传剩余分片
     */
    private List<PartETag> uploadRemainingParts(String objectKey, String uploadId,
                                              byte[] content, List<PartETag> uploadedParts) {
        List<PartETag> allParts = new ArrayList<>(uploadedParts);
        int partSize = 5 * 1024 * 1024; // 5MB分片
        int totalParts = (content.length + partSize - 1) / partSize;

        // 找出已上传的分片号
        Set<Integer> uploadedPartNumbers = uploadedParts.stream()
            .map(PartETag::getPartNumber)
            .collect(Collectors.toSet());

        // 上传缺失的分片
        for (int partNumber = 1; partNumber <= totalParts; partNumber++) {
            if (!uploadedPartNumbers.contains(partNumber)) {
                int startPos = (partNumber - 1) * partSize;
                int endPos = Math.min(startPos + partSize, content.length);
                byte[] partData = Arrays.copyOfRange(content, startPos, endPos);

                PartETag partETag = uploadPart(objectKey, uploadId, partNumber, partData);
                allParts.add(partETag);
            }
        }

        // 按分片号排序
        allParts.sort((p1, p2) -> Integer.compare(p1.getPartNumber(), p2.getPartNumber()));
        return allParts;
    }

    /**
     * 上传单个分片
     */
    private PartETag uploadPart(String objectKey, String uploadId, int partNumber, byte[] partData) {
        UploadPartRequest uploadPartRequest = new UploadPartRequest();
        uploadPartRequest.setBucketName(ossConfig.getBucketName());
        uploadPartRequest.setKey(objectKey);
        uploadPartRequest.setUploadId(uploadId);
        uploadPartRequest.setPartNumber(partNumber);
        uploadPartRequest.setInputStream(new ByteArrayInputStream(partData));
        uploadPartRequest.setPartSize(partData.length);

        UploadPartResult uploadPartResult = ossClient.uploadPart(uploadPartRequest);
        return uploadPartResult.getPartETag();
    }

    /**
     * 缓存上传信息到Redis
     */
    private void cacheUploadInfo(String objectKey, String uploadId, int contentLength) {
        try {
            Map<String, Object> uploadInfo = new HashMap<>();
            uploadInfo.put("uploadId", uploadId);
            uploadInfo.put("contentLength", contentLength);
            uploadInfo.put("timestamp", System.currentTimeMillis());

            String cacheKey = "oss:upload:" + objectKey;
            redisTemplate.opsForHash().putAll(cacheKey, uploadInfo);
            redisTemplate.expire(cacheKey, Duration.ofHours(24)); // 24小时过期

        } catch (Exception e) {
            log.warn("缓存上传信息失败: objectKey={}", objectKey, e);
        }
    }

    /**
     * 清理上传信息缓存
     */
    private void clearUploadInfo(String objectKey) {
        try {
            String cacheKey = "oss:upload:" + objectKey;
            redisTemplate.delete(cacheKey);
        } catch (Exception e) {
            log.warn("清理上传信息缓存失败: objectKey={}", objectKey, e);
        }
    }

    /**
     * 设置文件生命周期规则
     */
    public void setupLifecycleRules() {
        try {
            SetBucketLifecycleRequest request = new SetBucketLifecycleRequest(ossConfig.getBucketName());

            // 临时文件规则：7天后删除
            LifecycleRule tempFileRule = new LifecycleRule();
            tempFileRule.setId("temp-files-cleanup");
            tempFileRule.setPrefix("temp-files/");
            tempFileRule.setStatus(LifecycleRule.RuleStatus.Enabled);
            tempFileRule.setExpirationDays(7);

            // 简历文件规则：90天后转为低频存储，365天后转为归档存储
            LifecycleRule resumeFileRule = new LifecycleRule();
            resumeFileRule.setId("resume-files-lifecycle");
            resumeFileRule.setPrefix("resume-files/");
            resumeFileRule.setStatus(LifecycleRule.RuleStatus.Enabled);

            // 设置存储类型转换
            List<LifecycleRule.StorageTransition> transitions = new ArrayList<>();

            // 90天后转为低频存储
            LifecycleRule.StorageTransition iaTransition = new LifecycleRule.StorageTransition();
            iaTransition.setCreatedBeforeDate(new Date(System.currentTimeMillis() + 90L * 24 * 60 * 60 * 1000));
            iaTransition.setStorageClass(StorageClass.IA);
            transitions.add(iaTransition);

            // 365天后转为归档存储
            LifecycleRule.StorageTransition archiveTransition = new LifecycleRule.StorageTransition();
            archiveTransition.setCreatedBeforeDate(new Date(System.currentTimeMillis() + 365L * 24 * 60 * 60 * 1000));
            archiveTransition.setStorageClass(StorageClass.Archive);
            transitions.add(archiveTransition);

            // 注意：实际的OSS API可能不同，这里简化处理
            // resumeFileRule.setStorageTransitions(transitions);

            // 设置规则
            request.setLifecycleRules(Arrays.asList(tempFileRule, resumeFileRule));
            ossClient.setBucketLifecycle(request);

            log.info("OSS生命周期规则设置完成");
            meterRegistry.counter("oss.lifecycle.rules.setup").increment();

        } catch (Exception e) {
            log.error("设置OSS生命周期规则失败", e);
            meterRegistry.counter("oss.lifecycle.rules.setup.failed").increment();
        }
    }

    /**
     * 智能存储类型选择
     */
    public StorageClass selectOptimalStorageClass(String fileName, long fileSize, String businessType) {
        // 根据业务类型和文件大小选择最优存储类型
        if ("temp".equals(businessType)) {
            return StorageClass.Standard; // 临时文件使用标准存储
        }

        if (fileSize < 1024 * 1024) { // 小于1MB
            return StorageClass.Standard; // 小文件使用标准存储
        }

        if ("archive".equals(businessType)) {
            return StorageClass.Archive; // 归档业务直接使用归档存储
        }

        // 默认使用标准存储
        return StorageClass.Standard;
    }

    /**
     * 批量文件操作
     */
    public CompletableFuture<List<String>> batchUpload(List<FileUploadRequest> requests) {
        return CompletableFuture.supplyAsync(() -> {
            List<String> results = new ArrayList<>();

            for (FileUploadRequest request : requests) {
                try {
                    String fileUrl = uploadFileAsync(request.getFileName(), request.getContent()).get();
                    results.add(fileUrl);
                } catch (Exception e) {
                    log.error("批量上传文件失败: fileName={}", request.getFileName(), e);
                    results.add(null); // 失败的文件返回null
                }
            }

            return results;
        }, ossUploadExecutor);
    }

    /**
     * 文件上传请求
     */
    public static class FileUploadRequest {
        private String fileName;
        private byte[] content;
        private String businessType;

        public FileUploadRequest(String fileName, byte[] content, String businessType) {
            this.fileName = fileName;
            this.content = content;
            this.businessType = businessType;
        }

        // Getters
        public String getFileName() { return fileName; }
        public byte[] getContent() { return content; }
        public String getBusinessType() { return businessType; }
    }
}
