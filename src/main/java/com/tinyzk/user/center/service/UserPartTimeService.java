package com.tinyzk.user.center.service;

import com.tinyzk.user.center.dto.CreatePartTimeDTO;
import com.tinyzk.user.center.dto.UpdatePartTimeDTO;
import com.tinyzk.user.center.vo.UserPartTimeVO;

import java.util.List;

/**
 * 用户兼职经历服务接口
 */
public interface UserPartTimeService {

    /**
     * 获取用户兼职经历列表
     *
     * @param userId 用户ID
     * @return 兼职经历列表
     */
    List<UserPartTimeVO> getUserPartTimes(Long userId);

    /**
     * 获取用户特定兼职经历
     *
     * @param userId     用户ID
     * @param partTimeId 兼职经历ID
     * @return 兼职经历详情
     */
    UserPartTimeVO getUserPartTime(Long userId, Long partTimeId);

    /**
     * 创建用户兼职经历
     *
     * @param userId          用户ID
     * @param createPartTimeDTO 创建兼职经历信息
     * @return 创建后的兼职经历
     */
    UserPartTimeVO createUserPartTime(Long userId, CreatePartTimeDTO createPartTimeDTO);

    /**
     * 更新用户兼职经历
     *
     * @param userId          用户ID
     * @param partTimeId       兼职经历ID
     * @param updatePartTimeDTO 更新信息
     * @return 更新后的兼职经历
     */
    UserPartTimeVO updateUserPartTime(Long userId, Long partTimeId, UpdatePartTimeDTO updatePartTimeDTO);

    /**
     * 删除用户兼职经历
     *
     * @param userId     用户ID
     * @param partTimeId 兼职经历ID
     */
    void deleteUserPartTime(Long userId, Long partTimeId);
} 