package com.tinyzk.user.center.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tinyzk.user.center.dto.CreateEducationDTO;
import com.tinyzk.user.center.dto.UpdateEducationDTO;
import com.tinyzk.user.center.entity.UserEducationHistory;
import com.tinyzk.user.center.vo.UserEducationVO;

import java.util.List;

/**
 * 用户教育经历服务接口
 */
public interface UserEducationHistoryService extends IService<UserEducationHistory> {

    /**
     * 获取用户所有教育经历
     *
     * @param userId 用户ID
     * @return 教育经历列表
     */
    List<UserEducationVO> getUserEducations(Long userId);

    /**
     * 获取用户特定教育经历
     *
     * @param userId      用户ID
     * @param educationId 教育经历ID
     * @return 教育经历详情
     */
    UserEducationVO getUserEducation(Long userId, Long educationId);

    /**
     * 创建用户教育经历
     *
     * @param userId           用户ID
     * @param createEducationDTO 创建教育经历请求
     * @return 创建后的教育经历
     */
    UserEducationVO createUserEducation(Long userId, CreateEducationDTO createEducationDTO);

    /**
     * 更新用户教育经历
     *
     * @param userId           用户ID
     * @param educationId      教育经历ID
     * @param updateEducationDTO 更新教育经历请求
     * @return 更新后的教育经历
     */
    UserEducationVO updateUserEducation(Long userId, Long educationId, UpdateEducationDTO updateEducationDTO);

    /**
     * 删除用户教育经历
     *
     * @param userId      用户ID
     * @param educationId 教育经历ID
     */
    void deleteUserEducation(Long userId, Long educationId);
} 