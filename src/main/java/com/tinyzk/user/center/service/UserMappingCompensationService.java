package com.tinyzk.user.center.service;

/**
 * 用户映射补偿服务接口
 * 用于处理注册时用户外部映射关系创建失败的补偿逻辑
 */
public interface UserMappingCompensationService {
    
    /**
     * 为没有映射关系的用户创建补偿映射
     * 
     * @param userId 用户ID
     * @param clientId 客户端ID  
     * @param externalUserId 外部用户ID
     * @return 是否创建成功
     */
    boolean createCompensationMapping(Long userId, String clientId, String externalUserId);
    
    /**
     * 批量修复缺失的用户映射关系
     * 扫描没有映射关系的用户并尝试创建补偿映射
     * 
     * @param batchSize 批处理大小
     * @return 修复的用户数量
     */
    int batchFixMissingMappings(int batchSize);
    
    /**
     * 检查用户是否缺少映射关系
     * 
     * @param userId 用户ID
     * @return 是否缺少映射关系
     */
    boolean isMissingMapping(Long userId);
} 