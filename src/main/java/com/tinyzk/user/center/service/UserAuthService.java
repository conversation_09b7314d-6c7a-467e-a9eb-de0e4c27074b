package com.tinyzk.user.center.service;

import com.tinyzk.user.center.dto.ExternalLoginDTO;
import com.tinyzk.user.center.dto.LoginDTO;
import com.tinyzk.user.center.dto.RegisterDTO;
import com.tinyzk.user.center.vo.ExternalLoginVO;
import com.tinyzk.user.center.vo.LoginVO;
import com.tinyzk.user.center.vo.RegisterVO;
/**
 * 用户认证服务接口
 */
public interface UserAuthService {

    /**
     * 用户登录
     *
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    LoginVO login(LoginDTO loginDTO);

    /**
     * 用户注册
     *
     * @param registerDTO 注册信息
     * @return 登录结果（注册成功后自动登录）
     */
    RegisterVO register(RegisterDTO registerDTO);

    /**
     * 第三方业务系统登录
     * 根据第三方业务用户ID和客户端ID进行登录
     *
     * @param externalLoginDTO 第三方登录信息
     * @return 第三方登录结果
     */
    ExternalLoginVO externalLogin(ExternalLoginDTO externalLoginDTO);
}
