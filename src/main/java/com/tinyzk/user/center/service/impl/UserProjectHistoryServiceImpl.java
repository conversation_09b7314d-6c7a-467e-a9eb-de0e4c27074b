package com.tinyzk.user.center.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tinyzk.user.center.common.annotation.UserAudit;
import com.tinyzk.user.center.common.enums.OperationType;
import com.tinyzk.user.center.common.exception.BusinessException;
import com.tinyzk.user.center.common.exception.ErrorCode;
import com.tinyzk.user.center.dto.CreateProjectDTO;
import com.tinyzk.user.center.dto.UpdateProjectDTO;
import com.tinyzk.user.center.entity.UserProjectHistory;
import com.tinyzk.user.center.mapper.UserProjectHistoryMapper;
import com.tinyzk.user.center.service.UserProjectHistoryService;
import com.tinyzk.user.center.vo.UserProjectVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户项目经历服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserProjectHistoryServiceImpl extends ServiceImpl<UserProjectHistoryMapper, UserProjectHistory> implements UserProjectHistoryService {

    private final UserProjectHistoryMapper userProjectHistoryMapper;

    @Override
    public List<UserProjectVO> getUserProjects(Long userId) {
        log.info("获取用户项目经历列表: userId={}", userId);
        LambdaQueryWrapper<UserProjectHistory> queryWrapper = new LambdaQueryWrapper<UserProjectHistory>()
                .eq(UserProjectHistory::getUserId, userId)
                .orderByDesc(UserProjectHistory::getCreatedAt);
                
        List<UserProjectHistory> projectHistories = userProjectHistoryMapper.selectList(queryWrapper);
        
        List<UserProjectVO> projectVOs = projectHistories.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
                
        log.info("获取用户项目经历列表成功: userId={}, count={}", userId, projectVOs.size());
        return projectVOs;
    }

    @Override
    public UserProjectVO getUserProject(Long userId, Long projectId) {
        log.info("获取用户项目经历: userId={}, projectId={}", userId, projectId);
        UserProjectHistory projectHistory = getUserProjectOrThrow(userId, projectId);
        UserProjectVO projectVO = convertToVO(projectHistory);
        log.info("获取用户项目经历成功: userId={}, projectId={}", userId, projectId);
        return projectVO;
    }

    @Override
    @UserAudit(type = OperationType.PROJECT_CREATE,failType = OperationType.PROJECT_CREATE_FAIL, detail = "创建用户项目经历")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public UserProjectVO createUserProject(Long userId, CreateProjectDTO createProjectDTO) {
        log.info("创建用户项目经历: userId={}", userId);
        
        UserProjectHistory projectHistory = new UserProjectHistory();
        BeanUtils.copyProperties(createProjectDTO, projectHistory);
        projectHistory.setUserId(userId);
        
        userProjectHistoryMapper.insert(projectHistory);
        
        UserProjectVO projectVO = convertToVO(projectHistory);
        log.info("创建用户项目经历成功: userId={}, projectId={}", userId, projectVO.getProjectId());
        return projectVO;
    }

    @Override
    @UserAudit(type = OperationType.PROJECT_UPDATE,failType = OperationType.PROJECT_UPDATE_FAIL, detail = "更新用户项目经历")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public UserProjectVO updateUserProject(Long userId, Long projectId, UpdateProjectDTO updateProjectDTO) {
        log.info("更新用户项目经历: userId={}, projectId={}", userId, projectId);
        
        UserProjectHistory projectHistory = getUserProjectOrThrow(userId, projectId);
        
        // 只更新非null字段
        if (updateProjectDTO.getProjectName() != null) {
            projectHistory.setProjectName(updateProjectDTO.getProjectName());
        }
        if (updateProjectDTO.getRole() != null) {
            projectHistory.setRole(updateProjectDTO.getRole());
        }
        if (updateProjectDTO.getStartDate() != null) {
            projectHistory.setStartDate(updateProjectDTO.getStartDate());
        }
        if (updateProjectDTO.getEndDate() != null) {
            projectHistory.setEndDate(updateProjectDTO.getEndDate());
        }
        if (updateProjectDTO.getDescription() != null) {
            projectHistory.setDescription(updateProjectDTO.getDescription());
        }
        if (updateProjectDTO.getProjectUrl() != null) {
            projectHistory.setProjectUrl(updateProjectDTO.getProjectUrl());
        }
        if (updateProjectDTO.getAssociatedOrganization() != null) {
            projectHistory.setAssociatedOrganization(updateProjectDTO.getAssociatedOrganization());
        }
        if (updateProjectDTO.getVisibility() != null) {
            projectHistory.setVisibility(updateProjectDTO.getVisibility());
        }
        
        userProjectHistoryMapper.updateById(projectHistory);
        
        UserProjectVO projectVO = convertToVO(projectHistory);
        log.info("更新用户项目经历成功: userId={}, projectId={}", userId, projectId);
        return projectVO;
    }

    @Override
    @UserAudit(type = OperationType.PROJECT_DELETE,failType = OperationType.PROJECT_DELETE_FAIL, detail = "删除用户项目经历")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public void deleteUserProject(Long userId, Long projectId) {
        log.info("删除用户项目经历: userId={}, projectId={}", userId, projectId);
        
        UserProjectHistory projectHistory = getUserProjectOrThrow(userId, projectId);
        
        userProjectHistoryMapper.deleteById(projectId);
        
        log.info("删除用户项目经历成功: userId={}, projectId={}", userId, projectId);
    }
    
    /**
     * 获取用户项目经历，如果不存在则抛出异常
     */
    private UserProjectHistory getUserProjectOrThrow(Long userId, Long projectId) {
        LambdaQueryWrapper<UserProjectHistory> queryWrapper = new LambdaQueryWrapper<UserProjectHistory>()
                .eq(UserProjectHistory::getUserId, userId)
                .eq(UserProjectHistory::getProjectId, projectId);
                
        UserProjectHistory projectHistory = userProjectHistoryMapper.selectOne(queryWrapper);
        
        if (projectHistory == null) {
            log.warn("用户项目经历不存在: userId={}, projectId={}", userId, projectId);
            throw new BusinessException(ErrorCode.NOT_FOUND, "项目经历不存在");
        }
        
        return projectHistory;
    }
    
    /**
     * 将实体转换为VO对象
     */
    private UserProjectVO convertToVO(UserProjectHistory projectHistory) {
        UserProjectVO projectVO = new UserProjectVO();
        BeanUtils.copyProperties(projectHistory, projectVO);
        return projectVO;
    }
} 