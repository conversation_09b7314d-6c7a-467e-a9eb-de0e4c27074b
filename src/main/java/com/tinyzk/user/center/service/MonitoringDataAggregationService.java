package com.tinyzk.user.center.service;

import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 监控数据聚合服务
 * 统一收集和展示系统各组件的监控数据
 */
@Service
@Slf4j
public class MonitoringDataAggregationService {

    private final ThreadPoolMonitorService threadPoolMonitorService;
    private final BackpressureControlService backpressureService;
    private final DeadLetterQueueService deadLetterService;
    private final MessageRetryService retryService;
    private final BatchDatabaseService batchDatabaseService;
    private final MeterRegistry meterRegistry;

    public MonitoringDataAggregationService(ThreadPoolMonitorService threadPoolMonitorService,
                                          BackpressureControlService backpressureService,
                                          DeadLetterQueueService deadLetterService,
                                          MessageRetryService retryService,
                                          BatchDatabaseService batchDatabaseService,
                                          MeterRegistry meterRegistry) {
        this.threadPoolMonitorService = threadPoolMonitorService;
        this.backpressureService = backpressureService;
        this.deadLetterService = deadLetterService;
        this.retryService = retryService;
        this.batchDatabaseService = batchDatabaseService;
        this.meterRegistry = meterRegistry;
    }

    /**
     * 获取系统全局监控数据
     */
    public SystemMonitoringData getSystemMonitoringData() {
        SystemMonitoringData data = new SystemMonitoringData();
        data.setTimestamp(LocalDateTime.now());

        try {
            // 线程池状态
            data.setThreadPoolStatus(threadPoolMonitorService.getThreadPoolStatus());

            // 背压控制状态
            data.setBackpressureStatus(backpressureService.getStatistics());

            // 死信队列统计
            data.setDeadLetterStatistics(deadLetterService.getDeadLetterStatistics());

            // 重试统计
            data.setRetryStatistics(retryService.getRetryStatistics());

            // 数据库连接池状态
            data.setDatabaseStatus(batchDatabaseService.getConnectionPoolStatus());

            // 业务指标
            data.setBusinessMetrics(collectBusinessMetrics());

            // 系统健康状态
            data.setSystemHealth(calculateSystemHealth(data));

        } catch (Exception e) {
            log.error("获取系统监控数据失败", e);
        }

        return data;
    }

    /**
     * 获取数据流转状态
     */
    public DataFlowStatus getDataFlowStatus() {
        DataFlowStatus status = new DataFlowStatus();
        status.setTimestamp(LocalDateTime.now());

        try {
            // 消息队列流转状态
            status.setMessageQueueStatus(getMessageQueueStatus());

            // OSS存储流转状态
            status.setOssStorageStatus(getOssStorageStatus());

            // 数据库操作状态
            status.setDatabaseOperationStatus(getDatabaseOperationStatus());

            // 第三方API调用状态
            status.setThirdPartyApiStatus(getThirdPartyApiStatus());

            // 数据转换状态
            status.setDataConversionStatus(getDataConversionStatus());

        } catch (Exception e) {
            log.error("获取数据流转状态失败", e);
        }

        return status;
    }

    /**
     * 收集业务指标
     */
    private BusinessMetrics collectBusinessMetrics() {
        BusinessMetrics metrics = new BusinessMetrics();

        // 从Prometheus指标中收集业务数据
        metrics.setTotalMessagesProcessed(getCounterValue("mq.message.consume.success"));
        metrics.setTotalMessagesFailed(getCounterValue("mq.message.consume.failure"));
        metrics.setTotalFilesUploaded(getCounterValue("oss.upload.success"));
        metrics.setTotalDatabaseOperations(getCounterValue("database.batch.insert.success"));
        metrics.setTotalResumesParsed(getCounterValue("resume.parse.direct.success"));
        metrics.setTotalDataConversions(getCounterValue("resume.data.conversion.success"));

        // 计算成功率
        double totalMessages = metrics.getTotalMessagesProcessed() + metrics.getTotalMessagesFailed();
        metrics.setMessageSuccessRate(totalMessages > 0 ? metrics.getTotalMessagesProcessed() / totalMessages : 0.0);

        return metrics;
    }

    /**
     * 计算系统健康状态
     */
    private SystemHealth calculateSystemHealth(SystemMonitoringData data) {
        SystemHealth health = new SystemHealth();

        // 线程池健康状态
        boolean threadPoolHealthy = data.getThreadPoolStatus().values().stream()
            .allMatch(status -> status.getPoolUsageRatio() < 0.9);

        // 背压控制健康状态
        boolean backpressureHealthy = !data.getBackpressureStatus().isBackpressureEnabled();

        // 数据库连接池健康状态
        boolean databaseHealthy = data.getDatabaseStatus().getConnectionUsageRatio() < 0.8;

        // 死信队列健康状态
        boolean deadLetterHealthy = data.getDeadLetterStatistics().getPendingCount() < 100;

        // 综合健康状态
        if (threadPoolHealthy && backpressureHealthy && databaseHealthy && deadLetterHealthy) {
            health.setOverallStatus("HEALTHY");
            health.setHealthScore(100);
        } else if (threadPoolHealthy && databaseHealthy) {
            health.setOverallStatus("WARNING");
            health.setHealthScore(75);
        } else {
            health.setOverallStatus("CRITICAL");
            health.setHealthScore(50);
        }

        health.setThreadPoolHealthy(threadPoolHealthy);
        health.setBackpressureHealthy(backpressureHealthy);
        health.setDatabaseHealthy(databaseHealthy);
        health.setDeadLetterHealthy(deadLetterHealthy);

        return health;
    }

    /**
     * 获取消息队列状态
     */
    private MessageQueueFlowStatus getMessageQueueStatus() {
        MessageQueueFlowStatus status = new MessageQueueFlowStatus();
        status.setProducedMessages(getCounterValue("mq.message.send.success"));
        status.setConsumedMessages(getCounterValue("mq.message.consume.success"));
        status.setFailedMessages(getCounterValue("mq.message.consume.failure"));
        status.setDeadLetterMessages(getCounterValue("dead_letter.message.sent"));
        return status;
    }

    /**
     * 获取OSS存储状态
     */
    private OssStorageFlowStatus getOssStorageStatus() {
        OssStorageFlowStatus status = new OssStorageFlowStatus();
        status.setUploadedFiles(getCounterValue("oss.upload.success"));
        status.setFailedUploads(getCounterValue("oss.upload.failure"));
        status.setDownloadedFiles(getCounterValue("oss.download.success"));
        status.setCacheHits(getCounterValue("oss.upload.cache_hit"));
        return status;
    }

    /**
     * 获取数据库操作状态
     */
    private DatabaseOperationFlowStatus getDatabaseOperationStatus() {
        DatabaseOperationFlowStatus status = new DatabaseOperationFlowStatus();
        status.setBatchInserts(getCounterValue("database.batch.insert.success"));
        status.setBatchUpdates(getCounterValue("database.batch.update.success"));
        status.setTransactionOperations(getCounterValue("database.batch.transaction.success"));
        status.setFailedOperations(getCounterValue("database.batch.insert.failure") + 
                                 getCounterValue("database.batch.update.failure"));
        return status;
    }

    /**
     * 获取第三方API调用状态
     */
    private ThirdPartyApiFlowStatus getThirdPartyApiStatus() {
        ThirdPartyApiFlowStatus status = new ThirdPartyApiFlowStatus();
        status.setSuccessfulCalls(getCounterValue("resume.parse.direct.success"));
        status.setFailedCalls(getCounterValue("resume.parse.direct.failed"));
        status.setCircuitBreakerTriggered(getCounterValue("mq.message.consume.circuit_breaker"));
        status.setRetryAttempts(getCounterValue("retry.attempt"));
        return status;
    }

    /**
     * 获取数据转换状态
     */
    private DataConversionFlowStatus getDataConversionStatus() {
        DataConversionFlowStatus status = new DataConversionFlowStatus();
        status.setSuccessfulConversions(getCounterValue("resume.data.conversion.success"));
        status.setFailedConversions(getCounterValue("resume.data.conversion.failure"));
        status.setBasicInfoConversions(getCounterValue("resume.conversion.basic_info.success"));
        status.setEducationConversions(getCounterValue("resume.conversion.education.success"));
        status.setWorkExperienceConversions(getCounterValue("resume.conversion.work_experience.success"));
        return status;
    }

    /**
     * 从指标注册表获取计数器值
     */
    private double getCounterValue(String name) {
        try {
            return meterRegistry.find(name).counter() != null ?
                meterRegistry.find(name).counter().count() : 0.0;
        } catch (Exception e) {
            log.warn("获取指标值失败: {}", name, e);
            return 0.0;
        }
    }

    /**
     * 系统监控数据
     */
    public static class SystemMonitoringData {
        private LocalDateTime timestamp;
        private Map<String, ThreadPoolMonitorService.ThreadPoolStatus> threadPoolStatus;
        private BackpressureControlService.BackpressureStatistics backpressureStatus;
        private DeadLetterQueueService.DeadLetterStatistics deadLetterStatistics;
        private MessageRetryService.RetryStatistics retryStatistics;
        private BatchDatabaseService.DatabaseConnectionPoolStatus databaseStatus;
        private BusinessMetrics businessMetrics;
        private SystemHealth systemHealth;

        // Getters and Setters
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
        public Map<String, ThreadPoolMonitorService.ThreadPoolStatus> getThreadPoolStatus() { return threadPoolStatus; }
        public void setThreadPoolStatus(Map<String, ThreadPoolMonitorService.ThreadPoolStatus> threadPoolStatus) { this.threadPoolStatus = threadPoolStatus; }
        public BackpressureControlService.BackpressureStatistics getBackpressureStatus() { return backpressureStatus; }
        public void setBackpressureStatus(BackpressureControlService.BackpressureStatistics backpressureStatus) { this.backpressureStatus = backpressureStatus; }
        public DeadLetterQueueService.DeadLetterStatistics getDeadLetterStatistics() { return deadLetterStatistics; }
        public void setDeadLetterStatistics(DeadLetterQueueService.DeadLetterStatistics deadLetterStatistics) { this.deadLetterStatistics = deadLetterStatistics; }
        public MessageRetryService.RetryStatistics getRetryStatistics() { return retryStatistics; }
        public void setRetryStatistics(MessageRetryService.RetryStatistics retryStatistics) { this.retryStatistics = retryStatistics; }
        public BatchDatabaseService.DatabaseConnectionPoolStatus getDatabaseStatus() { return databaseStatus; }
        public void setDatabaseStatus(BatchDatabaseService.DatabaseConnectionPoolStatus databaseStatus) { this.databaseStatus = databaseStatus; }
        public BusinessMetrics getBusinessMetrics() { return businessMetrics; }
        public void setBusinessMetrics(BusinessMetrics businessMetrics) { this.businessMetrics = businessMetrics; }
        public SystemHealth getSystemHealth() { return systemHealth; }
        public void setSystemHealth(SystemHealth systemHealth) { this.systemHealth = systemHealth; }
    }

    /**
     * 业务指标
     */
    public static class BusinessMetrics {
        private double totalMessagesProcessed;
        private double totalMessagesFailed;
        private double totalFilesUploaded;
        private double totalDatabaseOperations;
        private double totalResumesParsed;
        private double totalDataConversions;
        private double messageSuccessRate;

        // Getters and Setters
        public double getTotalMessagesProcessed() { return totalMessagesProcessed; }
        public void setTotalMessagesProcessed(double totalMessagesProcessed) { this.totalMessagesProcessed = totalMessagesProcessed; }
        public double getTotalMessagesFailed() { return totalMessagesFailed; }
        public void setTotalMessagesFailed(double totalMessagesFailed) { this.totalMessagesFailed = totalMessagesFailed; }
        public double getTotalFilesUploaded() { return totalFilesUploaded; }
        public void setTotalFilesUploaded(double totalFilesUploaded) { this.totalFilesUploaded = totalFilesUploaded; }
        public double getTotalDatabaseOperations() { return totalDatabaseOperations; }
        public void setTotalDatabaseOperations(double totalDatabaseOperations) { this.totalDatabaseOperations = totalDatabaseOperations; }
        public double getTotalResumesParsed() { return totalResumesParsed; }
        public void setTotalResumesParsed(double totalResumesParsed) { this.totalResumesParsed = totalResumesParsed; }
        public double getTotalDataConversions() { return totalDataConversions; }
        public void setTotalDataConversions(double totalDataConversions) { this.totalDataConversions = totalDataConversions; }
        public double getMessageSuccessRate() { return messageSuccessRate; }
        public void setMessageSuccessRate(double messageSuccessRate) { this.messageSuccessRate = messageSuccessRate; }
    }

    /**
     * 系统健康状态
     */
    public static class SystemHealth {
        private String overallStatus;
        private int healthScore;
        private boolean threadPoolHealthy;
        private boolean backpressureHealthy;
        private boolean databaseHealthy;
        private boolean deadLetterHealthy;

        // Getters and Setters
        public String getOverallStatus() { return overallStatus; }
        public void setOverallStatus(String overallStatus) { this.overallStatus = overallStatus; }
        public int getHealthScore() { return healthScore; }
        public void setHealthScore(int healthScore) { this.healthScore = healthScore; }
        public boolean isThreadPoolHealthy() { return threadPoolHealthy; }
        public void setThreadPoolHealthy(boolean threadPoolHealthy) { this.threadPoolHealthy = threadPoolHealthy; }
        public boolean isBackpressureHealthy() { return backpressureHealthy; }
        public void setBackpressureHealthy(boolean backpressureHealthy) { this.backpressureHealthy = backpressureHealthy; }
        public boolean isDatabaseHealthy() { return databaseHealthy; }
        public void setDatabaseHealthy(boolean databaseHealthy) { this.databaseHealthy = databaseHealthy; }
        public boolean isDeadLetterHealthy() { return deadLetterHealthy; }
        public void setDeadLetterHealthy(boolean deadLetterHealthy) { this.deadLetterHealthy = deadLetterHealthy; }
    }

    /**
     * 数据流转状态
     */
    public static class DataFlowStatus {
        private LocalDateTime timestamp;
        private MessageQueueFlowStatus messageQueueStatus;
        private OssStorageFlowStatus ossStorageStatus;
        private DatabaseOperationFlowStatus databaseOperationStatus;
        private ThirdPartyApiFlowStatus thirdPartyApiStatus;
        private DataConversionFlowStatus dataConversionStatus;

        // Getters and Setters
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
        public MessageQueueFlowStatus getMessageQueueStatus() { return messageQueueStatus; }
        public void setMessageQueueStatus(MessageQueueFlowStatus messageQueueStatus) { this.messageQueueStatus = messageQueueStatus; }
        public OssStorageFlowStatus getOssStorageStatus() { return ossStorageStatus; }
        public void setOssStorageStatus(OssStorageFlowStatus ossStorageStatus) { this.ossStorageStatus = ossStorageStatus; }
        public DatabaseOperationFlowStatus getDatabaseOperationStatus() { return databaseOperationStatus; }
        public void setDatabaseOperationStatus(DatabaseOperationFlowStatus databaseOperationStatus) { this.databaseOperationStatus = databaseOperationStatus; }
        public ThirdPartyApiFlowStatus getThirdPartyApiStatus() { return thirdPartyApiStatus; }
        public void setThirdPartyApiStatus(ThirdPartyApiFlowStatus thirdPartyApiStatus) { this.thirdPartyApiStatus = thirdPartyApiStatus; }
        public DataConversionFlowStatus getDataConversionStatus() { return dataConversionStatus; }
        public void setDataConversionStatus(DataConversionFlowStatus dataConversionStatus) { this.dataConversionStatus = dataConversionStatus; }
    }

    /**
     * 消息队列流转状态
     */
    public static class MessageQueueFlowStatus {
        private double producedMessages;
        private double consumedMessages;
        private double failedMessages;
        private double deadLetterMessages;

        // Getters and Setters
        public double getProducedMessages() { return producedMessages; }
        public void setProducedMessages(double producedMessages) { this.producedMessages = producedMessages; }
        public double getConsumedMessages() { return consumedMessages; }
        public void setConsumedMessages(double consumedMessages) { this.consumedMessages = consumedMessages; }
        public double getFailedMessages() { return failedMessages; }
        public void setFailedMessages(double failedMessages) { this.failedMessages = failedMessages; }
        public double getDeadLetterMessages() { return deadLetterMessages; }
        public void setDeadLetterMessages(double deadLetterMessages) { this.deadLetterMessages = deadLetterMessages; }
    }

    /**
     * OSS存储流转状态
     */
    public static class OssStorageFlowStatus {
        private double uploadedFiles;
        private double failedUploads;
        private double downloadedFiles;
        private double cacheHits;

        // Getters and Setters
        public double getUploadedFiles() { return uploadedFiles; }
        public void setUploadedFiles(double uploadedFiles) { this.uploadedFiles = uploadedFiles; }
        public double getFailedUploads() { return failedUploads; }
        public void setFailedUploads(double failedUploads) { this.failedUploads = failedUploads; }
        public double getDownloadedFiles() { return downloadedFiles; }
        public void setDownloadedFiles(double downloadedFiles) { this.downloadedFiles = downloadedFiles; }
        public double getCacheHits() { return cacheHits; }
        public void setCacheHits(double cacheHits) { this.cacheHits = cacheHits; }
    }

    /**
     * 数据库操作流转状态
     */
    public static class DatabaseOperationFlowStatus {
        private double batchInserts;
        private double batchUpdates;
        private double transactionOperations;
        private double failedOperations;

        // Getters and Setters
        public double getBatchInserts() { return batchInserts; }
        public void setBatchInserts(double batchInserts) { this.batchInserts = batchInserts; }
        public double getBatchUpdates() { return batchUpdates; }
        public void setBatchUpdates(double batchUpdates) { this.batchUpdates = batchUpdates; }
        public double getTransactionOperations() { return transactionOperations; }
        public void setTransactionOperations(double transactionOperations) { this.transactionOperations = transactionOperations; }
        public double getFailedOperations() { return failedOperations; }
        public void setFailedOperations(double failedOperations) { this.failedOperations = failedOperations; }
    }

    /**
     * 第三方API流转状态
     */
    public static class ThirdPartyApiFlowStatus {
        private double successfulCalls;
        private double failedCalls;
        private double circuitBreakerTriggered;
        private double retryAttempts;

        // Getters and Setters
        public double getSuccessfulCalls() { return successfulCalls; }
        public void setSuccessfulCalls(double successfulCalls) { this.successfulCalls = successfulCalls; }
        public double getFailedCalls() { return failedCalls; }
        public void setFailedCalls(double failedCalls) { this.failedCalls = failedCalls; }
        public double getCircuitBreakerTriggered() { return circuitBreakerTriggered; }
        public void setCircuitBreakerTriggered(double circuitBreakerTriggered) { this.circuitBreakerTriggered = circuitBreakerTriggered; }
        public double getRetryAttempts() { return retryAttempts; }
        public void setRetryAttempts(double retryAttempts) { this.retryAttempts = retryAttempts; }
    }

    /**
     * 数据转换流转状态
     */
    public static class DataConversionFlowStatus {
        private double successfulConversions;
        private double failedConversions;
        private double basicInfoConversions;
        private double educationConversions;
        private double workExperienceConversions;

        // Getters and Setters
        public double getSuccessfulConversions() { return successfulConversions; }
        public void setSuccessfulConversions(double successfulConversions) { this.successfulConversions = successfulConversions; }
        public double getFailedConversions() { return failedConversions; }
        public void setFailedConversions(double failedConversions) { this.failedConversions = failedConversions; }
        public double getBasicInfoConversions() { return basicInfoConversions; }
        public void setBasicInfoConversions(double basicInfoConversions) { this.basicInfoConversions = basicInfoConversions; }
        public double getEducationConversions() { return educationConversions; }
        public void setEducationConversions(double educationConversions) { this.educationConversions = educationConversions; }
        public double getWorkExperienceConversions() { return workExperienceConversions; }
        public void setWorkExperienceConversions(double workExperienceConversions) { this.workExperienceConversions = workExperienceConversions; }
    }
}
