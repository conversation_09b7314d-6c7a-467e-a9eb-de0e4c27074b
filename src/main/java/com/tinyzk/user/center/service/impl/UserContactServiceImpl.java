package com.tinyzk.user.center.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tinyzk.user.center.common.annotation.UserAudit;
import com.tinyzk.user.center.common.enums.OperationType;
import com.tinyzk.user.center.common.exception.BusinessException;
import com.tinyzk.user.center.common.exception.ErrorCode;
import com.tinyzk.user.center.dto.CreateContactDTO;
import com.tinyzk.user.center.dto.UpdateContactDTO;
import com.tinyzk.user.center.entity.UserContactMethods;
import com.tinyzk.user.center.mapper.UserContactMethodsMapper;
import com.tinyzk.user.center.service.UserContactService;
import com.tinyzk.user.center.vo.UserContactVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户联系方式服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserContactServiceImpl implements UserContactService {

    private final UserContactMethodsMapper userContactMethodsMapper;

    @Override
    public List<UserContactVO> getUserContacts(Long userId) {
        // 查询用户所有联系方式
        List<UserContactMethods> contacts = userContactMethodsMapper.selectList(new LambdaQueryWrapper<UserContactMethods>()
                .eq(UserContactMethods::getUserId, userId));
        
        // 转换为VO
        return contacts.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public UserContactVO getUserContact(Long userId, Long contactId) {
        // 查询特定联系方式
        UserContactMethods contact = userContactMethodsMapper.selectById(contactId);
        
        // 检查是否存在
        if (contact == null || !contact.getUserId().equals(userId)) {
            throw new BusinessException(ErrorCode.NOT_FOUND, "联系方式不存在");
        }
        
        // 转换为VO
        return convertToVO(contact);
    }

    @Override
    @Transactional
    @UserAudit(type = OperationType.CONTACT_CREATE,failType = OperationType.CONTACT_CREATE_FAIL, detail = "创建用户联系方式")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public UserContactVO createUserContact(Long userId, CreateContactDTO createContactDTO) {
        // 创建实体对象
        UserContactMethods contact = new UserContactMethods();
        
        // 拷贝属性
        BeanUtils.copyProperties(createContactDTO, contact);
        contact.setUserId(userId);
        contact.setCreatedAt(LocalDateTime.now());
        contact.setUpdatedAt(LocalDateTime.now());
        
        // 插入数据库
        userContactMethodsMapper.insert(contact);
        
        // 返回VO
        return convertToVO(contact);
    }

    @Override
    @Transactional
    @UserAudit(type = OperationType.CONTACT_UPDATE,failType = OperationType.CONTACT_UPDATE_FAIL, detail = "更新用户联系方式")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public UserContactVO updateUserContact(Long userId, Long contactId, UpdateContactDTO updateContactDTO) {
        // 查询原联系方式
        UserContactMethods contact = userContactMethodsMapper.selectById(contactId);
        
        // 检查是否存在
        if (contact == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND, "联系方式不存在");
        }
        
        // 检查权限
        if (!contact.getUserId().equals(userId)) {
            throw new BusinessException(ErrorCode.FORBIDDEN, "无权修改该联系方式");
        }
        
        // 更新属性
        BeanUtils.copyProperties(updateContactDTO, contact);
        contact.setUpdatedAt(LocalDateTime.now());
        
        // 更新数据库
        userContactMethodsMapper.updateById(contact);
        
        // 返回VO
        return convertToVO(contact);
    }

    @Override
    @Transactional
    @UserAudit(type = OperationType.CONTACT_DELETE,failType = OperationType.CONTACT_DELETE_FAIL, detail = "删除用户联系方式")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public void deleteUserContact(Long userId, Long contactId) {
        // 查询原联系方式
        UserContactMethods contact = userContactMethodsMapper.selectById(contactId);
        
        // 检查是否存在
        if (contact == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND, "联系方式不存在");
        }
        
        // 检查权限
        if (!contact.getUserId().equals(userId)) {
            throw new BusinessException(ErrorCode.FORBIDDEN, "无权删除该联系方式");
        }
        
        // 删除联系方式（逻辑删除）
        userContactMethodsMapper.deleteById(contactId);
    }
    
    /**
     * 将实体对象转换为VO
     */
    private UserContactVO convertToVO(UserContactMethods contact) {
        UserContactVO vo = new UserContactVO();
        BeanUtils.copyProperties(contact, vo);
        return vo;
    }
} 