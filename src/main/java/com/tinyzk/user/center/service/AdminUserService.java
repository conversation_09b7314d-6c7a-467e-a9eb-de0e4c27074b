package com.tinyzk.user.center.service;

import com.tinyzk.user.center.dto.UpdateUserRequestDTO;
import com.tinyzk.user.center.dto.UserListRequestDTO;
import com.tinyzk.user.center.vo.PageResponseVO;
import com.tinyzk.user.center.vo.UserDetailVO;
import com.tinyzk.user.center.vo.UserListVO;

/**
 * 管理员用户服务接口
 */
public interface AdminUserService {
    
    /**
     * 获取用户列表
     * 
     * @param requestDTO 请求参数
     * @return 用户列表分页数据
     */
    PageResponseVO<UserListVO> getUserList(UserListRequestDTO requestDTO);
    
    /**
     * 获取用户详情
     * 
     * @param userId 用户ID
     * @return 用户详情信息
     */
    UserDetailVO getUserDetail(Long userId);
    
    /**
     * 更新用户信息
     * 
     * @param userId 用户ID
     * @param requestDTO 更新用户信息请求DTO
     * @return 是否更新成功
     */
    boolean updateUser(Long userId, UpdateUserRequestDTO requestDTO);
    
    /**
     * 禁用用户
     *
     * @param userId 用户ID
     * @param reason 禁用原因
     * @return 是否操作成功
     */
    boolean disableUser(Long userId, String reason);
    
    /**
     * 启用用户
     *
     * @param userId 用户ID
     * @return 是否操作成功
     */
    boolean enableUser(Long userId);
}
