package com.tinyzk.user.center.service;

import com.tinyzk.user.center.common.enums.OperationType;
import com.tinyzk.user.center.entity.UserAuditLog;

/**
 * 用户审计日志服务接口
 */
public interface UserAuditLogService {
    
    /**
     * 记录审计日志
     *
     * @param userId        用户ID
     * @param operationType 操作类型
     * @param detail        操作详情
     * @param ipAddress     IP地址
     * @param userAgent     用户代理
     * @return 日志记录
     */
    UserAuditLog recordLog(Long userId, OperationType operationType, String detail, String ipAddress, String userAgent);
}
