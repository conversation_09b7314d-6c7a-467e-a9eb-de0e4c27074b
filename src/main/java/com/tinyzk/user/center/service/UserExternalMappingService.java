package com.tinyzk.user.center.service;

import com.tinyzk.user.center.entity.UserExternalMapping;

/**
 * 用户外部系统映射服务接口
 */
public interface UserExternalMappingService {
    
    /**
     * 根据外部用户ID和客户端ID查询用户中心用户ID
     *
     * @param externalUserId 外部用户ID
     * @param clientId 客户端ID
     * @return 用户中心用户ID，如果不存在返回null
     */
    Long getUserIdByExternalId(String externalUserId, String clientId);
    
    /**
     * 根据用户中心用户ID和客户端ID查询外部用户ID
     *
     * @param userId 用户中心用户ID
     * @param clientId 客户端ID
     * @return 外部用户ID，如果不存在返回null
     */
    String getExternalIdByUserId(Long userId, String clientId);
    
    /**
     * 创建用户映射关系
     *
     * @param userId 用户中心用户ID
     * @param externalUserId 外部用户ID
     * @param clientId 客户端ID
     * @param metadata 附加信息
     * @return 映射关系记录
     */
    UserExternalMapping createMapping(Long userId, String externalUserId, String clientId, String metadata);
    
    /**
     * 更新映射关系的附加信息
     *
     * @param userId 用户中心用户ID
     * @param clientId 客户端ID
     * @param metadata 新的附加信息
     * @return 是否更新成功
     */
    boolean updateMappingMetadata(Long userId, String clientId, String metadata);
    
    /**
     * 删除映射关系
     *
     * @param userId 用户中心用户ID
     * @param clientId 客户端ID
     * @return 是否删除成功
     */
    boolean deleteMapping(Long userId, String clientId);
    
    /**
     * 检查用户是否有权限访问指定数据
     * 根据当前客户端和用户映射关系检查数据权限
     *
     * @param userId 要访问的用户ID
     * @param currentClientId 当前客户端ID
     * @return 是否有权限
     */
    boolean hasDataAccess(Long userId, String currentClientId);
    
    /**
     * 获取用户在当前客户端系统中的外部ID
     * 
     * @param userId 用户中心用户ID
     * @return 外部用户ID，如果当前用户不属于当前客户端系统返回null
     */
    String getCurrentClientExternalId(Long userId);
} 