package com.tinyzk.user.center.service;

import com.tinyzk.user.center.dto.CreateContactDTO;
import com.tinyzk.user.center.dto.UpdateContactDTO;
import com.tinyzk.user.center.vo.UserContactVO;

import java.util.List;

/**
 * 用户联系方式服务接口
 */
public interface UserContactService {

    /**
     * 获取用户联系方式列表
     *
     * @param userId 用户ID
     * @return 联系方式列表
     */
    List<UserContactVO> getUserContacts(Long userId);

    /**
     * 获取用户特定联系方式
     *
     * @param userId    用户ID
     * @param contactId 联系方式ID
     * @return 联系方式详情
     */
    UserContactVO getUserContact(Long userId, Long contactId);

    /**
     * 创建用户联系方式
     *
     * @param userId          用户ID
     * @param createContactDTO 创建联系方式信息
     * @return 创建后的联系方式
     */
    UserContactVO createUserContact(Long userId, CreateContactDTO createContactDTO);

    /**
     * 更新用户联系方式
     *
     * @param userId          用户ID
     * @param contactId       联系方式ID
     * @param updateContactDTO 更新信息
     * @return 更新后的联系方式
     */
    UserContactVO updateUserContact(Long userId, Long contactId, UpdateContactDTO updateContactDTO);

    /**
     * 删除用户联系方式
     *
     * @param userId    用户ID
     * @param contactId 联系方式ID
     */
    void deleteUserContact(Long userId, Long contactId);
} 