package com.tinyzk.user.center.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tinyzk.user.center.common.annotation.UserAudit;
import com.tinyzk.user.center.common.enums.OperationType;
import com.tinyzk.user.center.common.exception.BusinessException;
import com.tinyzk.user.center.common.exception.ErrorCode;
import com.tinyzk.user.center.dto.CreateWorkDTO;
import com.tinyzk.user.center.dto.UpdateWorkDTO;
import com.tinyzk.user.center.entity.UserWorkHistory;
import com.tinyzk.user.center.mapper.UserWorkHistoryMapper;
import com.tinyzk.user.center.service.UserWorkHistoryService;
import com.tinyzk.user.center.vo.UserWorkVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户工作经历服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserWorkHistoryServiceImpl extends ServiceImpl<UserWorkHistoryMapper, UserWorkHistory> implements UserWorkHistoryService {

    private final UserWorkHistoryMapper userWorkHistoryMapper;

    @Override
    public List<UserWorkVO> getUserWorks(Long userId) {
        log.info("获取用户工作经历列表: userId={}", userId);
        LambdaQueryWrapper<UserWorkHistory> queryWrapper = new LambdaQueryWrapper<UserWorkHistory>()
                .eq(UserWorkHistory::getUserId, userId)
                .orderByDesc(UserWorkHistory::getCreatedAt);
                
        List<UserWorkHistory> workHistories = userWorkHistoryMapper.selectList(queryWrapper);
        
        List<UserWorkVO> workVOs = workHistories.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
                
        log.info("获取用户工作经历列表成功: userId={}, count={}", userId, workVOs.size());
        return workVOs;
    }

    @Override
    public UserWorkVO getUserWork(Long userId, Long workId) {
        log.info("获取用户工作经历: userId={}, workId={}", userId, workId);
        UserWorkHistory workHistory = getUserWorkOrThrow(userId, workId);
        UserWorkVO workVO = convertToVO(workHistory);
        log.info("获取用户工作经历成功: userId={}, workId={}", userId, workId);
        return workVO;
    }

    @Override
    @UserAudit(type = OperationType.WORK_CREATE,failType = OperationType.WORK_CREATE_FAIL, detail = "创建用户工作经历")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public UserWorkVO createUserWork(Long userId, CreateWorkDTO createWorkDTO) {
        log.info("创建用户工作经历: userId={}", userId);
        
        UserWorkHistory workHistory = new UserWorkHistory();
        BeanUtils.copyProperties(createWorkDTO, workHistory);
        workHistory.setUserId(userId);
        
        userWorkHistoryMapper.insert(workHistory);
        
        UserWorkVO workVO = convertToVO(workHistory);
        log.info("创建用户工作经历成功: userId={}, workId={}", userId, workVO.getWorkId());
        return workVO;
    }

    @Override
    @UserAudit(type = OperationType.WORK_UPDATE,failType = OperationType.WORK_UPDATE_FAIL, detail = "更新用户工作经历")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public UserWorkVO updateUserWork(Long userId, Long workId, UpdateWorkDTO updateWorkDTO) {
        log.info("更新用户工作经历: userId={}, workId={}", userId, workId);
        
        UserWorkHistory workHistory = getUserWorkOrThrow(userId, workId);
        
        // 只更新非null字段
        if (updateWorkDTO.getCompanyName() != null) {
            workHistory.setCompanyName(updateWorkDTO.getCompanyName());
        }
        if (updateWorkDTO.getCompanyLogo() != null) {
            workHistory.setCompanyLogo(updateWorkDTO.getCompanyLogo());
        }
        if (updateWorkDTO.getCompanyUrl() != null) {
            workHistory.setCompanyUrl(updateWorkDTO.getCompanyUrl());
        }
        if (updateWorkDTO.getCompanySize() != null) {
            workHistory.setCompanySize(updateWorkDTO.getCompanySize());
        }
        if (updateWorkDTO.getCompanyIndustry() != null) {
            workHistory.setCompanyIndustry(updateWorkDTO.getCompanyIndustry());
        }
        if (updateWorkDTO.getCompanyLocation() != null) {
            workHistory.setCompanyLocation(updateWorkDTO.getCompanyLocation());
        }
        if (updateWorkDTO.getPosition() != null) {
            workHistory.setPositionName(updateWorkDTO.getPosition());
        }
        if (updateWorkDTO.getDepartment() != null) {
            workHistory.setDepartment(updateWorkDTO.getDepartment());
        }
        if (updateWorkDTO.getStartDate() != null) {
            workHistory.setStartDate(updateWorkDTO.getStartDate());
        }
        if (updateWorkDTO.getEndDate() != null) {
            workHistory.setEndDate(updateWorkDTO.getEndDate());
        }
        if (updateWorkDTO.getDescription() != null) {
            workHistory.setDescription(updateWorkDTO.getDescription());
        }
        if (updateWorkDTO.getAchievements() != null) {
            workHistory.setAchievements(updateWorkDTO.getAchievements());
        }
        if (updateWorkDTO.getReportingTo() != null) {
            workHistory.setReportingTo(updateWorkDTO.getReportingTo());
        }
        if (updateWorkDTO.getReasonForLeaving() != null) {
            workHistory.setReasonForLeaving(updateWorkDTO.getReasonForLeaving());
        }
        if (updateWorkDTO.getSalaryMin() != null) {
            workHistory.setSalaryMin(updateWorkDTO.getSalaryMin());
        }
        if (updateWorkDTO.getSalaryMax() != null) {
            workHistory.setSalaryMax(updateWorkDTO.getSalaryMax());
        }
        if (updateWorkDTO.getCertificationType() != null) {
            workHistory.setCertificationType(updateWorkDTO.getCertificationType());
        }
        if (updateWorkDTO.getCertificationStatus() != null) {
            workHistory.setCertificationStatus(updateWorkDTO.getCertificationStatus());
        }
        if (updateWorkDTO.getVisibility() != null) {
            workHistory.setVisibility(updateWorkDTO.getVisibility());
        }
        
        userWorkHistoryMapper.updateById(workHistory);
        
        UserWorkVO workVO = convertToVO(workHistory);
        log.info("更新用户工作经历成功: userId={}, workId={}", userId, workId);
        return workVO;
    }

    @Override
    @UserAudit(type = OperationType.WORK_DELETE,failType = OperationType.WORK_DELETE_FAIL, detail = "删除用户工作经历")
    @Caching(evict = {
        @CacheEvict(value = "userDetailCache", key = "#userId")
    })
    public void deleteUserWork(Long userId, Long workId) {
        log.info("删除用户工作经历: userId={}, workId={}", userId, workId);
        
        UserWorkHistory workHistory = getUserWorkOrThrow(userId, workId);
        
        userWorkHistoryMapper.deleteById(workId);
        
        log.info("删除用户工作经历成功: userId={}, workId={}", userId, workId);
    }
    
    /**
     * 获取用户工作经历，如果不存在则抛出异常
     */
    private UserWorkHistory getUserWorkOrThrow(Long userId, Long workId) {
        LambdaQueryWrapper<UserWorkHistory> queryWrapper = new LambdaQueryWrapper<UserWorkHistory>()
                .eq(UserWorkHistory::getUserId, userId)
                .eq(UserWorkHistory::getWorkId, workId);
                
        UserWorkHistory workHistory = userWorkHistoryMapper.selectOne(queryWrapper);
        
        if (workHistory == null) {
            log.warn("用户工作经历不存在: userId={}, workId={}", userId, workId);
            throw new BusinessException(ErrorCode.NOT_FOUND, "工作经历不存在");
        }
        
        return workHistory;
    }
    
    /**
     * 将实体转换为VO对象
     */
    private UserWorkVO convertToVO(UserWorkHistory workHistory) {
        UserWorkVO workVO = new UserWorkVO();
        BeanUtils.copyProperties(workHistory, workVO);
        return workVO;
    }
} 