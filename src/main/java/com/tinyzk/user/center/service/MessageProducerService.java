package com.tinyzk.user.center.service;

import com.alibaba.fastjson2.JSON;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * 消息生产服务
 */
@Service
@Slf4j
public class MessageProducerService {

    private final DefaultMQProducer resumeParseProducer;
    private final DefaultMQProducer fileUploadProducer;
    private final DefaultMQProducer generalProducer;
    private final RateLimiter producerLimiter;
    private final MeterRegistry meterRegistry;

    // 主题定义
    public static final String RESUME_PARSE_TOPIC = "RESUME_PARSE_TOPIC";
    public static final String FILE_UPLOAD_TOPIC = "FILE_UPLOAD_TOPIC";
    public static final String GENERAL_TOPIC = "GENERAL_TOPIC";

    public MessageProducerService(@Qualifier("resumeParseProducer") DefaultMQProducer resumeParseProducer,
                                 @Qualifier("fileUploadProducer") DefaultMQProducer fileUploadProducer,
                                 @Qualifier("generalProducer") DefaultMQProducer generalProducer,
                                 MeterRegistry meterRegistry) {
        this.resumeParseProducer = resumeParseProducer;
        this.fileUploadProducer = fileUploadProducer;
        this.generalProducer = generalProducer;
        this.meterRegistry = meterRegistry;
        
        // 使用Resilience4j的RateLimiter，限制生产速度
        this.producerLimiter = RateLimiter.of("message-producer",
            RateLimiterConfig.custom()
                .limitForPeriod(50) // 每个周期50个请求
                .limitRefreshPeriod(Duration.ofSeconds(1)) // 1秒刷新周期
                .timeoutDuration(Duration.ofMillis(100)) // 100ms超时
                .build());
    }

    /**
     * 发送简历解析消息
     */
    public void sendResumeParseMessage(Object message) {
        // 使用Resilience4j RateLimiter
        String result = producerLimiter.executeSupplier(() -> {
            return sendMessageInternal(RESUME_PARSE_TOPIC, "PARSE", message, resumeParseProducer);
        });

        if (result == null) {
            throw new RuntimeException("消息发送频率过高，请稍后重试");
        }
    }

    /**
     * 发送文件上传消息
     */
    public void sendFileUploadMessage(Object message) {
        sendMessageInternal(FILE_UPLOAD_TOPIC, "UPLOAD", message, fileUploadProducer);
    }

    /**
     * 发送通用消息
     */
    public void sendGeneralMessage(String topic, String tag, Object message) {
        sendMessageInternal(topic, tag, message, generalProducer);
    }

    /**
     * 内部消息发送方法
     */
    private String sendMessageInternal(String topic, String tag, Object message, DefaultMQProducer producer) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            String messageId = generateMessageId();
            Message mqMessage = new Message(
                topic,
                tag,
                messageId,
                JSON.toJSONBytes(message)
            );

            // 设置消息属性
            mqMessage.putUserProperty("messageId", messageId);
            mqMessage.putUserProperty("timestamp", String.valueOf(System.currentTimeMillis()));

            // 异步发送
            producer.send(mqMessage, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.debug("消息发送成功: topic={}, messageId={}, queueId={}",
                            topic, messageId, sendResult.getMessageQueue().getQueueId());
                    meterRegistry.counter("mq.message.send.success",
                        "topic", topic).increment();
                }

                @Override
                public void onException(Throwable e) {
                    log.error("消息发送失败: topic={}, messageId={}", topic, messageId, e);
                    meterRegistry.counter("mq.message.send.failure",
                        "topic", topic).increment();
                    // 可以实现重试或死信队列逻辑
                }
            });

            return "SUCCESS";

        } catch (Exception e) {
            log.error("发送消息异常: topic={}", topic, e);
            meterRegistry.counter("mq.message.send.error",
                "topic", topic).increment();
            throw new RuntimeException("消息发送失败", e);
        } finally {
            sample.stop(Timer.builder("mq.message.send.duration")
                .tag("topic", topic)
                .register(meterRegistry));
        }
    }

    /**
     * 同步发送消息
     */
    public SendResult sendMessageSync(String topic, String tag, Object message) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            String messageId = generateMessageId();
            Message mqMessage = new Message(
                topic,
                tag,
                messageId,
                JSON.toJSONBytes(message)
            );

            // 设置消息属性
            mqMessage.putUserProperty("messageId", messageId);
            mqMessage.putUserProperty("timestamp", String.valueOf(System.currentTimeMillis()));

            SendResult result = generalProducer.send(mqMessage);
            log.debug("同步消息发送成功: topic={}, messageId={}, result={}",
                    topic, messageId, result.getSendStatus());

            meterRegistry.counter("mq.message.send.sync.success",
                "topic", topic).increment();

            return result;

        } catch (Exception e) {
            log.error("同步发送消息失败: topic={}", topic, e);
            meterRegistry.counter("mq.message.send.sync.failure",
                "topic", topic).increment();
            throw new RuntimeException("同步消息发送失败", e);
        } finally {
            sample.stop(Timer.builder("mq.message.send.sync.duration")
                .tag("topic", topic)
                .register(meterRegistry));
        }
    }

    /**
     * 生成消息ID
     */
    private String generateMessageId() {
        return System.currentTimeMillis() + "-" + Thread.currentThread().getId();
    }
}
