package com.tinyzk.user.center.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tinyzk.user.center.dto.CreateWorkDTO;
import com.tinyzk.user.center.dto.UpdateWorkDTO;
import com.tinyzk.user.center.entity.UserWorkHistory;
import com.tinyzk.user.center.vo.UserWorkVO;

import java.util.List;

/**
 * 用户工作经历服务接口
 */
public interface UserWorkHistoryService extends IService<UserWorkHistory> {

    /**
     * 获取用户所有工作经历
     *
     * @param userId 用户ID
     * @return 工作经历列表
     */
    List<UserWorkVO> getUserWorks(Long userId);

    /**
     * 获取用户特定工作经历
     *
     * @param userId 用户ID
     * @param workId 工作经历ID
     * @return 工作经历详情
     */
    UserWorkVO getUserWork(Long userId, Long workId);

    /**
     * 创建用户工作经历
     *
     * @param userId       用户ID
     * @param createWorkDTO 创建工作经历请求
     * @return 创建后的工作经历
     */
    UserWorkVO createUserWork(Long userId, CreateWorkDTO createWorkDTO);

    /**
     * 更新用户工作经历
     *
     * @param userId       用户ID
     * @param workId       工作经历ID
     * @param updateWorkDTO 更新工作经历请求
     * @return 更新后的工作经历
     */
    UserWorkVO updateUserWork(Long userId, Long workId, UpdateWorkDTO updateWorkDTO);

    /**
     * 删除用户工作经历
     *
     * @param userId 用户ID
     * @param workId 工作经历ID
     */
    void deleteUserWork(Long userId, Long workId);
} 