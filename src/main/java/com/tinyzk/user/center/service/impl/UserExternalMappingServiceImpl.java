package com.tinyzk.user.center.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tinyzk.user.center.common.context.ClientContext;
import com.tinyzk.user.center.entity.UserExternalMapping;
import com.tinyzk.user.center.mapper.UserExternalMappingMapper;
import com.tinyzk.user.center.service.UserExternalMappingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 用户外部系统映射服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserExternalMappingServiceImpl extends ServiceImpl<UserExternalMappingMapper, UserExternalMapping> 
        implements UserExternalMappingService {

    private final UserExternalMappingMapper userExternalMappingMapper;

    @Override
    public Long getUserIdByExternalId(String externalUserId, String clientId) {
        if (externalUserId == null || clientId == null) {
            return null;
        }
        return userExternalMappingMapper.getUserIdByExternalId(externalUserId, clientId);
    }

    @Override
    public String getExternalIdByUserId(Long userId, String clientId) {
        if (userId == null || clientId == null) {
            return null;
        }
        return userExternalMappingMapper.getExternalIdByUserId(userId, clientId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserExternalMapping createMapping(Long userId, String externalUserId, String clientId, String metadata) {
        log.info("创建用户映射关系: userId={}, externalUserId={}, clientId={}", userId, externalUserId, clientId);
        
        // 检查是否已存在映射关系
        LambdaQueryWrapper<UserExternalMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserExternalMapping::getUserId, userId)
                .eq(UserExternalMapping::getExternalSystemClientId, clientId);
        
        UserExternalMapping existingMapping = this.getOne(queryWrapper);
        if (existingMapping != null) {
            log.warn("用户映射关系已存在: userId={}, clientId={}", userId, clientId);
            return existingMapping;
        }
        
        // 创建新的映射关系
        UserExternalMapping mapping = new UserExternalMapping();
        mapping.setUserId(userId);
        mapping.setExternalUserId(externalUserId);
        mapping.setExternalSystemClientId(clientId);
        mapping.setMetadata(metadata);
        mapping.setCreatedAt(LocalDateTime.now());
        mapping.setUpdatedAt(LocalDateTime.now());
        
        boolean saved = this.save(mapping);
        if (saved) {
            log.info("用户映射关系创建成功: mappingId={}", mapping.getMappingId());
            return mapping;
        } else {
            log.error("用户映射关系创建失败: userId={}, clientId={}", userId, clientId);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMappingMetadata(Long userId, String clientId, String metadata) {
        log.info("更新用户映射关系元数据: userId={}, clientId={}", userId, clientId);
        
        LambdaUpdateWrapper<UserExternalMapping> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserExternalMapping::getUserId, userId)
                .eq(UserExternalMapping::getExternalSystemClientId, clientId)
                .set(UserExternalMapping::getMetadata, metadata)
                .set(UserExternalMapping::getUpdatedAt, LocalDateTime.now());
        
        return this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMapping(Long userId, String clientId) {
        log.info("删除用户映射关系: userId={}, clientId={}", userId, clientId);
        
        LambdaQueryWrapper<UserExternalMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserExternalMapping::getUserId, userId)
                .eq(UserExternalMapping::getExternalSystemClientId, clientId);
        
        return this.remove(queryWrapper);
    }

    @Override
    public boolean hasDataAccess(Long userId, String currentClientId) {
        if (userId == null || currentClientId == null) {
            return false;
        }
        
        // 如果是匿名客户端，没有数据访问权限
        if ("anonymous".equals(currentClientId)) {
            log.debug("匿名客户端无数据访问权限: userId={}", userId);
            return false;
        }
        
        // 检查用户是否属于当前客户端系统
        String externalUserId = getExternalIdByUserId(userId, currentClientId);
        boolean hasAccess = externalUserId != null;
        
        if (!hasAccess) {
            log.debug("用户不属于当前客户端系统: userId={}, clientId={}", userId, currentClientId);
        }
        
        return hasAccess;
    }

    @Override
    public String getCurrentClientExternalId(Long userId) {
        String currentClientId = ClientContext.getCurrentClientId();
        if ("anonymous".equals(currentClientId)) {
            return null;
        }
        
        return getExternalIdByUserId(userId, currentClientId);
    }
} 