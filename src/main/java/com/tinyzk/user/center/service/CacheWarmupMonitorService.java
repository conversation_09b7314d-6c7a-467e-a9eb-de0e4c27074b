package com.tinyzk.user.center.service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 缓存预热监控服务
 */
@Slf4j
@Service
public class CacheWarmupMonitorService {

    private final MeterRegistry meterRegistry;
    private final RedisTemplate<String, Object> redisTemplate;

    // 监控指标
    private final Counter warmupSuccessCounter;
    private final Counter warmupFailureCounter;
    private final Timer warmupDurationTimer;

    public CacheWarmupMonitorService(MeterRegistry meterRegistry, RedisTemplate<String, Object> redisTemplate) {
        this.meterRegistry = meterRegistry;
        this.redisTemplate = redisTemplate;

        // 初始化监控指标
        this.warmupSuccessCounter = Counter.builder("cache.warmup.success")
            .description("缓存预热成功次数")
            .register(meterRegistry);

        this.warmupFailureCounter = Counter.builder("cache.warmup.failure")
            .description("缓存预热失败次数")
            .register(meterRegistry);

        this.warmupDurationTimer = Timer.builder("cache.warmup.duration")
            .description("缓存预热耗时")
            .register(meterRegistry);
    }
    
    /**
     * 记录预热成功
     */
    public void recordWarmupSuccess(String cacheType, String key) {
        // 使用标签记录指标
        Counter.builder("cache.warmup.success")
            .tag("cache_type", cacheType)
            .tag("status", "success")
            .register(meterRegistry)
            .increment();

        // 记录到Redis用于统计分析
        String statsKey = "cache:warmup:stats:" + getCurrentDate();
        redisTemplate.opsForHash().increment(statsKey, cacheType + ":success", 1);
        redisTemplate.expire(statsKey, 30, TimeUnit.DAYS);

        log.debug("缓存预热成功: type={}, key={}", cacheType, key);
    }

    /**
     * 记录预热失败
     */
    public void recordWarmupFailure(String cacheType, String key, String error) {
        // 使用标签记录指标
        Counter.builder("cache.warmup.failure")
            .tag("cache_type", cacheType)
            .tag("status", "failure")
            .register(meterRegistry)
            .increment();

        // 记录到Redis用于统计分析
        String statsKey = "cache:warmup:stats:" + getCurrentDate();
        redisTemplate.opsForHash().increment(statsKey, cacheType + ":failure", 1);
        redisTemplate.expire(statsKey, 30, TimeUnit.DAYS);

        log.warn("缓存预热失败: type={}, key={}, error={}", cacheType, key, error);
    }
    
    /**
     * 记录预热失败（简化版本）
     */
    public void recordWarmupFailure(String error) {
        recordWarmupFailure("unknown", "unknown", error);
    }
    
    /**
     * 记录预热完成
     */
    public void recordWarmupCompletion(long durationMs) {
        warmupDurationTimer.record(durationMs, TimeUnit.MILLISECONDS);
        
        // 记录预热完成时间和耗时
        String completionKey = "cache:warmup:completion:" + getCurrentDate();
        Map<String, Object> completionData = new HashMap<>();
        completionData.put("timestamp", LocalDateTime.now().toString());
        completionData.put("duration_ms", durationMs);
        completionData.put("status", "completed");
        
        redisTemplate.opsForHash().putAll(completionKey, completionData);
        redisTemplate.expire(completionKey, 30, TimeUnit.DAYS);
        
        log.info("缓存预热完成统计记录: duration={}ms", durationMs);
    }
    
    /**
     * 获取预热统计信息
     */
    public Map<String, Object> getWarmupStats() {
        String statsKey = "cache:warmup:stats:" + getCurrentDate();
        Map<Object, Object> todayStats = redisTemplate.opsForHash().entries(statsKey);
        
        Map<String, Object> result = new HashMap<>();
        result.put("today", todayStats);
        result.put("timestamp", LocalDateTime.now());
        
        // 添加实时指标
        result.put("total_success", warmupSuccessCounter.count());
        result.put("total_failure", warmupFailureCounter.count());
        result.put("avg_duration_ms", warmupDurationTimer.mean(TimeUnit.MILLISECONDS));
        
        return result;
    }
    
    /**
     * 获取缓存命中率统计
     */
    public Map<String, Object> getCacheHitRateStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 这里可以实现缓存命中率的统计逻辑
        // 例如通过Redis的INFO命令获取命中率信息
        try {
            // 示例：获取Redis的统计信息
            // 实际实现需要根据具体的Redis配置和监控需求
            stats.put("redis_hit_rate", "需要实现具体的命中率计算逻辑");
            stats.put("cache_size", "需要实现缓存大小统计");
            
        } catch (Exception e) {
            log.warn("获取缓存命中率统计失败", e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }
    
    /**
     * 记录缓存访问
     */
    public void recordCacheAccess(String cacheType, String key, boolean hit) {
        String accessKey = "cache:access:stats:" + getCurrentDate();
        String hitKey = cacheType + (hit ? ":hit" : ":miss");
        
        redisTemplate.opsForHash().increment(accessKey, hitKey, 1);
        redisTemplate.expire(accessKey, 7, TimeUnit.DAYS);
        
        // 记录热点数据
        if (hit) {
            String hotDataKey = "cache:hotdata:" + cacheType;
            redisTemplate.opsForZSet().incrementScore(hotDataKey, key, 1);
            redisTemplate.expire(hotDataKey, 7, TimeUnit.DAYS);
        }
    }
    
    /**
     * 获取热点数据
     */
    public Map<String, Double> getHotData(String cacheType, int limit) {
        String hotDataKey = "cache:hotdata:" + cacheType;
        var hotData = redisTemplate.opsForZSet().reverseRangeWithScores(hotDataKey, 0, limit - 1);
        
        Map<String, Double> result = new HashMap<>();
        if (hotData != null) {
            hotData.forEach(item -> {
                if (item.getValue() != null && item.getScore() != null) {
                    result.put(item.getValue().toString(), item.getScore());
                }
            });
        }
        
        return result;
    }
    
    /**
     * 清理过期的统计数据
     */
    public void cleanupExpiredStats() {
        try {
            // 清理30天前的统计数据
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(30);
            String cutoffDateStr = cutoffDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            
            // 这里可以实现具体的清理逻辑
            log.info("清理过期统计数据: cutoff={}", cutoffDateStr);
            
        } catch (Exception e) {
            log.error("清理过期统计数据失败", e);
        }
    }
    
    /**
     * 获取当前日期字符串
     */
    private String getCurrentDate() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }
    
    /**
     * 获取预热建议
     */
    public Map<String, Object> getWarmupRecommendations() {
        Map<String, Object> recommendations = new HashMap<>();
        
        try {
            // 基于热点数据分析给出预热建议
            Map<String, Double> userDetailHotData = getHotData("userDetail", 100);
            Map<String, Double> userListHotData = getHotData("userList", 50);
            
            recommendations.put("hot_user_details", userDetailHotData);
            recommendations.put("hot_user_lists", userListHotData);
            
            // 分析预热效果
            Map<String, Object> stats = getWarmupStats();
            recommendations.put("warmup_effectiveness", analyzeWarmupEffectiveness(stats));
            
        } catch (Exception e) {
            log.error("生成预热建议失败", e);
            recommendations.put("error", e.getMessage());
        }
        
        return recommendations;
    }
    
    /**
     * 分析预热效果
     */
    private Map<String, Object> analyzeWarmupEffectiveness(Map<String, Object> stats) {
        Map<String, Object> analysis = new HashMap<>();
        
        // 这里可以实现具体的效果分析逻辑
        // 例如：成功率、平均耗时、命中率提升等
        analysis.put("success_rate", "需要实现具体的成功率计算");
        analysis.put("performance_improvement", "需要实现性能提升分析");
        analysis.put("recommendations", "基于数据分析的优化建议");
        
        return analysis;
    }
}
