package com.tinyzk.user.center.service;

import com.tinyzk.user.center.dto.BatchResumeUploadRequestDTO;
import com.tinyzk.user.center.dto.UserDuplicationCheckDTO;
import com.tinyzk.user.center.vo.BatchResumeUploadResultVO;

/**
 * 批量简历解析服务接口
 */
public interface BatchResumeParseService {

    /**
     * 批量解析简历文件
     *
     * @param requestDTO 批量上传请求DTO
     * @return 批量处理结果
     */
    BatchResumeUploadResultVO batchParseResumes(BatchResumeUploadRequestDTO requestDTO);

    /**
     * 检查用户是否重复
     *
     * @param checkDTO 用户去重检查DTO
     * @return 检查结果
     */
    UserDuplicationCheckDTO.CheckResult checkUserDuplication(UserDuplicationCheckDTO checkDTO);
}
