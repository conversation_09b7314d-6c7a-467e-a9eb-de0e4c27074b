package com.tinyzk.user.center.service;

import com.tinyzk.user.center.config.CacheWarmupConfig;
import com.tinyzk.user.center.dto.UserListRequestDTO;
import com.tinyzk.user.center.service.impl.AdminUserServiceImpl;
import com.tinyzk.user.center.mapper.UserBaseMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 缓存预热服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CacheWarmupService {
    
    private final CacheWarmupConfig warmupConfig;
    private final AdminUserServiceImpl adminUserService;
    private final UserBaseMapper userBaseMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    private final CacheWarmupMonitorService monitorService;
    
    /**
     * 执行完整的缓存预热
     */
    @Async("cacheWarmupExecutor")
    public CompletableFuture<Void> executeFullWarmup() {
        if (!warmupConfig.isEnabled()) {
            log.info("缓存预热已禁用，跳过执行");
            return CompletableFuture.completedFuture(null);
        }
        
        log.info("开始执行缓存预热...");
        long startTime = System.currentTimeMillis();
        
        try {
            // 按优先级执行预热
            for (String priority : warmupConfig.getStrategy().getPriorities()) {
                switch (priority) {
                    case "userDetail":
                        warmupUserDetails().get();
                        break;
                    case "userList":
                        warmupUserLists().get();
                        break;
                    case "clientDetails":
                        warmupClientDetails().get();
                        break;
                    case "userMapping":
                        warmupUserMappings().get();
                        break;
                    default:
                        log.warn("未知的预热优先级: {}", priority);
                }
                
                // 预热间隔，避免对系统造成压力
                Thread.sleep(warmupConfig.getResource().getInterval().toMillis());
            }
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("缓存预热完成，耗时: {}ms", duration);
            
            // 记录预热统计
            monitorService.recordWarmupCompletion(duration);
            
        } catch (Exception e) {
            log.error("缓存预热执行失败", e);
            monitorService.recordWarmupFailure(e.getMessage());
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 预热用户详情缓存
     */
    @Async("cacheWarmupExecutor")
    public CompletableFuture<Void> warmupUserDetails() {
        log.info("开始预热用户详情缓存...");
        
        try {
            // 获取最近活跃的用户ID列表
            List<Long> activeUserIds = getActiveUserIds();
            int count = Math.min(activeUserIds.size(), warmupConfig.getStrategy().getUserDetailCount());
            
            log.info("准备预热 {} 个用户详情", count);
            
            // 分批预热，避免一次性加载过多数据
            int batchSize = warmupConfig.getResource().getBatchSize();
            for (int i = 0; i < count; i += batchSize) {
                int endIndex = Math.min(i + batchSize, count);
                List<Long> batchUserIds = activeUserIds.subList(i, endIndex);
                
                // 并行预热这一批用户
                List<CompletableFuture<Void>> futures = batchUserIds.stream()
                    .map(this::warmupSingleUserDetail)
                    .toList();
                
                // 等待这一批完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
                
                log.info("已预热用户详情: {}/{}", endIndex, count);
                
                // 批次间隔
                if (endIndex < count) {
                    Thread.sleep(warmupConfig.getResource().getInterval().toMillis());
                }
            }
            
            log.info("用户详情缓存预热完成");
            
        } catch (Exception e) {
            log.error("用户详情缓存预热失败", e);
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 预热用户列表缓存
     */
    @Async("cacheWarmupExecutor")
    public CompletableFuture<Void> warmupUserLists() {
        log.info("开始预热用户列表缓存...");
        
        try {
            int pages = warmupConfig.getStrategy().getUserListPages();
            int pageSize = warmupConfig.getStrategy().getPageSize();
            
            // 预热常见的用户列表查询
            for (int page = 1; page <= pages; page++) {
                UserListRequestDTO requestDTO = new UserListRequestDTO();
                requestDTO.setPageNumber(page);
                requestDTO.setPageSize(pageSize);
                
                // 预热默认排序的用户列表
                adminUserService.getUserList(requestDTO);
                
                // 预热按注册时间排序的用户列表
                requestDTO.setSortBy("created_at");
                requestDTO.setSortOrder("desc");
                adminUserService.getUserList(requestDTO);
                
                log.info("已预热用户列表第 {} 页", page);
                
                // 页面间隔
                Thread.sleep(warmupConfig.getResource().getInterval().toMillis());
            }
            
            log.info("用户列表缓存预热完成");
            
        } catch (Exception e) {
            log.error("用户列表缓存预热失败", e);
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 预热单个用户详情
     */
    public CompletableFuture<Void> warmupSingleUserDetail(Long userId) {
        return CompletableFuture.runAsync(() -> {
            try {
                adminUserService.getUserDetail(userId);
                monitorService.recordWarmupSuccess("userDetail", userId.toString());
            } catch (Exception e) {
                log.warn("预热用户详情失败: userId={}, error={}", userId, e.getMessage());
                monitorService.recordWarmupFailure("userDetail", userId.toString(), e.getMessage());
            }
        });
    }
    
    /**
     * 预热客户端详情缓存
     */
    @Async("cacheWarmupExecutor")
    public CompletableFuture<Void> warmupClientDetails() {
        log.info("开始预热客户端详情缓存...");
        
        try {
            // 这里可以根据实际业务需求实现客户端详情预热
            // 例如预热所有活跃的客户端信息
            
            log.info("客户端详情缓存预热完成");
            
        } catch (Exception e) {
            log.error("客户端详情缓存预热失败", e);
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 预热用户映射关系缓存
     */
    @Async("cacheWarmupExecutor")
    public CompletableFuture<Void> warmupUserMappings() {
        log.info("开始预热用户映射关系缓存...");
        
        try {
            // 这里可以根据实际业务需求实现用户映射关系预热
            
            log.info("用户映射关系缓存预热完成");
            
        } catch (Exception e) {
            log.error("用户映射关系缓存预热失败", e);
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 获取最近活跃的用户ID列表
     * 智能获取策略：优先活跃用户 -> 最近注册用户 -> 所有正常用户
     */
    private List<Long> getActiveUserIds() {
        int targetCount = warmupConfig.getStrategy().getUserDetailCount();
        LocalDateTime since = LocalDateTime.now().minusDays(warmupConfig.getStrategy().getHotDataDays());

        try {
            // 1. 优先获取最近活跃的用户（有登录记录）
            List<Long> activeUserIds = userBaseMapper.selectActiveUserIds(since, targetCount);

            if (activeUserIds.size() >= targetCount) {
                log.info("获取到 {} 个活跃用户ID", activeUserIds.size());
                return activeUserIds.subList(0, targetCount);
            }

            // 2. 如果活跃用户不够，补充最近注册的用户
            int remainingCount = targetCount - activeUserIds.size();
            List<Long> recentUserIds = userBaseMapper.selectRecentUserIds(since, remainingCount);

            // 去重合并
            activeUserIds.addAll(recentUserIds.stream()
                .filter(id -> !activeUserIds.contains(id))
                .limit(remainingCount)
                .toList());

            if (activeUserIds.size() >= targetCount) {
                log.info("获取到 {} 个用户ID（活跃+最近注册）", activeUserIds.size());
                return activeUserIds.subList(0, targetCount);
            }

            // 3. 如果还不够，获取所有正常状态的用户作为兜底
            int finalRemainingCount = targetCount - activeUserIds.size();
            List<Long> normalUserIds = userBaseMapper.selectNormalUserIds(finalRemainingCount);

            activeUserIds.addAll(normalUserIds.stream()
                .filter(id -> !activeUserIds.contains(id))
                .limit(finalRemainingCount)
                .toList());

            log.info("最终获取到 {} 个用户ID用于预热", activeUserIds.size());
            return activeUserIds;

        } catch (Exception e) {
            log.error("获取活跃用户ID列表失败，使用兜底策略", e);

            // 兜底策略：直接获取最近的正常用户
            List<Long> fallbackUserIds = userBaseMapper.selectNormalUserIds(targetCount);
            log.info("兜底策略获取到 {} 个用户ID", fallbackUserIds.size());
            return fallbackUserIds;
        }
    }
    
    /**
     * 检查缓存是否即将过期
     */
    public boolean isCacheNearExpiry(String cacheKey) {
        try {
            Long ttl = redisTemplate.getExpire(cacheKey, TimeUnit.SECONDS);
            if (ttl == null || ttl <= 0) {
                return true; // 缓存不存在或已过期
            }
            
            long beforeExpireSeconds = warmupConfig.getSchedule().getBeforeExpire().getSeconds();
            return ttl <= beforeExpireSeconds;
            
        } catch (Exception e) {
            log.warn("检查缓存过期时间失败: key={}", cacheKey, e);
            return true; // 出错时认为需要预热
        }
    }
}
