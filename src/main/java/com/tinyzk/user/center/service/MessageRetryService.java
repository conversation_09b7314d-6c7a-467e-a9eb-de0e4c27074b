package com.tinyzk.user.center.service;

import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryConfig;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.function.Supplier;

/**
 * 消息重试策略服务
 */
@Service
@Slf4j
public class MessageRetryService {

    private final MeterRegistry meterRegistry;
    private final DeadLetterQueueService deadLetterQueueService;

    // 不同类型的重试策略
    private final Retry defaultRetry;
    private final Retry apiCallRetry;
    private final Retry databaseRetry;

    public MessageRetryService(MeterRegistry meterRegistry,
                             DeadLetterQueueService deadLetterQueueService) {
        this.meterRegistry = meterRegistry;
        this.deadLetterQueueService = deadLetterQueueService;

        // 默认重试策略
        this.defaultRetry = Retry.of("default-retry",
            RetryConfig.custom()
                .maxAttempts(3)
                .waitDuration(Duration.ofSeconds(2))
                .exponentialBackoffMultiplier(2.0)
                .retryOnException(this::isRetryableException)
                .build());

        // API调用重试策略
        this.apiCallRetry = Retry.of("api-call-retry",
            RetryConfig.custom()
                .maxAttempts(5)
                .waitDuration(Duration.ofSeconds(1))
                .exponentialBackoffMultiplier(1.5)
                .retryOnException(this::isApiRetryableException)
                .build());

        // 数据库操作重试策略
        this.databaseRetry = Retry.of("database-retry",
            RetryConfig.custom()
                .maxAttempts(2)
                .waitDuration(Duration.ofMillis(500))
                .retryOnException(this::isDatabaseRetryableException)
                .build());

        // 注册重试指标
        registerRetryMetrics();
    }

    /**
     * 执行带重试的操作（默认策略）
     */
    public <T> T executeWithRetry(Supplier<T> operation, String operationName) {
        return executeWithRetry(operation, operationName, defaultRetry);
    }

    /**
     * 执行带重试的API调用
     */
    public <T> T executeApiCallWithRetry(Supplier<T> operation, String operationName) {
        return executeWithRetry(operation, operationName, apiCallRetry);
    }

    /**
     * 执行带重试的数据库操作
     */
    public <T> T executeDatabaseOperationWithRetry(Supplier<T> operation, String operationName) {
        return executeWithRetry(operation, operationName, databaseRetry);
    }

    /**
     * 执行带重试的操作（通用方法）
     */
    private <T> T executeWithRetry(Supplier<T> operation, String operationName, Retry retry) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            return retry.executeSupplier(operation);
        } catch (Exception e) {
            log.error("操作重试失败: {}", operationName, e);
            meterRegistry.counter("retry.operation.failed", "operation", operationName).increment();
            throw e;
        } finally {
            sample.stop(Timer.builder("retry.operation.duration")
                .tag("operation", operationName)
                .register(meterRegistry));
        }
    }

    /**
     * 执行带重试和死信队列的消息处理
     */
    public <T> T executeMessageProcessingWithRetry(Supplier<T> operation, 
                                                  String operationName,
                                                  String topic,
                                                  Object originalMessage) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            return defaultRetry.executeSupplier(operation);
        } catch (Exception e) {
            log.error("消息处理重试失败，发送到死信队列: operation={}, topic={}", operationName, topic, e);
            
            // 发送到死信队列
            deadLetterQueueService.sendToDeadLetterQueue(
                topic, 
                originalMessage, 
                e.getMessage(), 
                defaultRetry.getRetryConfig().getMaxAttempts()
            );
            
            meterRegistry.counter("retry.message.processing.failed", 
                "operation", operationName, "topic", topic).increment();
            
            throw e;
        } finally {
            sample.stop(Timer.builder("retry.message.processing.duration")
                .tag("operation", operationName)
                .tag("topic", topic)
                .register(meterRegistry));
        }
    }

    /**
     * 判断是否为可重试异常（默认）
     */
    private boolean isRetryableException(Throwable throwable) {
        return throwable instanceof java.net.SocketTimeoutException ||
               throwable instanceof java.net.ConnectException ||
               throwable instanceof java.util.concurrent.TimeoutException ||
               (throwable instanceof RuntimeException && 
                throwable.getMessage() != null && 
                throwable.getMessage().contains("temporary"));
    }

    /**
     * 判断是否为API可重试异常
     */
    private boolean isApiRetryableException(Throwable throwable) {
        if (isRetryableException(throwable)) {
            return true;
        }
        
        // HTTP 5xx 错误可重试
        if (throwable instanceof org.springframework.web.client.HttpServerErrorException) {
            return true;
        }
        
        // 熔断器异常可重试
        if (throwable instanceof io.github.resilience4j.circuitbreaker.CallNotPermittedException) {
            return true;
        }
        
        return false;
    }

    /**
     * 判断是否为数据库可重试异常
     */
    private boolean isDatabaseRetryableException(Throwable throwable) {
        return throwable instanceof org.springframework.dao.DataAccessException ||
               throwable instanceof java.sql.SQLException ||
               (throwable instanceof RuntimeException && 
                throwable.getMessage() != null && 
                (throwable.getMessage().contains("connection") || 
                 throwable.getMessage().contains("timeout")));
    }

    /**
     * 注册重试指标
     */
    private void registerRetryMetrics() {
        // 默认重试指标
        defaultRetry.getEventPublisher().onRetry(event -> {
            meterRegistry.counter("retry.attempt", 
                "retry_name", "default",
                "attempt", String.valueOf(event.getNumberOfRetryAttempts())).increment();
        });

        defaultRetry.getEventPublisher().onSuccess(event -> {
            meterRegistry.counter("retry.success", "retry_name", "default").increment();
        });

        defaultRetry.getEventPublisher().onError(event -> {
            meterRegistry.counter("retry.error", "retry_name", "default").increment();
        });

        // API调用重试指标
        apiCallRetry.getEventPublisher().onRetry(event -> {
            meterRegistry.counter("retry.attempt", 
                "retry_name", "api-call",
                "attempt", String.valueOf(event.getNumberOfRetryAttempts())).increment();
        });

        apiCallRetry.getEventPublisher().onSuccess(event -> {
            meterRegistry.counter("retry.success", "retry_name", "api-call").increment();
        });

        apiCallRetry.getEventPublisher().onError(event -> {
            meterRegistry.counter("retry.error", "retry_name", "api-call").increment();
        });

        // 数据库重试指标
        databaseRetry.getEventPublisher().onRetry(event -> {
            meterRegistry.counter("retry.attempt", 
                "retry_name", "database",
                "attempt", String.valueOf(event.getNumberOfRetryAttempts())).increment();
        });

        databaseRetry.getEventPublisher().onSuccess(event -> {
            meterRegistry.counter("retry.success", "retry_name", "database").increment();
        });

        databaseRetry.getEventPublisher().onError(event -> {
            meterRegistry.counter("retry.error", "retry_name", "database").increment();
        });

        log.info("重试策略指标注册完成");
    }

    /**
     * 获取重试统计信息
     */
    public RetryStatistics getRetryStatistics() {
        RetryStatistics stats = new RetryStatistics();
        
        // 这里可以从指标注册表中获取统计信息
        stats.setDefaultRetryAttempts(getCounterValue("retry.attempt", "retry_name", "default"));
        stats.setDefaultRetrySuccesses(getCounterValue("retry.success", "retry_name", "default"));
        stats.setDefaultRetryErrors(getCounterValue("retry.error", "retry_name", "default"));
        
        stats.setApiRetryAttempts(getCounterValue("retry.attempt", "retry_name", "api-call"));
        stats.setApiRetrySuccesses(getCounterValue("retry.success", "retry_name", "api-call"));
        stats.setApiRetryErrors(getCounterValue("retry.error", "retry_name", "api-call"));
        
        stats.setDatabaseRetryAttempts(getCounterValue("retry.attempt", "retry_name", "database"));
        stats.setDatabaseRetrySuccesses(getCounterValue("retry.success", "retry_name", "database"));
        stats.setDatabaseRetryErrors(getCounterValue("retry.error", "retry_name", "database"));
        
        return stats;
    }

    private double getCounterValue(String name, String tagKey, String tagValue) {
        return meterRegistry.find(name).tag(tagKey, tagValue).counter() != null ?
            meterRegistry.find(name).tag(tagKey, tagValue).counter().count() : 0.0;
    }

    /**
     * 重试统计信息
     */
    public static class RetryStatistics {
        private double defaultRetryAttempts;
        private double defaultRetrySuccesses;
        private double defaultRetryErrors;
        private double apiRetryAttempts;
        private double apiRetrySuccesses;
        private double apiRetryErrors;
        private double databaseRetryAttempts;
        private double databaseRetrySuccesses;
        private double databaseRetryErrors;

        // Getters and Setters
        public double getDefaultRetryAttempts() { return defaultRetryAttempts; }
        public void setDefaultRetryAttempts(double defaultRetryAttempts) { this.defaultRetryAttempts = defaultRetryAttempts; }
        public double getDefaultRetrySuccesses() { return defaultRetrySuccesses; }
        public void setDefaultRetrySuccesses(double defaultRetrySuccesses) { this.defaultRetrySuccesses = defaultRetrySuccesses; }
        public double getDefaultRetryErrors() { return defaultRetryErrors; }
        public void setDefaultRetryErrors(double defaultRetryErrors) { this.defaultRetryErrors = defaultRetryErrors; }
        public double getApiRetryAttempts() { return apiRetryAttempts; }
        public void setApiRetryAttempts(double apiRetryAttempts) { this.apiRetryAttempts = apiRetryAttempts; }
        public double getApiRetrySuccesses() { return apiRetrySuccesses; }
        public void setApiRetrySuccesses(double apiRetrySuccesses) { this.apiRetrySuccesses = apiRetrySuccesses; }
        public double getApiRetryErrors() { return apiRetryErrors; }
        public void setApiRetryErrors(double apiRetryErrors) { this.apiRetryErrors = apiRetryErrors; }
        public double getDatabaseRetryAttempts() { return databaseRetryAttempts; }
        public void setDatabaseRetryAttempts(double databaseRetryAttempts) { this.databaseRetryAttempts = databaseRetryAttempts; }
        public double getDatabaseRetrySuccesses() { return databaseRetrySuccesses; }
        public void setDatabaseRetrySuccesses(double databaseRetrySuccesses) { this.databaseRetrySuccesses = databaseRetrySuccesses; }
        public double getDatabaseRetryErrors() { return databaseRetryErrors; }
        public void setDatabaseRetryErrors(double databaseRetryErrors) { this.databaseRetryErrors = databaseRetryErrors; }
    }
}
