package com.tinyzk.user.center.listener;

import com.tinyzk.user.center.config.CacheWarmupConfig;
import com.tinyzk.user.center.service.CacheWarmupService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 应用启动缓存预热监听器
 * 在应用完全启动后执行缓存预热
 */
@Slf4j
@Component
@Order(1000) // 确保在其他初始化完成后执行
@RequiredArgsConstructor
public class CacheWarmupApplicationListener implements ApplicationListener<ApplicationReadyEvent> {
    
    private final CacheWarmupConfig warmupConfig;
    private final CacheWarmupService cacheWarmupService;
    
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        if (!warmupConfig.isEnabled() || !warmupConfig.isStartupWarmup()) {
            log.info("应用启动缓存预热已禁用");
            return;
        }
        
        log.info("应用启动完成，开始执行缓存预热...");
        
        try {
            // 异步执行预热，不阻塞应用启动
            cacheWarmupService.executeFullWarmup()
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.error("应用启动缓存预热失败", throwable);
                    } else {
                        log.info("应用启动缓存预热完成");
                    }
                });
                
        } catch (Exception e) {
            log.error("启动缓存预热时发生异常", e);
        }
    }
}
