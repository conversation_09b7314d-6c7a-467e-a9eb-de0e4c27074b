package com.tinyzk.user.center.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import java.util.Arrays;

public enum HealthStatus implements IEnum<Integer> {
    /**
     * 1-健康,2-良好,3-一般,4-较差
     */
    HEALTHY(1),
    GOOD(2),
    AVERAGE(3),
    POOR(4);

    @EnumValue
    private final Integer value;

    HealthStatus(Integer value) {
        this.value = value;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    public static HealthStatus getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        return Arrays.stream(HealthStatus.values())
                .filter(status -> status.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
}
