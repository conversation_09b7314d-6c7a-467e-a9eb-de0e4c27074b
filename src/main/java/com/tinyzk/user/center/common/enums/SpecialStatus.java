package com.tinyzk.user.center.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * 特殊身份枚举
 */
public enum SpecialStatus implements IEnum<Integer> {
    /**
     * 1-军人,2-警察,3-医生,4-教师,5-残疾人,6-其他
     */
    SOLDIER(1),
    POLICE(2),
    DOCTOR(3),
    TEACHER(4),
    DISABLED(5),
    OTHER(6);

    @EnumValue
    private final int value;

    SpecialStatus(int value) {
        this.value = value;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }

    /**
     * 根据值获取对应的枚举
     */
    public static SpecialStatus getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (SpecialStatus status : values()) {
            if (status.value == value) {
                return status;
            }
        }
        return null;
    }
}
