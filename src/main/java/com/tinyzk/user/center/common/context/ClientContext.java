package com.tinyzk.user.center.common.context;

import com.tinyzk.user.center.entity.OAuthClientDetails;
import lombok.Data;

/**
 * 客户端上下文信息
 */
@Data
public class ClientContext {
    
    /**
     * 当前客户端ID
     */
    private String clientId;
    
    /**
     * 客户端详情
     */
    private OAuthClientDetails clientDetails;
    
    /**
     * 是否为匿名客户端
     */
    private boolean anonymous;
    
    /**
     * 线程本地存储
     */
    private static final ThreadLocal<ClientContext> CONTEXT = new ThreadLocal<>();
    
    /**
     * 设置当前线程的客户端上下文
     */
    public static void setCurrentContext(ClientContext context) {
        CONTEXT.set(context);
    }
    
    /**
     * 获取当前线程的客户端上下文
     */
    public static ClientContext getCurrentContext() {
        return CONTEXT.get();
    }
    
    /**
     * 获取当前客户端ID
     */
    public static String getCurrentClientId() {
        ClientContext context = getCurrentContext();
        return context != null ? context.getClientId() : "anonymous";
    }
    
    /**
     * 获取当前客户端详情
     */
    public static OAuthClientDetails getCurrentClientDetails() {
        ClientContext context = getCurrentContext();
        return context != null ? context.getClientDetails() : null;
    }
    
    /**
     * 检查当前是否为匿名客户端
     */
    public static boolean isAnonymous() {
        ClientContext context = getCurrentContext();
        return context == null || context.anonymous;
    }
    
    /**
     * 清除当前线程的客户端上下文
     */
    public static void clearContext() {
        CONTEXT.remove();
    }
} 