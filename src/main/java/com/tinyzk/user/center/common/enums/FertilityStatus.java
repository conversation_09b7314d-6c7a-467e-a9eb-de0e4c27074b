package com.tinyzk.user.center.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import java.util.Arrays;

public enum FertilityStatus implements IEnum<Integer>{
    /**
     * 1-未育,2-已育,3-已育一孩,4-已育两孩及以上
     */
    UNBORN(1),
    BORN(2),
    BORN_ONE(3),
    BORN_TWO_OR_MORE(4);

    @EnumValue
    private final Integer value;

    FertilityStatus(Integer value) {
        this.value = value;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    public static FertilityStatus getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        return Arrays.stream(FertilityStatus.values())
                .filter(status -> status.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
}
