package com.tinyzk.user.center.common.annotation;

import com.tinyzk.user.center.common.enums.OperationType;

import java.lang.annotation.*;

/**
 * 用户审计日志注解
 * 用于标记需要记录审计日志的方法
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface UserAudit {
    
    /**
     * 操作类型
     */
    OperationType type();
    
    /**
     * 操作失败时的类型
     * 默认为 LOGIN_SUCCESS，表示使用默认推断规则
     */
    OperationType failType() default OperationType.LOGIN_SUCCESS;
    
    /**
     * 操作详情
     */
    String detail() default "";
    
    /**
     * 用户ID参数索引
     * 默认为-1，表示不从参数中获取
     */
    int userIdIndex() default -1;
    
    /**
     * 用户ID参数名
     * 默认为空字符串，表示不从参数名获取
     */
    String userIdName() default "";
}
