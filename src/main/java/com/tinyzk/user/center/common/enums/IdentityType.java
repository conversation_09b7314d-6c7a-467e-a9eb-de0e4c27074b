package com.tinyzk.user.center.common.enums;

/**
 * 身份认证类型枚举
 */
public enum IdentityType {
    
    /**
     * 微信小程序
     */
    WECHAT_MP("微信小程序"),
    
    /**
     * 支付宝小程序
     */
    ALIPAY_MP("支付宝小程序"),
    
    /**
     * 抖音小程序
     */
    DOUYIN_MP("抖音小程序"),
    
    /**
     * 手机号
     */
    PHONE("手机号"),
    
    /**
     * 邮箱
     */
    EMAIL("邮箱"),
    
    /**
     * 用户名
     */
    USERNAME("用户名");
    
    private final String desc;
    
    IdentityType(String desc) {
        this.desc = desc;
    }
    
    public String getDesc() {
        return desc;
    }
}
