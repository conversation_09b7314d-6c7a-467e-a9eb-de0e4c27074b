package com.tinyzk.user.center.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 用户状态枚举
 */
@Getter
@RequiredArgsConstructor
public enum UserStatus {
    
    /**
     * 已禁用
     */
    DISABLED(0, "已禁用"),
    
    /**
     * 正常
     */
    NORMAL(1, "正常"),
    
    /**
     * 已锁定
     */
    LOCKED(2, "已锁定"),
    
    /**
     * 已合并
     */
    MERGED(3, "已合并"),
    
    /**
     * 已注销
     */
    CANCELED(9, "已注销");
    
    /**
     * 状态码
     */
    private final Integer code;
    
    /**
     * 状态描述
     */
    private final String description;
    
    /**
     * 根据状态码获取枚举值
     *
     * @param code 状态码
     * @return 对应的枚举值
     */
    public static UserStatus getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (UserStatus status : UserStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        
        return null;
    }
}
