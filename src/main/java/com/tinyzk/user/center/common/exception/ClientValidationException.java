package com.tinyzk.user.center.common.exception;

/**
 * 客户端验证异常
 * 用于客户端身份验证失败时抛出的异常
 */
public class ClientValidationException extends BusinessException {
    
    public ClientValidationException(String message) {
        super(ErrorCode.FORBIDDEN, message);
    }
    
    public ClientValidationException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }
    
    /**
     * 客户端ID缺失异常
     */
    public static ClientValidationException missingClientId() {
        return new ClientValidationException("缺少客户端标识，请在请求头中添加 X-Client-ID 参数");
    }
    
    /**
     * 客户端ID无效异常
     */
    public static ClientValidationException invalidClientId(String clientId) {
        return new ClientValidationException("无效的客户端标识: " + clientId);
    }
    
    /**
     * 客户端已禁用异常
     */
    public static ClientValidationException clientDisabled(String clientId) {
        return new ClientValidationException("客户端已被禁用: " + clientId);
    }
    
    /**
     * 匿名客户端权限不足异常
     */
    public static ClientValidationException anonymousClientForbidden(String operation) {
        return new ClientValidationException("匿名客户端无权执行此操作: " + operation + "，请提供有效的客户端标识");
    }
} 