package com.tinyzk.user.center.common.exception;

/**
 * 文件上传异常
 */
public class FileUploadException extends BusinessException {

    public FileUploadException(String message) {
        super(message);
    }

    public FileUploadException(Integer code, String message) {
        super(code, message);
    }

    public FileUploadException(ErrorCode errorCode) {
        super(errorCode);
    }

    public FileUploadException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }
}
