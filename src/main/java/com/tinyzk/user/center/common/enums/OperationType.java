package com.tinyzk.user.center.common.enums;

/**
 * 操作类型枚举
 */
public enum OperationType {

    /**
     * 登录成功
     */
    LOGIN_SUCCESS("登录成功"),

    /**
     * 登录失败
     */
    LOGIN_FAIL("登录失败"),

    /**
     * 注册成功
     */
    REGISTER_SUCCESS("注册成功"),

    /**
     * 注册失败
     */
    REGISTER_FAIL("注册失败"),

    /**
     * 登出成功
     */
    LOGOUT_SUCCESS("登出成功"),

    /**
     * 实名认证提交
     */
    REAL_NAME_AUTH_SUBMIT("实名认证提交"),

    /**
     * 实名认证成功
     */
    REAL_NAME_AUTH_SUCCESS("实名认证成功"),

    /**
     * 实名认证失败
     */
    REAL_NAME_AUTH_FAIL("实名认证失败"),

    /**
     * 账户合并开始
     */
    ACCOUNT_MERGE_START("账户合并开始"),

    /**
     * 账户合并认证更新
     */
    ACCOUNT_MERGE_AUTH_UPDATE("账户合并认证更新"),

    /**
     * 账户合并状态变更
     */
    ACCOUNT_MERGE_STATUS_CHANGE("账户合并状态变更"),

    /**
     * 账户合并完成
     */
    ACCOUNT_MERGE_COMPLETE("账户合并完成"),

    /**
     * 个人资料创建
     */
    PROFILE_CREATE("个人资料创建"),

    /**
     * 个人资料创建失败
     */
    PROFILE_CREATE_FAIL("个人资料创建失败"),

    /**
     * 个人资料更新
     */
    PROFILE_UPDATE("个人资料更新"),

    /**
     * 个人资料删除
     */
    PROFILE_DELETE("个人资料删除"),

    /**
     * 个人资料删除失败
     */
    PROFILE_DELETE_FAIL("个人资料删除失败"),

    /**
     * 认证方式移除
     */
    AUTH_METHOD_REMOVE("认证方式移除"),

    /**
     * 账户注销请求
     */
    ACCOUNT_DELETE_REQUEST("账户注销请求"),

    /**
     * 账户已注销
     */
    ACCOUNT_DELETED("账户已注销"),

    /**
     * 业务错误
     */
    BUSINESS_ERROR("业务错误"),

    /**
     * 提交实名认证
     */
    REALNAME_AUTH_SUBMIT(300, "提交实名认证"),

    /**
     * 实名认证成功
     */
    REALNAME_AUTH_SUCCESS(301, "实名认证成功"),

    /**
     * 实名认证失败
     */
    REALNAME_AUTH_FAIL(302, "实名认证失败"),

    /**
     * 查询实名认证
     */
    REALNAME_AUTH_QUERY(303, "查询实名认证"),

    /**
     * 更新资料成功
     */
    PROFILE_UPDATE_SUCCESS(200, "更新资料成功"),

    /**
     * 更新资料失败
     */
    PROFILE_UPDATE_FAIL(201, "更新资料失败"),

    /**
     * 实名合并-AUTH记录合并
     */
    ACCOUNT_MERGE_AUTH_TRANSFER(400, "实名合并-AUTH记录合并"),

    /**
     * 实名合并-用户资料合并
     */
    ACCOUNT_MERGE_PROFILE_UPDATE(401, "实名合并-用户资料合并"), 
    /**
     * 创建用户教育经历
     */
    EDUCATION_CREATE(501, "创建用户教育经历"),

    /**
     * 创建用户教育经历失败
     */
    EDUCATION_CREATE_FAIL(502, "创建用户教育经历失败"),

    /**
     * 更新用户教育经历
     */
    EDUCATION_UPDATE(502, "更新用户教育经历"),

    /**
     * 更新用户教育经历失败
     */
    EDUCATION_UPDATE_FAIL(503, "更新用户教育经历失败"),

    /**
     * 删除用户教育经历
     */
    EDUCATION_DELETE(504, "删除用户教育经历"),

    /**
     * 删除用户教育经历失败
     */
    EDUCATION_DELETE_FAIL(505, "删除用户教育经历失败"),

    /**
     * 创建用户兼职经历
     */
    PART_TIME_CREATE(506, "创建用户兼职经历"),

    /**
     * 创建用户兼职经历失败
     */
    PART_TIME_CREATE_FAIL(507, "创建用户兼职经历失败"),

    /**
     * 更新用户兼职经历
     */
    PART_TIME_UPDATE(508, "更新用户兼职经历"),

    /**
     * 更新用户兼职经历失败
     */
    PART_TIME_UPDATE_FAIL(509, "更新用户兼职经历失败"),
    
    /**
     * 删除用户兼职经历
     */
    PART_TIME_DELETE(510, "删除用户兼职经历"),

    /**
     * 删除用户兼职经历失败
     */
    PART_TIME_DELETE_FAIL(511, "删除用户兼职经历失败"),

    /**
     * 创建用户项目经历
     */
    PROJECT_CREATE(512, "创建用户项目经历"),

    /**
     * 创建用户项目经历失败
     */
    PROJECT_CREATE_FAIL(513, "创建用户项目经历失败"),

    /**
     * 更新用户项目经历
     */
    PROJECT_UPDATE(514, "更新用户项目经历"),

    /**
     * 更新用户项目经历失败
     */
    PROJECT_UPDATE_FAIL(515, "更新用户项目经历失败"),

    /**
     * 删除用户项目经历
     */
    PROJECT_DELETE(516, "删除用户项目经历"),

    /**
     * 删除用户项目经历失败
     */
    PROJECT_DELETE_FAIL(517, "删除用户项目经历失败"),

    /**
     * 创建用户培训经历
     */
    TRAINING_CREATE(518, "创建用户培训经历"),

    /**
     * 创建用户培训经历失败
     */
    TRAINING_CREATE_FAIL(519, "创建用户培训经历失败"),

    /**
     * 更新用户培训经历
     */
    TRAINING_UPDATE(520, "更新用户培训经历"),

    /**
     * 更新用户培训经历失败
     */
    TRAINING_UPDATE_FAIL(521, "更新用户培训经历失败"),

    /**
     * 删除用户培训经历
     */
    TRAINING_DELETE(522, "删除用户培训经历"),

    /**
     * 删除用户培训经历失败
     */
    TRAINING_DELETE_FAIL(523, "删除用户培训经历失败"),

    /**
     * 创建用户工作经历
     */
    WORK_CREATE(524, "创建用户工作经历"),

    /**
     * 创建用户工作经历失败
     */
    WORK_CREATE_FAIL(525, "创建用户工作经历失败"),

    /**
     * 更新用户工作经历
     */
    WORK_UPDATE(526, "更新用户工作经历"),

    /**
     * 更新用户工作经历失败
     */
    WORK_UPDATE_FAIL(527, "更新用户工作经历失败"),

    /**
     * 删除用户工作经历
     */
    WORK_DELETE(528, "删除用户工作经历"),

    /**
     * 删除用户工作经历失败
     */
    WORK_DELETE_FAIL(529, "删除用户工作经历失败"),

    /**
     * 创建用户联系方式
     */
    CONTACT_CREATE(530, "创建用户联系方式"),

    /**
     * 创建用户联系方式失败
     */
    CONTACT_CREATE_FAIL(531, "创建用户联系方式失败"),

    /**
     * 更新用户联系方式
     */
    CONTACT_UPDATE(532, "更新用户联系方式"),
    /**
     * 更新用户联系方式失败
     */
    CONTACT_UPDATE_FAIL(533, "更新用户联系方式失败"),

    /**
     * 删除用户联系方式
     */
    CONTACT_DELETE(534, "删除用户联系方式"),

    /**
     * 删除用户联系方式失败
     */
    CONTACT_DELETE_FAIL(535, "删除用户联系方式失败");
    private final Integer code;
    private final String description;

    OperationType(String description) {
        this(null, description);
    }

    OperationType(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
