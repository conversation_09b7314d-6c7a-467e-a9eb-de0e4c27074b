package com.tinyzk.user.center.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import java.util.Arrays;

public enum MaritalStatus implements IEnum<Integer> {
    /**
     * 1-未婚,2-已婚,3-离异,4-丧偶
     */
    UNMARRIED(1),
    MARRIED(2),
    DIVORCED(3),
    WIDOWER(4);

    @EnumValue
    private final Integer value;

    MaritalStatus(Integer value) {
        this.value = value;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    public static MaritalStatus getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        return Arrays.stream(MaritalStatus.values())
                .filter(status -> status.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
}
