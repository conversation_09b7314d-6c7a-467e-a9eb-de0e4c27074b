package com.tinyzk.user.center.common.aop;

import com.tinyzk.user.center.common.annotation.DataPermission;
import com.tinyzk.user.center.common.context.ClientContext;
import com.tinyzk.user.center.common.exception.BusinessException;
import com.tinyzk.user.center.common.exception.ErrorCode;
import com.tinyzk.user.center.common.util.AuthUtil;
import com.tinyzk.user.center.entity.OAuthClientDetails;
import com.tinyzk.user.center.service.UserExternalMappingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 数据权限验证切面
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class DataPermissionAspect {
    
    private final UserExternalMappingService userExternalMappingService;
    
    /**
     * 定义切点：所有带有 @DataPermission 注解的方法
     */
    @Pointcut("@annotation(com.tinyzk.user.center.common.annotation.DataPermission)")
    public void dataPermissionPointcut() {
    }
    
    /**
     * 方法执行前验证数据权限
     *
     * @param joinPoint 连接点
     */
    @Before("dataPermissionPointcut()")
    public void checkDataPermission(JoinPoint joinPoint) {
        try {
            // 获取注解信息
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            DataPermission dataPermission = method.getAnnotation(DataPermission.class);
            
            // 获取当前客户端信息
            ClientContext clientContext = ClientContext.getCurrentContext();
            if (clientContext == null) {
                log.warn("无法获取客户端上下文，拒绝访问: method={}", method.getName());
                throw new BusinessException(ErrorCode.FORBIDDEN, "无法验证客户端身份");
            }
            
            String currentClientId = clientContext.getClientId();
            OAuthClientDetails clientDetails = clientContext.getClientDetails();
            
            // 根据模式进行不同的权限验证
            if (dataPermission.mode() == DataPermission.Mode.ADMIN) {
                // 管理端模式：仅验证客户端是否具有管理员权限
                checkAdminPermission(method, currentClientId, clientDetails, dataPermission);
            } else {
                // 用户端模式：验证客户端是否有权限访问当前用户数据
                checkUserPermission(method, currentClientId, dataPermission);
            }
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("数据权限验证异常", e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "权限验证异常");
        }
    }
    
    /**
     * 检查管理端权限
     * 仅验证客户端是否具有管理员权限
     *
     * @param method 方法
     * @param currentClientId 当前客户端ID
     * @param clientDetails 客户端详情
     * @param dataPermission 权限注解
     */
    private void checkAdminPermission(Method method, String currentClientId, 
                                    OAuthClientDetails clientDetails, DataPermission dataPermission) {
        
        if (!isAdminClient(clientDetails)) {
            log.warn("非管理员客户端尝试访问管理接口: clientId={}, method={}", currentClientId, method.getName());
            throw new BusinessException(ErrorCode.FORBIDDEN, "需要管理员权限");
        }
        
        log.debug("管理员客户端权限验证通过: clientId={}, method={}", currentClientId, method.getName());
    }
    
    /**
     * 检查用户端权限
     * 验证客户端是否有权限访问当前用户数据
     *
     * @param method 方法
     * @param currentClientId 当前客户端ID
     * @param dataPermission 权限注解
     */
    private void checkUserPermission(Method method, String currentClientId, DataPermission dataPermission) {
        // 获取当前登录用户ID
        Long currentUserId = AuthUtil.getCurrentUserId();
        if (currentUserId == null) {
            log.warn("无法获取当前用户ID: method={}", method.getName());
            throw new BusinessException(ErrorCode.UNAUTHORIZED, "用户未登录");
        }
        
        // 验证客户端是否有权限访问当前用户数据
        boolean hasAccess = userExternalMappingService.hasDataAccess(currentUserId, currentClientId);
        if (!hasAccess) {
            log.warn("客户端无权限访问用户数据: userId={}, clientId={}, method={}", 
                     currentUserId, currentClientId, method.getName());
            throw new BusinessException(ErrorCode.FORBIDDEN, dataPermission.message());
        }
        
        log.debug("用户端数据权限验证通过: userId={}, clientId={}, method={}", 
                 currentUserId, currentClientId, method.getName());
    }
    
    /**
     * 检查是否为管理员客户端
     *
     * @param clientDetails 客户端详情
     * @return 是否为管理员客户端
     */
    private boolean isAdminClient(OAuthClientDetails clientDetails) {
        if (clientDetails == null) {
            return false;
        }
        
        // 检查客户端是否具有管理员权限
        String authorities = clientDetails.getAuthorities();
        if (authorities != null && authorities.contains("ROLE_ADMIN")) {
            return true;
        }
        
        // 检查客户端范围是否包含管理员权限
        String scope = clientDetails.getScope();
        if (scope != null && scope.contains("admin")) {
            return true;
        }
        
        return false;
    }
} 