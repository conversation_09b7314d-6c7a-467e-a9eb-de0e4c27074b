package com.tinyzk.user.center.common.util;

import com.tinyzk.user.center.config.BatchResumeUploadConfig;
import com.tinyzk.user.center.common.exception.BatchResumeProcessException;
import com.tinyzk.user.center.vo.BatchResumeUploadResultVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;

/**
 * 文件验证工具类
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class FileValidationUtil {

    private final BatchResumeUploadConfig config;

    /**
     * 验证批量上传文件
     */
    public void validateBatchFiles(MultipartFile[] files) {
        if (files == null || files.length == 0) {
            throw new BatchResumeProcessException("文件列表不能为空", 
                BatchResumeUploadResultVO.ErrorType.FILE_VALIDATION);
        }

        // 验证文件数量
        if (files.length > config.getMaxFileCount()) {
            throw new BatchResumeProcessException(
                String.format("文件数量超过限制，最多支持%d个文件", config.getMaxFileCount()),
                BatchResumeUploadResultVO.ErrorType.FILE_VALIDATION);
        }

        // 验证总大小
        long totalSize = Arrays.stream(files).mapToLong(MultipartFile::getSize).sum();
        if (totalSize > config.getMaxTotalSizeBytes()) {
            throw new BatchResumeProcessException(
                String.format("文件总大小超过限制，最大支持%dMB", config.getMaxTotalSizeMb()),
                BatchResumeUploadResultVO.ErrorType.FILE_VALIDATION);
        }

        // 验证每个文件
        for (MultipartFile file : files) {
            validateSingleFile(file);
        }
    }

    /**
     * 验证单个文件
     */
    public void validateSingleFile(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        
        // 验证文件是否为空
        if (file.isEmpty()) {
            throw new BatchResumeProcessException(
                String.format("文件 %s 为空", fileName),
                BatchResumeUploadResultVO.ErrorType.FILE_VALIDATION, fileName);
        }

        // 验证文件大小
        if (config.getEnableFileSizeValidation() && file.getSize() > config.getMaxFileSizeBytes()) {
            throw new BatchResumeProcessException(
                String.format("文件 %s 大小超过限制，最大支持%dMB", fileName, config.getMaxFileSizeMb()),
                BatchResumeUploadResultVO.ErrorType.FILE_VALIDATION, fileName);
        }

        // 验证文件类型
        if (config.getEnableFileTypeValidation()) {
            validateFileType(file);
        }
    }

    /**
     * 验证文件类型
     */
    private void validateFileType(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        String contentType = file.getContentType();

        // 验证文件扩展名
        if (fileName != null) {
            String fileExtension = getFileExtension(fileName).toLowerCase();
            if (!Arrays.asList(config.getSupportedFileTypes()).contains(fileExtension)) {
                throw new BatchResumeProcessException(
                    String.format("文件 %s 类型不支持，支持的类型：%s", 
                        fileName, String.join(", ", config.getSupportedFileTypes())),
                    BatchResumeUploadResultVO.ErrorType.FILE_VALIDATION, fileName);
            }
        }

        // 验证MIME类型
        if (contentType != null && !Arrays.asList(config.getSupportedMimeTypes()).contains(contentType)) {
            throw new BatchResumeProcessException(
                String.format("文件 %s MIME类型不支持：%s", fileName, contentType),
                BatchResumeUploadResultVO.ErrorType.FILE_VALIDATION, fileName);
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        return fileName.substring(lastDotIndex + 1);
    }

    /**
     * 验证文件名安全性
     */
    public void validateFileNameSecurity(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new BatchResumeProcessException("文件名不能为空", 
                BatchResumeUploadResultVO.ErrorType.FILE_VALIDATION);
        }

        // 检查危险字符
        String[] dangerousChars = {"../", "..\\", "<", ">", ":", "\"", "|", "?", "*"};
        for (String dangerousChar : dangerousChars) {
            if (fileName.contains(dangerousChar)) {
                throw new BatchResumeProcessException(
                    String.format("文件名包含非法字符：%s", fileName),
                    BatchResumeUploadResultVO.ErrorType.FILE_VALIDATION, fileName);
            }
        }

        // 检查文件名长度
        if (fileName.length() > 255) {
            throw new BatchResumeProcessException(
                String.format("文件名过长：%s", fileName),
                BatchResumeUploadResultVO.ErrorType.FILE_VALIDATION, fileName);
        }
    }

    /**
     * 验证并发参数
     */
    public void validateConcurrencyParams(Integer maxConcurrency, Integer timeoutSeconds) {
        if (maxConcurrency != null) {
            if (maxConcurrency <= 0 || maxConcurrency > config.getMaxConcurrency()) {
                throw new BatchResumeProcessException(
                    String.format("并发数量参数无效，范围：1-%d", config.getMaxConcurrency()),
                    BatchResumeUploadResultVO.ErrorType.FILE_VALIDATION);
            }
        }

        if (timeoutSeconds != null) {
            if (timeoutSeconds <= 0 || timeoutSeconds > config.getMaxTimeoutSeconds()) {
                throw new BatchResumeProcessException(
                    String.format("超时时间参数无效，范围：1-%d秒", config.getMaxTimeoutSeconds()),
                    BatchResumeUploadResultVO.ErrorType.FILE_VALIDATION);
            }
        }
    }

    /**
     * 检查文件是否可能包含病毒（简单检查）
     */
    public void performBasicVirusScan(MultipartFile file) {
        if (!config.getEnableVirusScan()) {
            return;
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null) {
            return;
        }

        // 简单的文件名检查（实际应用中应该使用专业的病毒扫描引擎）
        String[] suspiciousExtensions = {".exe", ".bat", ".cmd", ".scr", ".pif", ".com"};
        String lowerFileName = fileName.toLowerCase();
        
        for (String ext : suspiciousExtensions) {
            if (lowerFileName.endsWith(ext)) {
                throw new BatchResumeProcessException(
                    String.format("文件 %s 可能包含恶意代码", fileName),
                    BatchResumeUploadResultVO.ErrorType.FILE_VALIDATION, fileName);
            }
        }
    }
}
