package com.tinyzk.user.center.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据权限验证注解
 * 用于在方法级别控制数据访问权限
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DataPermission {
    
    /**
     * 权限验证模式
     */
    enum Mode {
        /**
         * 用户端模式：从token获取当前用户ID，验证客户端是否有权限访问该用户数据
         */
        USER,
        
        /**
         * 管理端模式：从参数获取目标用户ID，仅验证客户端是否具有管理员权限
         */
        ADMIN
    }
    
    /**
     * 权限验证模式，默认为用户端模式
     */
    Mode mode() default Mode.USER;
    
    /**
     * 用户ID参数名称，默认为 "userId"
     * 仅在管理端模式下使用，用于从方法参数中获取需要验证的用户ID
     */
    String userIdParamName() default "userId";
    
    /**
     * 用户ID参数索引，默认为-1表示使用参数名查找
     * 仅在管理端模式下使用，如果指定了索引，则优先使用索引获取用户ID
     */
    int userIdParamIndex() default -1;
    
    /**
     * 验证失败时的错误消息
     */
    String message() default "数据访问权限不足";
} 