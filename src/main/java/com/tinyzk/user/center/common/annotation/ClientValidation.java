package com.tinyzk.user.center.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 客户端身份验证注解
 * 用于验证客户端身份的有效性，不涉及用户数据权限
 * 适用于注册、登录等接口
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ClientValidation {
    
    /**
     * 是否要求客户端必须有效（非匿名）
     * 默认为true，表示必须提供有效的客户端ID
     */
    boolean requireValidClient() default true;
    
    /**
     * 验证失败时的错误消息
     */
    String message() default "无效的客户端身份";
} 