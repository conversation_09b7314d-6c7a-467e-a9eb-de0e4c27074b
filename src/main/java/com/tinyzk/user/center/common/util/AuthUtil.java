package com.tinyzk.user.center.common.util;

import com.tinyzk.user.center.common.exception.BusinessException;
import com.tinyzk.user.center.common.exception.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

@Slf4j
public class AuthUtil {

    /**
     * 获取当前认证用户的ID。
     *
     * @return 当前用户的ID
     * @throws BusinessException 如果用户未认证或无法获取用户ID
     */
    public static Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || !authentication.isAuthenticated() || authentication instanceof AnonymousAuthenticationToken) {
            // 如果身份验证为空、未通过验证或为匿名身份验证，则抛出未授权异常
            throw new BusinessException(ErrorCode.UNAUTHORIZED, "用户未登录或会话无效");
        }

        Object principal = authentication.getPrincipal();
        if (principal instanceof Long) {
            return (Long) principal;
        }

        // 如果程序执行到这里，说明用户已认证，但 Principal 不是预期的 Long 类型。
        // 这可能表示上下文中存在非预期的身份验证类型。
        log.warn("Authenticated user's principal is not of type Long. Principal type: {}, Principal value: {}",
                principal.getClass().getName(), principal.toString());
        throw new BusinessException(ErrorCode.UNAUTHORIZED, "无法识别用户身份类型");
    }

    /**
     * 获取当前认证用户的ID，如果未认证则返回null。
     *
     * @return 当前用户的ID，或null（如果未认证或无法获取）
     */
    public static Long getCurrentUserIdOrNull() {
        try {
            return getCurrentUserId();
        } catch (BusinessException e) {
            // 根据实际需要，也可以记录这里的异常 e.getMessage()
            return null;
        }
    }
} 