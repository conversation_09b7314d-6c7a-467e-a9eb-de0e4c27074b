package com.tinyzk.user.center.common.constants;

/**
 * 通用常量定义
 */
public final class CommonConstants {

    private CommonConstants() {
        // 工具类，禁止实例化
    }

    /**
     * 性别常量
     */
    public static final class Gender {
        public static final Integer UNKNOWN = 0;
        public static final Integer MALE = 1;
        public static final Integer FEMALE = 2;
        
        public static final String MALE_TEXT = "男";
        public static final String FEMALE_TEXT = "女";
    }

    /**
     * 验证状态常量
     */
    public static final class VerificationStatus {
        public static final Integer UNVERIFIED = 0;
        public static final Integer VERIFIED = 1;
    }

    /**
     * 解析状态常量
     */
    public static final class ParseStatus {
        public static final Integer SUCCESS = 2;
        public static final Integer FAILURE = 3;
    }

    /**
     * 数据来源常量
     */
    public static final class DataSource {
        public static final String RESUME = "resume";
        public static final String MANUAL = "manual";
    }

    /**
     * 可见性常量
     */
    public static final class Visibility {
        public static final String PUBLIC = "PUBLIC";
        public static final String PRIVATE = "PRIVATE";
        public static final String FRIENDS = "FRIENDS";
    }

    /**
     * 文件扩展名常量
     */
    public static final class FileExtension {
        public static final String UNKNOWN = "unknown";
        public static final String PDF = "pdf";
        public static final String DOC = "doc";
        public static final String DOCX = "docx";
    }

    /**
     * 解析选项常量
     */
    public static final class ParseOptions {
        public static final String SOURCE = "batch_upload";
        public static final String VERSION = "1.0";
    }

    /**
     * 日期格式常量
     */
    public static final class DatePatterns {
        public static final String[] BIRTHDAY_PATTERNS = {
            "yyyy.MM.dd",    // 1981.09.15
            "yyyy-MM-dd",    // 1981-09-15
            "yyyy/MM/dd",    // 1981/09/15
            "yyyy年MM月dd日", // 1981年09月15日
            "yyyy年M月d日",   // 1981年9月15日
            "yyyy.M.d",      // 1981.9.15
            "yyyy-M-d",      // 1981-9-15
            "yyyy/M/d",      // 1981/9/15
            "yyyyMMdd",      // 19810915
            "MM/dd/yyyy",    // 09/15/1981
            "dd/MM/yyyy",    // 15/09/1981
            "MM-dd-yyyy",    // 09-15-1981
            "dd-MM-yyyy"     // 15-09-1981
        };
    }

    /**
     * 错误消息常量
     */
    public static final class ErrorMessages {
        public static final String CREATE_USER_FAILED = "创建新用户失败";
        public static final String SAVE_PARSE_DATA_FAILED = "保存解析数据失败";
        public static final String SAVE_PARSE_RECORD_FAILED = "保存解析记录失败";
        public static final String PARSE_BIRTHDAY_FAILED = "无法解析生日格式";
        public static final String JSON_CONVERSION_FAILED = "JSON conversion failed";
    }

    /**
     * 日志消息常量
     */
    public static final class LogMessages {
        public static final String USER_CREATED = "创建新用户成功: userId={}";
        public static final String USER_PROFILE_CREATED = "创建用户资料成功: userId={}";
        public static final String USER_PROFILE_UPDATED = "更新用户资料成功: userId={}";
        public static final String PARSE_DATA_SAVED = "保存解析数据到数据库成功: userId={}, parseRecordId={}";
        public static final String PARSE_RECORD_SAVED = "保存解析记录成功: userId={}, recordId={}, thirdPartyId={}";
    }

    /**
     * JSON相关常量
     */
    public static final class Json {
        public static final String EMPTY_OBJECT = "{}";
        public static final String ERROR_OBJECT = "{\"error\":\"JSON conversion failed\"}";
    }
}
