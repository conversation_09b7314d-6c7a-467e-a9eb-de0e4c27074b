package com.tinyzk.user.center.common.enums;

import lombok.Getter;

/**
 * 实名认证状态枚举
 */
@Getter
public enum RealNameAuthStatus {
    
    PENDING(0, "待审核"),
    VERIFIED(1, "已认证"),
    REJECTED(2, "已驳回");
    
    private final Integer code;
    private final String description;
    
    RealNameAuthStatus(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}
