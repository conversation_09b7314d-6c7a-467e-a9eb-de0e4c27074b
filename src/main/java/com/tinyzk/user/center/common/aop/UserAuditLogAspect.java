package com.tinyzk.user.center.common.aop;

import com.tinyzk.user.center.common.annotation.UserAudit;
import com.tinyzk.user.center.common.context.ClientContext;
import com.tinyzk.user.center.common.enums.OperationType;
import com.tinyzk.user.center.common.event.UserAuditEvent;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;

/**
 * 用户审计日志切面
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class UserAuditLogAspect {
    
    private final ApplicationEventPublisher eventPublisher;
    
    /**
     * 定义切点：所有带有 @UserAudit 注解的方法
     */
    @Pointcut("@annotation(com.tinyzk.user.center.common.annotation.UserAudit)")
    public void userAuditPointcut() {
    }
    
    /**
     * 方法执行成功后记录审计日志
     *
     * @param joinPoint 连接点
     * @param result    返回结果
     */
    @AfterReturning(pointcut = "userAuditPointcut()", returning = "result")
    public void afterReturning(JoinPoint joinPoint, Object result) {
        try {
            // 获取注解信息
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            UserAudit userAudit = method.getAnnotation(UserAudit.class);
            
            // 获取用户ID
            Long userId = getUserId(joinPoint, userAudit, result);
            if (userId == null) {
                log.warn("无法获取用户ID，跳过审计日志记录");
                return;
            }
            
            // 获取请求信息
            HttpServletRequest request = getRequest();
            if (request == null) {
                log.warn("无法获取请求信息，跳过审计日志记录");
                return;
            }
            
            // 获取客户端信息
            String currentClientId = ClientContext.getCurrentClientId();
            
            // 发布审计事件
            UserAuditEvent event = UserAuditEvent.builder()
                    .userId(userId)
                    .operationType(userAudit.type())
                    .detail(userAudit.detail())
                    .ipAddress(getIpAddress(request))
                    .userAgent(request.getHeader("User-Agent"))
                    .clientId(currentClientId)  // 添加客户端ID
                    .build();
            
            eventPublisher.publishEvent(event);
            log.debug("记录审计日志成功: userId={}, operationType={}, clientId={}", 
                     userId, userAudit.type(), currentClientId);
        } catch (Exception e) {
            log.error("记录审计日志失败", e);
        }
    }
    
    /**
     * 方法执行异常后记录审计日志
     *
     * @param joinPoint 连接点
     * @param e         异常
     */
    @AfterThrowing(pointcut = "userAuditPointcut()", throwing = "e")
    public void afterThrowing(JoinPoint joinPoint, Exception e) {
        try {
            // 获取注解信息
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            UserAudit userAudit = method.getAnnotation(UserAudit.class);
            
            // 获取用户ID
            Long userId = getUserId(joinPoint, userAudit, null);
            if (userId == null) {
                log.warn("无法获取用户ID，跳过审计日志记录");
                return;
            }
            
            // 获取请求信息
            HttpServletRequest request = getRequest();
            if (request == null) {
                log.warn("无法获取请求信息，跳过审计日志记录");
                return;
            }
            
            // 获取失败操作类型
            OperationType failType = userAudit.failType();
            if (failType == OperationType.LOGIN_SUCCESS) {
                // 如果未指定失败操作类型，则根据成功操作类型推断
                failType = getFailOperationType(userAudit.type());
            }
            
            // 获取客户端信息
            String currentClientId = ClientContext.getCurrentClientId();
            
            // 发布审计事件
            UserAuditEvent event = UserAuditEvent.builder()
                    .userId(userId)
                    .operationType(failType)
                    .detail(e.getMessage())
                    .ipAddress(getIpAddress(request))
                    .userAgent(request.getHeader("User-Agent"))
                    .clientId(currentClientId)  // 添加客户端ID
                    .build();
            
            eventPublisher.publishEvent(event);
            log.debug("记录失败审计日志成功: userId={}, operationType={}, clientId={}", 
                     userId, failType, currentClientId);
        } catch (Exception ex) {
            log.error("记录审计日志失败", ex);
        }
    }
    
    /**
     * 获取用户ID
     *
     * @param joinPoint 连接点
     * @param userAudit 注解
     * @param result 方法返回结果 (可为null)
     * @return 用户ID
     */
    private Long getUserId(JoinPoint joinPoint, UserAudit userAudit, Object result) {
        // 如果指定了用户ID参数索引，则从参数中获取
        if (userAudit.userIdIndex() >= 0) {
            Object[] args = joinPoint.getArgs();
            if (args.length > userAudit.userIdIndex()) {
                Object arg = args[userAudit.userIdIndex()];
                if (arg instanceof Long) {
                    return (Long) arg;
                }
            }
        }
        
        // 如果指定了用户ID参数名，则从参数中获取
        if (!userAudit.userIdName().isEmpty()) {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            String[] parameterNames = signature.getParameterNames();
            Object[] args = joinPoint.getArgs();
            
            for (int i = 0; i < parameterNames.length; i++) {
                if (userAudit.userIdName().equals(parameterNames[i]) && args[i] instanceof Long) {
                    return (Long) args[i];
                }
            }
        }
        
        // 尝试从返回结果中获取用户ID
        if (result != null) {
            Long userId = extractUserIdFromResult(result);
            if (userId != null) {
                return userId;
            }
        }
        
        // 默认返回null
        return null;
    }
    
    /**
     * 从方法返回结果中提取用户ID
     *
     * @param result 方法返回结果
     * @return 用户ID，如果无法提取则返回null
     */
    private Long extractUserIdFromResult(Object result) {
        try {
            // 处理常见的返回类型
            if (result instanceof com.tinyzk.user.center.vo.RegisterVO) {
                return ((com.tinyzk.user.center.vo.RegisterVO) result).getUserId();
            }
            
            if (result instanceof com.tinyzk.user.center.vo.LoginVO) {
                return ((com.tinyzk.user.center.vo.LoginVO) result).getUserId();
            }
            
            // 使用反射尝试获取userId字段
            Class<?> resultClass = result.getClass();
            try {
                java.lang.reflect.Field userIdField = resultClass.getDeclaredField("userId");
                userIdField.setAccessible(true);
                Object userIdValue = userIdField.get(result);
                if (userIdValue instanceof Long) {
                    return (Long) userIdValue;
                }
            } catch (NoSuchFieldException | IllegalAccessException e) {
                // 尝试通过getter方法获取
                try {
                    java.lang.reflect.Method getUserIdMethod = resultClass.getMethod("getUserId");
                    Object userIdValue = getUserIdMethod.invoke(result);
                    if (userIdValue instanceof Long) {
                        return (Long) userIdValue;
                    }
                } catch (NoSuchMethodException | IllegalAccessException | java.lang.reflect.InvocationTargetException ex) {
                    // 忽略，返回null
                }
            }
        } catch (Exception e) {
            log.debug("从返回结果中提取用户ID失败: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 获取请求信息
     *
     * @return HTTP请求
     */
    private HttpServletRequest getRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getRequest() : null;
    }
    
    /**
     * 获取IP地址
     *
     * @param request HTTP请求
     * @return IP地址
     */
    private String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
    
    /**
     * 根据成功操作类型推断失败操作类型
     *
     * @param successType 成功操作类型
     * @return 失败操作类型
     */
    private OperationType getFailOperationType(OperationType successType) {
        switch (successType) {
            case LOGIN_SUCCESS:
                return OperationType.LOGIN_FAIL;
            case REGISTER_SUCCESS:
                return OperationType.REGISTER_FAIL;
            case REAL_NAME_AUTH_SUCCESS:
                return OperationType.REAL_NAME_AUTH_FAIL;
            default:
                return OperationType.valueOf("BUSINESS_ERROR");
        }
    }
}
