package com.tinyzk.user.center.common.exception;

import com.tinyzk.user.center.vo.BatchResumeUploadResultVO;

/**
 * 批量简历处理异常
 */
public class BatchResumeProcessException extends RuntimeException {

    private final BatchResumeUploadResultVO.ErrorType errorType;
    private final String fileName;

    public BatchResumeProcessException(String message, BatchResumeUploadResultVO.ErrorType errorType) {
        super(message);
        this.errorType = errorType;
        this.fileName = null;
    }

    public BatchResumeProcessException(String message, BatchResumeUploadResultVO.ErrorType errorType, String fileName) {
        super(message);
        this.errorType = errorType;
        this.fileName = fileName;
    }

    public BatchResumeProcessException(String message, Throwable cause, BatchResumeUploadResultVO.ErrorType errorType) {
        super(message, cause);
        this.errorType = errorType;
        this.fileName = null;
    }

    public BatchResumeProcessException(String message, Throwable cause, BatchResumeUploadResultVO.ErrorType errorType, String fileName) {
        super(message, cause);
        this.errorType = errorType;
        this.fileName = fileName;
    }

    public BatchResumeUploadResultVO.ErrorType getErrorType() {
        return errorType;
    }

    public String getFileName() {
        return fileName;
    }
}
