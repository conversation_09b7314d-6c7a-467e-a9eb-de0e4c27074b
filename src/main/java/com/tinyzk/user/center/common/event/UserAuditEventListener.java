package com.tinyzk.user.center.common.event;

import com.tinyzk.user.center.entity.UserAuditLog;
import com.tinyzk.user.center.mapper.UserAuditLogMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 用户审计事件监听器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserAuditEventListener {
    
    private final UserAuditLogMapper userAuditLogMapper;
    
    /**
     * 处理用户审计事件
     * 异步处理，不阻塞主流程
     *
     * @param event 用户审计事件
     */
    @Async
    @EventListener
    public void handleUserAuditEvent(UserAuditEvent event) {
        log.debug("接收到用户审计事件: {}", event);
        
        UserAuditLog auditLog = new UserAuditLog();
        auditLog.setUserId(event.getUserId());
        auditLog.setOperationType(event.getOperationType().name());
        auditLog.setOperationDetail(event.getDetail());
        auditLog.setIpAddress(event.getIpAddress());
        auditLog.setUserAgent(event.getUserAgent());
        auditLog.setClientId(event.getClientId());
        
        userAuditLogMapper.insert(auditLog);
        
        log.info("记录用户审计日志成功: userId={}, operationType={}, clientId={}", 
                event.getUserId(), event.getOperationType(), event.getClientId());
    }
}
