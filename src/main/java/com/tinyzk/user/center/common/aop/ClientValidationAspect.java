package com.tinyzk.user.center.common.aop;

import com.tinyzk.user.center.common.annotation.ClientValidation;
import com.tinyzk.user.center.common.context.ClientContext;
import com.tinyzk.user.center.common.exception.ClientValidationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 客户端验证切面
 * 用于验证@ClientValidation注解标记的方法的客户端身份
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class ClientValidationAspect {
    
    /**
     * 定义切点：所有带有 @ClientValidation 注解的方法
     */
    @Pointcut("@annotation(com.tinyzk.user.center.common.annotation.ClientValidation)")
    public void clientValidationPointcut() {
    }
    
    /**
     * 方法执行前验证客户端身份
     *
     * @param joinPoint 连接点
     */
    @Before("clientValidationPointcut()")
    public void validateClient(JoinPoint joinPoint) {
        try {
            // 获取注解信息
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            ClientValidation clientValidation = method.getAnnotation(ClientValidation.class);
            
            // 获取当前客户端上下文
            ClientContext clientContext = ClientContext.getCurrentContext();
            
            if (clientContext == null) {
                log.warn("无法获取客户端上下文: method={}", method.getName());
                throw ClientValidationException.missingClientId();
            }
            
            String clientId = clientContext.getClientId();
            boolean isAnonymous = clientContext.isAnonymous();
            
            // 记录请求信息
            log.debug("客户端验证检查: method={}, clientId={}, anonymous={}, requireValid={}", 
                     method.getName(), clientId, isAnonymous, clientValidation.requireValidClient());
            
            // 如果要求有效客户端，则验证客户端是否匿名
            if (clientValidation.requireValidClient()) {
                if (isAnonymous || "anonymous".equals(clientId)) {
                    log.warn("匿名客户端尝试访问需要验证的接口: method={}, clientId={}", 
                            method.getName(), clientId);
                    throw ClientValidationException.anonymousClientForbidden(method.getName());
                }
                
                // 如果客户端详情为空，说明客户端无效
                if (clientContext.getClientDetails() == null) {
                    log.warn("无效的客户端尝试访问接口: method={}, clientId={}", 
                            method.getName(), clientId);
                    throw ClientValidationException.invalidClientId(clientId);
                }
            }
            
            log.debug("客户端验证通过: clientId={}, method={}", clientId, method.getName());
            
        } catch (ClientValidationException e) {
            throw e;  // 直接重新抛出客户端验证异常
        } catch (Exception e) {
            log.error("客户端验证异常: method={}", 
                     joinPoint.getSignature().getName(), e);
            throw ClientValidationException.missingClientId();  // 统一处理为缺少客户端ID
        }
    }
} 