package com.tinyzk.user.center.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import java.util.Arrays;

public enum PoliticalStatus implements IEnum<Integer> {
    /**
     *1-中共党员, 2-中共预备党员, 3-共青团员, 4-群众
     */
    CPC_MEMBER(1),
    CPC_PREPARED_MEMBER(2),
    CPTU_MEMBER(3),
    PUBLIC(4);

    @EnumValue
    private final Integer value;
    
    PoliticalStatus(Integer value) {
        this.value = value;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    public static PoliticalStatus getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        return Arrays.stream(PoliticalStatus.values())
                .filter(status -> status.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
}
