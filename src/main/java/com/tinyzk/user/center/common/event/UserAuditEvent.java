package com.tinyzk.user.center.common.event;

import com.tinyzk.user.center.common.enums.OperationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户审计事件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAuditEvent {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 操作类型
     */
    private OperationType operationType;
    
    /**
     * 操作详情
     */
    private String detail;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 客户端ID
     */
    private String clientId;
}
