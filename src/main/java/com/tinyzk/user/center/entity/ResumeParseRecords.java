package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 简历解析记录实体类
 */
@Data
@TableName("resume_parse_records")
public class ResumeParseRecords {

    /**
     * 解析记录主键ID
     */
    @TableId(value = "record_id", type = IdType.ASSIGN_ID)
    private Long recordId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 原始文件名
     */
    private String originalFilename;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 文件类型 (doc, docx, pdf)
     */
    private String fileType;

    /**
     * 文件MD5哈希值
     */
    private String fileHash;

    /**
     * 解析状态 (0-待解析, 1-解析中, 2-解析成功, 3-解析失败)
     */
    private Integer parseStatus;

    /**
     * 完整的解析结果JSON
     */
    private String parseResult;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 第三方服务返回的ID
     */
    private String thirdPartyId;

    /**
     * 解析耗时(毫秒)
     */
    private Integer parseDuration;

    /**
     * 记录创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 记录最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 记录删除时间(逻辑删除)
     */
    @TableLogic
    private LocalDateTime deletedAt;
}
