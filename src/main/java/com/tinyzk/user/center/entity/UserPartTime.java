package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户兼职经历实体类
 */
@Data
@TableName("user_part_time_history")
public class UserPartTime {

    /**
     * 主键ID
     */
    @TableId(value = "part_time_id", type = IdType.ASSIGN_ID)
    private Long partTimeId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 兼职名称
     */
    private String partTimeName;

    /**
     * 兼职类型
     * 1-咨询, 2-设计, 3-开发, 4-运营, 5-销售, 6-客服, 7-服务员, 8-其他
     */
    private Integer partTimeType;

    /**
     * 兼职提供者/机构
     */
    private String partTimeProvider;

    /**
     * 兼职地点
     */
    private String partTimeLocation;

    /**
     * 兼职薪资
     */
    private BigDecimal partTimeSalary;

    /**
     * 服务周期（如：1个月, 3个月, 6个月, 1年）
     */
    private String servicePeriod;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期（NULL表示进行中）
     */
    private LocalDate endDate;

    /**
     * 兼职描述
     */
    private String description;

    /**
     * 可见性
     */
    private String visibility;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 删除时间（逻辑删除）
     */
    @TableLogic
    private LocalDateTime deletedAt;
} 