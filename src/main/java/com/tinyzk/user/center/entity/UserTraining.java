package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户培训经历实体类
 */
@Data
@TableName("user_training_history")
public class UserTraining {

    /**
     * 主键ID
     */
    @TableId(value = "training_id", type = IdType.ASSIGN_ID)
    private Long trainingId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 培训名称
     */
    private String trainingName;

    /**
     * 培训类型（如：线上培训、线下培训）
     */
    private String trainingType;

    /**
     * 培训提供者/机构
     */
    private String trainingProvider;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期（NULL表示进行中）
     */
    private LocalDate endDate;

    /**
     * 培训描述
     */
    private String description;

    /**
     * 可见性
     */
    private String visibility;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 删除时间（逻辑删除）
     */
    @TableLogic
    private LocalDateTime deletedAt;
} 