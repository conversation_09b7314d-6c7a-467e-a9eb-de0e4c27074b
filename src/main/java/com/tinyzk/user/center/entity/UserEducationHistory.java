package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户教育经历实体类
 */
@Data
@TableName("user_education_history")
public class UserEducationHistory {

    /**
     * 教育经历主键ID
     */
    @TableId(value = "edu_id", type = IdType.ASSIGN_ID)
    private Long eduId;

    /**
     * 关联的用户ID
     */
    private Long userId;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 学位
     */
    private String degree;

    /**
     * 学位等级
     */
    private Integer degreeLevel;

    /**
     * 专业
     */
    private String major;

    /**
     * 第二专业
     */
    private String secondaryMajor;

    /**
     * 专业方向
     */
    private String majorArea;

    /**
     * 专业GPA
     */
    private Float majorGpa;

    /**
     * 入学日期
     */
    private LocalDate startDate;

    /**
     * 毕业/离校日期(NULL表示在读)
     */
    private LocalDate endDate;

    /**
     * 描述/在校经历/荣誉等
     */
    private String description;

    /**
     * 社团经历
     */
    private String clubExperience;

    /**
     * 可见性(公开,好友可见,私密)
     */
    private String visibility;

    /**
     * 记录创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 记录最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 记录删除时间(逻辑删除)
     */
    @TableLogic
    private LocalDateTime deletedAt;
}