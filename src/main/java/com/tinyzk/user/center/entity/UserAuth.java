package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户认证/登录方式实体类
 */
@Data
@TableName("user_auth")
public class UserAuth {
    
    /**
     * 认证记录主键ID
     */
    @TableId(value = "auth_id", type = IdType.ASSIGN_ID)
    private Long authId;
    
    /**
     * 关联的用户基础ID (逻辑外键)
     */
    private Long userId;
    
    /**
     * 认证/登录类型 (WECHAT_MP, ALIPAY_MP, DOUYIN_MP, PHONE, EMAIL, USERNAME)
     */
    private String identityType;
    
    /**
     * 凭证标识 (OpenID, UserID, 手机号, 用户名等)
     */
    private String identifier;
    
    /**
     * 凭证内容 (密码哈希, Token等, 可为空)
     */
    private String credential;
    
    /**
     * 是否已验证 (1: 已验证, 0: 未验证)
     */
    private Integer verified;
    
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginAt;
    
    /**
     * 记录创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 记录最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    /**
     * 记录删除时间(逻辑删除)
     */
    @TableLogic
    private LocalDateTime deletedAt;
}
