package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.tinyzk.user.center.common.enums.RealNameAuthStatus;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户实名认证记录实体类
 */
@Data
@TableName("user_real_name_auth")
public class UserRealNameAuth {
    
    /**
     * 认证记录主键
     */
    @TableId(value = "auth_record_id", type = IdType.ASSIGN_ID)
    private Long authRecordId;
    
    /**
     * 关联的用户基础ID (逻辑外键, 关联最终认证成功的user_id)
     */
    private Long userId;
    
    /**
     * 提交时使用的真实姓名 (应用层考虑加密)
     */
    private String submittedRealName;
    
    /**
     * 提交时使用的身份证号 (应用层必须加密存储)
     */
    private String submittedIdCardNumber;
    
    /**
     * 认证状态 (0:待审核 PENDING, 1:已认证 VERIFIED, 2:已驳回 REJECTED)
     */
    private Integer authStatus;
    
    /**
     * 认证完成时间
     */
    private LocalDateTime authTime;
    
    /**
     * 认证渠道/方式
     */
    private String authChannel;
    
    /**
     * 失败原因 (如果认证失败)
     */
    private String failReason;
    
    /**
     * 记录创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 记录最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
