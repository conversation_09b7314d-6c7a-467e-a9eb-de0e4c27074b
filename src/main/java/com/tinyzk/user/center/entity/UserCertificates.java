package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户证书实体类
 */
@Data
@TableName("user_certificates")
public class UserCertificates {

    /**
     * 证书主键ID
     */
    @TableId(value = "certificate_id", type = IdType.ASSIGN_ID)
    private Long certificateId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 证书名称
     */
    private String certificateName;

    /**
     * 颁发机构
     */
    private String issuingOrganization;

    /**
     * 证书编号
     */
    private String certificateNumber;

    /**
     * 颁发日期
     */
    private LocalDate issueDate;

    /**
     * 过期日期 (NULL表示永久有效)
     */
    private LocalDate expiryDate;

    /**
     * 证书链接或图片URL
     */
    private String certificateUrl;

    /**
     * 证书描述
     */
    private String description;

    /**
     * 验证状态 (0-未验证, 1-已验证, 2-验证失败)
     */
    private Integer verificationStatus;

    /**
     * 数据来源 (manual-手动添加, resume-简历解析, import-导入)
     */
    private String source;

    /**
     * 可见性 (public-公开, friends-好友可见, private-私密)
     */
    private String visibility;

    /**
     * 记录创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 记录最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 记录删除时间(逻辑删除)
     */
    @TableLogic
    private LocalDateTime deletedAt;
}
