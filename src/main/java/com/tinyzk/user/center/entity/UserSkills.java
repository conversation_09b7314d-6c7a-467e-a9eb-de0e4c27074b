package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户技能实体类
 */
@Data
@TableName("user_skills")
public class UserSkills {

    /**
     * 技能主键ID
     */
    @TableId(value = "skill_id", type = IdType.ASSIGN_ID)
    private Long skillId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 技能名称
     */
    private String skillName;

    /**
     * 技能类型 (1-IT技能, 2-业务技能, 3-语言技能, 4-其他技能)
     */
    private Integer skillType;

    /**
     * 熟练程度 (1-初级, 2-中级, 3-高级, 4-专家)
     */
    private Integer proficiencyLevel;

    /**
     * 使用年限
     */
    private BigDecimal yearsOfExperience;

    /**
     * 技能描述
     */
    private String description;

    /**
     * 数据来源 (manual-手动添加, resume-简历解析, import-导入)
     */
    private String source;

    /**
     * 可见性 (public-公开, friends-好友可见, private-私密)
     */
    private String visibility;

    /**
     * 记录创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 记录最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 记录删除时间(逻辑删除)
     */
    @TableLogic
    private LocalDateTime deletedAt;
}
