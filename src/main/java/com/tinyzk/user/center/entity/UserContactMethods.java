package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户附加联系方式实体类
 */
@Data
@TableName("user_contact_methods")
public class UserContactMethods {

    /**
     * 联系方式主键ID
     */
    @TableId(value = "contact_id", type = IdType.ASSIGN_ID)
    private Long contactId;

    /**
     * 用户ID (逻辑外键, 关联 user_base.user_id)
     */
    private Long userId;

    /**
     * 联系方式类型 (如 1-备用邮箱, 2-工作电话, 3-紧急联系人, 4-微信, 5-QQ、6-即刻、7-小红书、8-微博、9-抖音、10-其他)
     */
    private Integer contactType;

    /**
     * 联系方式的值 (邮箱地址, 电话号码, URL, ID等)
     */
    private String contactValue;

    /**
     * 用户自定义标签 (如: 备用邮箱, 工作电话)
     */
    private String label;

    /**
     * 可见性
     */
    private String visibility;

    /**
     * 是否已验证 (0: 未验证, 1: 已验证)
     */
    private Boolean isVerified;

    /**
     * 验证时间
     */
    private LocalDateTime verifiedAt;

    /**
     * 记录创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 记录更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 逻辑删除时间
     */
    @TableLogic
    private LocalDateTime deletedAt;
}