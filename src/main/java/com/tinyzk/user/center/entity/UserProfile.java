package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.tinyzk.user.center.common.enums.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户个人资料实体类
 */
@Data
@TableName("user_profile")
public class UserProfile {
    
    /**
     * 资料记录主键ID
     */
    @TableId(value = "profile_id", type = IdType.ASSIGN_ID)
    private Long profileId;
    
    /**
     * 关联的用户基础ID (逻辑外键)
     */
    private Long userId;
    
    /**
     * 用户昵称
     */
    private String nickname;
    
    /**
     * 头像URL
     */
    private String avatarUrl;
    
    /**
     * 性别 (0: 未知, 1: 男, 2: 女)
     */
    private Integer gender;
    
    /**
     * 生日
     */
    private LocalDate birthday;
    
    /**
     * 国籍
     */
    private String nationality;
    
    /**
     * 民族
     */
    private String ethnicity;
    
    /**
     * 特殊身份 (1-军人, 2-警察, 3-医生, 4-教师, 5-残疾人, 6-其他)
     */
    private SpecialStatus specialStatus;
    
    /**
     * 政治面貌 (1-中共党员, 2-中共预备党员, 3-共青团员, 4-群众)
     */
    private PoliticalStatus politicalStatus;
    
    /**
     * 婚姻状况 (1-未婚, 2-已婚, 3-离异, 4-丧偶)
     */
    private MaritalStatus maritalStatus;
    
    /**
     * 生育情况 (1-未育, 2-已育, 3-已育一孩, 4-已育两孩及以上)
     */
    private FertilityStatus fertilityStatus;
    
    /**
     * 健康状况 (1-健康, 2-良好, 3-一般, 4-较差)
     */
    private HealthStatus healthStatus;
    
    /**
     * 地区编码
     */
    private String regionCode;
    
    /**
     * 地区名称
     */
    private String regionName;
    
    /**
     * 详细地址
     */
    private String address;
    
    /**
     * 个人简介
     */
    private String bio;
    
    /**
     * 记录创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 记录最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    /**
     * 记录删除时间(逻辑删除)
     */
    @TableLogic
    private LocalDateTime deletedAt;
}
