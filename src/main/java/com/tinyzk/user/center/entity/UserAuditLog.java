package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户操作审计日志实体类
 */
@Data
@TableName("user_audit_log")
public class UserAuditLog {

    /**
     * 日志记录主键ID
     */
    @TableId(value = "log_id", type = IdType.AUTO)
    private Long logId;

    /**
     * 关联的用户基础ID (逻辑外键)
     */
    private Long userId;

    /**
     * 操作类型 (LOGIN_SUCCESS, LOGIN_FAIL, REGISTER_SUCCESS, ACCOUNT_MERGE_START, ACCOUNT_MERGE_COMPLETE等)
     */
    private String operationType;

    /**
     * 操作详情 (JSON格式, 包含操作的详细信息)
     */
    private String operationDetail;

    /**
     * 操作IP地址
     */
    private String ipAddress;

    /**
     * 用户代理信息
     */
    private String userAgent;

    /**
     * 发起请求的客户端ID (逻辑外键关联 oauth_client_details)
     */
    private String clientId;

    /**
     * 记录创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
