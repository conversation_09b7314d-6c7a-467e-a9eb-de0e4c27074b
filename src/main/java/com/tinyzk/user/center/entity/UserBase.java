package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户基础信息实体类
 */
@Data
@TableName("user_base")
public class UserBase {
    
    /**
     * 用户唯一主键ID
     */
    @TableId(value = "user_id", type = IdType.ASSIGN_ID)
    private Long userId;
    
    /**
     * 实名认证状态(0:未认证,1:已认证)
     */
    private Boolean realNameVerified;
    
    /**
     * 真实姓名(认证后填充,脱敏存储或加密)
     */
    private String realName;
    
    /**
     * 身份证号(认证后填充,必须加密存储)
     */
    private String idCardNumber;
    
    /**
     * 用户状态(1:正常,2:禁用,0:注销/逻辑删除,3:已合并)
     */
    private Integer status;
    
    /**
     * 记录创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 记录最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    /**
     * 记录删除时间(逻辑删除)
     */
    @TableLogic
    private LocalDateTime deletedAt;
}
