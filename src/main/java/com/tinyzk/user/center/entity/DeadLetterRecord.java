package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 死信记录实体类
 */
@Data
@TableName("dead_letter_records")
public class DeadLetterRecord {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 原始主题
     */
    private String originalTopic;

    /**
     * 原始消息内容
     */
    private String originalMessage;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 处理状态 (0-未处理, 1-重试中, 2-已处理, 3-处理失败)
     */
    private Integer status;

    /**
     * 重试时间
     */
    private LocalDateTime retryAt;

    /**
     * 处理时间
     */
    private LocalDateTime processedAt;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
