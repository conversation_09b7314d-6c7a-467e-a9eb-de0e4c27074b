package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * OAuth2客户端详情实体类
 */
@Data
@TableName("oauth_client_details")
public class OAuthClientDetails {
    
    /**
     * 客户端唯一标识符
     */
    @TableId
    private String clientId;
    
    /**
     * 客户端密钥 (应用层必须加密存储)
     */
    private String clientSecret;
    
    /**
     * 客户端名称 (如: 官方网站, iOS应用)
     */
    private String clientName;
    
    /**
     * 客户端描述
     */
    private String description;
    
    /**
     * 客户端授权范围 (逗号分隔, 如: read, write)
     */
    private String scope;
    
    /**
     * 允许的授权类型 (逗号分隔, 如: authorization_code, client_credentials, password, implicit, refresh_token)
     */
    private String authorizedGrantTypes;
    
    /**
     * 授权码模式或隐式模式的回调URI (逗号分隔)
     */
    private String webServerRedirectUri;
    
    /**
     * 客户端拥有的权限 (逗号分隔, Spring Security概念)
     */
    private String authorities;
    
    /**
     * 访问令牌有效期秒数
     */
    private Integer accessTokenValidity;
    
    /**
     * 刷新令牌有效期秒数
     */
    private Integer refreshTokenValidity;
    
    /**
     * 附加信息 (可存储JSON)
     */
    private String additionalInformation;
    
    /**
     * 自动授权范围 (逗号分隔, 或 "true" 表示全部)
     */
    private String autoapprove;
    
    /**
     * 客户端状态 (1: 正常, 0: 禁用)
     */
    private Integer status;
    
    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 记录最后更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 逻辑删除时间 (NULL表示未删除)
     */
    @TableLogic
    private LocalDateTime deletedAt;
} 