package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户项目经历实体类
 */
@Data
@TableName("user_project_history")
public class UserProjectHistory {

    /**
     * 项目经历主键ID
     */
    @TableId(value = "project_id", type = IdType.ASSIGN_ID)
    private Long projectId;

    /**
     * 关联的用户ID
     */
    private Long userId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 用户在项目中的角色/职责
     */
    private String role;

    /**
     * 项目开始日期
     */
    private LocalDate startDate;

    /**
     * 项目结束日期(NULL表示进行中)
     */
    private LocalDate endDate;

    /**
     * 项目描述
     */
    private String description;

    /**
     * 项目链接
     */
    private String projectUrl;

    /**
     * 关联组织/公司
     */
    private String associatedOrganization;

    /**
     * 可见性(公开,好友可见,私密)
     */
    private String visibility;

    /**
     * 记录创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 记录最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 记录删除时间(逻辑删除)
     */
    @TableLogic
    private LocalDateTime deletedAt;
}