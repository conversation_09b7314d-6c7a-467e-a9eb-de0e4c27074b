package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户获奖记录实体类
 */
@Data
@TableName("user_awards")
public class UserAwards {

    /**
     * 获奖记录主键ID
     */
    @TableId(value = "award_id", type = IdType.ASSIGN_ID)
    private Long awardId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 奖项名称
     */
    private String awardName;

    /**
     * 奖项级别 (如: 国家级, 省级, 市级, 校级)
     */
    private String awardLevel;

    /**
     * 颁奖机构
     */
    private String awardingOrganization;

    /**
     * 获奖日期
     */
    private LocalDate awardDate;

    /**
     * 获奖描述
     */
    private String description;

    /**
     * 奖项链接或证书图片URL
     */
    private String awardUrl;

    /**
     * 数据来源 (manual-手动添加, resume-简历解析, import-导入)
     */
    private String source;

    /**
     * 可见性 (public-公开, friends-好友可见, private-私密)
     */
    private String visibility;

    /**
     * 记录创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 记录最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 记录删除时间(逻辑删除)
     */
    @TableLogic
    private LocalDateTime deletedAt;
}
