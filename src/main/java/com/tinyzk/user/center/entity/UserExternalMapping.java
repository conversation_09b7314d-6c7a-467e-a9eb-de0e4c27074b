package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户与外部系统身份映射关系实体类
 */
@Data
@TableName("user_external_mapping")
public class UserExternalMapping {
    
    /**
     * 映射记录主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long mappingId;
    
    /**
     * 系统A (用户中心) 的用户ID (逻辑外键)
     */
    private Long userId;
    
    /**
     * 外部系统客户端ID (逻辑外键关联 oauth_client_details)
     */
    private String externalSystemClientId;
    
    /**
     * 用户在外部系统中的唯一标识符
     */
    private String externalUserId;
    
    /**
     * 附加信息 (例如: 映射状态, 关联时间等)
     */
    private String metadata;
    
    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 记录最后更新时间
     */
    private LocalDateTime updatedAt;
} 