package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户工作经历实体类
 */
@Data
@TableName("user_work_history")
public class UserWorkHistory {

    /**
     * 工作经历主键ID
     */
    @TableId(value = "work_id", type = IdType.ASSIGN_ID)
    private Long workId;

    /**
     * 关联的用户ID
     */
    private Long userId;

    /**
     * 公司/组织名称
     */
    private String companyName;

    /**
     * 公司/组织Logo URL
     */
    private String companyLogo;

    /**
     * 公司/组织官网 URL
     */
    private String companyUrl;

    /**
     * 公司/组织规模
     */
    private Integer companySize;

    /**
     * 公司/组织行业
     */
    private String companyIndustry;

    /**
     * 公司/组织地点
     */
    private String companyLocation;

    /**
     * 职位/头衔
     */
    private String positionName;

    /**
     * 所属部门
     */
    private String department;

    /**
     * 入职日期
     */
    private LocalDate startDate;

    /**
     * 离职日期(NULL表示在职)
     */
    private LocalDate endDate;

    /**
     * 工作职责描述
     */
    private String description;

    /**
     * 工作业绩/成果
     */
    private String achievements;

    /**
     * 汇报对象
     */
    private String reportingTo;

    /**
     * 离职原因
     */
    private String reasonForLeaving;

    /**
     * 薪资最小值
     */
    private BigDecimal salaryMin;

    /**
     * 薪资最大值
     */
    private BigDecimal salaryMax;


    /**
     * 认证方式
     */
    private Integer certificationType;

    /**
     * 认证状态
     */
    private Integer certificationStatus;

    /**
     * 可见性(公开,好友可见,私密)
     */
    private String visibility;

    /**
     * 记录创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 记录最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 记录删除时间(逻辑删除)
     */
    @TableLogic
    private LocalDateTime deletedAt;
}