package com.tinyzk.user.center.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户语言能力实体类
 */
@Data
@TableName("user_languages")
public class UserLanguages {

    /**
     * 语言能力主键ID
     */
    @TableId(value = "language_id", type = IdType.ASSIGN_ID)
    private Long languageId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 语言名称
     */
    private String languageName;

    /**
     * 听力水平 (1-初级, 2-中级, 3-高级, 4-母语)
     */
    private Integer listeningLevel;

    /**
     * 口语水平 (1-初级, 2-中级, 3-高级, 4-母语)
     */
    private Integer speakingLevel;

    /**
     * 阅读水平 (1-初级, 2-中级, 3-高级, 4-母语)
     */
    private Integer readingLevel;

    /**
     * 写作水平 (1-初级, 2-中级, 3-高级, 4-母语)
     */
    private Integer writingLevel;

    /**
     * 总体水平描述
     */
    private String overallLevel;

    /**
     * 相关证书信息
     */
    private String certificateInfo;

    /**
     * 数据来源 (manual-手动添加, resume-简历解析, import-导入)
     */
    private String source;

    /**
     * 可见性 (public-公开, friends-好友可见, private-私密)
     */
    private String visibility;

    /**
     * 记录创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 记录最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 记录删除时间(逻辑删除)
     */
    @TableLogic
    private LocalDateTime deletedAt;
}
