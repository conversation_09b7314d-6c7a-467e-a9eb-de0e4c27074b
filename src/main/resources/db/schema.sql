-- 创建数据库
CREATE DATABASE IF NOT EXISTS user_center DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE user_center;

-- 用户基础表
CREATE TABLE IF NOT EXISTS `user_base` (
  `user_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户唯一主键ID',
  `real_name_verified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '实名认证状态(0:未认证,1:已认证)',
  `real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '真实姓名(认证后填充,脱敏存储或加密)',
  `id_card_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号(认证后填充,必须加密存储)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '用户状态(1:正常,2:禁用,0:注销/逻辑删除,3:已合并)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '记录删除时间(逻辑删除)',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_id_card_number` (`id_card_number`),
  KEY `idx_real_name` (`real_name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户基础表';

-- 用户认证/登录方式表
CREATE TABLE IF NOT EXISTS `user_auth` (
  `auth_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '认证记录主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '关联的用户基础ID (逻辑外键)',
  `identity_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '认证/登录类型 (WECHAT_MP, ALIPAY_MP, DOUYIN_MP, PHONE, EMAIL, USERNAME)',
  `identifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '凭证标识 (OpenID, UserID, 手机号, 用户名等)',
  `credential` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '凭证内容 (密码哈希, Token等, 可为空)',
  `verified` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否已验证 (1: 已验证, 0: 未验证)',
  `last_login_at` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '记录删除时间(逻辑删除)',
  PRIMARY KEY (`auth_id`),
  UNIQUE KEY `uk_identity_identifier` (`identity_type`,`identifier`,`deleted_at`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_identity_type` (`identity_type`),
  KEY `idx_identifier` (`identifier`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户认证/登录方式表';

-- 用户个人资料表
CREATE TABLE IF NOT EXISTS `user_profile` (
  `profile_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '资料记录主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '关联的用户基础ID (逻辑外键)',
  `nickname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户昵称',
  `avatar_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint DEFAULT '0' COMMENT '性别 (0: 未知, 1: 男, 2: 女)',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `region_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地区编码',
  `region_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地区名称',
  `bio` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '个人简介',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '记录删除时间(逻辑删除)',
  PRIMARY KEY (`profile_id`),
  UNIQUE KEY `uk_user_id` (`user_id`,`deleted_at`),
  KEY `idx_nickname` (`nickname`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户个人资料表';

-- 用户实名认证记录表
CREATE TABLE IF NOT EXISTS `user_real_name_auth` (
  `auth_record_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '认证记录主键',
  `user_id` bigint unsigned NOT NULL COMMENT '关联的用户基础ID (逻辑外键, 关联最终认证成功的user_id)',
  `submitted_real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提交时使用的真实姓名 (应用层考虑加密)',
  `submitted_id_card_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提交时使用的身份证号 (应用层必须加密存储)',
  `auth_status` tinyint NOT NULL DEFAULT '0' COMMENT '认证状态 (0: 处理中, 1: 成功, 2: 失败)',
  `auth_time` datetime DEFAULT NULL COMMENT '认证完成时间',
  `auth_channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '认证渠道/方式',
  `fail_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '失败原因 (如果认证失败)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  PRIMARY KEY (`auth_record_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_auth_status` (`auth_status`),
  KEY `idx_submitted_id_card` (`submitted_id_card_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户实名认证记录表';

-- 用户操作审计日志表
CREATE TABLE IF NOT EXISTS `user_audit_log` (
  `log_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志记录主键ID',
  `user_id` bigint unsigned DEFAULT NULL COMMENT '关联的用户基础ID (逻辑外键)',
  `operation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型 (LOGIN_SUCCESS, LOGIN_FAIL, REGISTER_SUCCESS, ACCOUNT_MERGE_START, ACCOUNT_MERGE_COMPLETE等)',
  `operation_detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '操作详情 (JSON格式, 包含操作的详细信息)',
  `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户代理信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户操作审计日志表';
