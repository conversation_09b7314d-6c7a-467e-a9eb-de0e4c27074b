-- ----------------------------
-- 更新 user_profile 表，添加PRD文档定义的缺失字段
-- ----------------------------

-- 添加国籍字段
ALTER TABLE `user_profile` 
ADD COLUMN `nationality` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国籍' AFTER `birthday`;

-- 添加民族字段
ALTER TABLE `user_profile` 
ADD COLUMN `ethnicity` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '民族' AFTER `nationality`;

-- 添加特殊身份字段
ALTER TABLE `user_profile` 
ADD COLUMN `special_status` tinyint DEFAULT NULL COMMENT '特殊身份 (1-军人, 2-警察, 3-医生, 4-教师, 5-残疾人, 6-其他)' AFTER `ethnicity`;

-- 添加政治面貌字段
ALTER TABLE `user_profile` 
ADD COLUMN `political_status` tinyint DEFAULT NULL COMMENT '政治面貌 (1-中共党员, 2-中共预备党员, 3-共青团员, 4-群众)' AFTER `special_status`;

-- 添加婚姻状况字段
ALTER TABLE `user_profile` 
ADD COLUMN `marital_status` tinyint DEFAULT NULL COMMENT '婚姻状况 (1-未婚, 2-已婚, 3-离异, 4-丧偶)' AFTER `political_status`;

-- 添加生育情况字段
ALTER TABLE `user_profile` 
ADD COLUMN `fertility_status` tinyint DEFAULT NULL COMMENT '生育情况 (1-未育, 2-已育, 3-已育一孩, 4-已育两孩及以上)' AFTER `marital_status`;

-- 添加健康状况字段
ALTER TABLE `user_profile` 
ADD COLUMN `health_status` tinyint DEFAULT NULL COMMENT '健康状况 (1-健康, 2-良好, 3-一般, 4-较差)' AFTER `fertility_status`;

-- 添加详细地址字段
ALTER TABLE `user_profile` 
ADD COLUMN `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '详细地址' AFTER `region_name`;

-- 调整头像URL字段长度，保持与原始创建脚本一致
ALTER TABLE `user_profile` 
MODIFY COLUMN `avatar_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL';

-- ----------------------------
-- 创建培训经历表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `user_training_history` (
  `training_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '培训经历主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID (逻辑外键, 关联 user_base.user_id)',
  `training_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '培训名称',
  `training_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '培训类型 (如: 线上培训, 线下培训)',
  `training_provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '培训提供者 (如: 公司, 机构)',
  `start_date` date DEFAULT NULL COMMENT '培训开始日期 (年-月-日)',
  `end_date` date DEFAULT NULL COMMENT '培训结束日期 (年-月-日, NULL表示进行中)',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '培训描述 (如: 培训内容, 培训成果)',
  `visibility` enum('PUBLIC','CONNECTIONS','PRIVATE') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PRIVATE' COMMENT '可见性 (公开, 好友可见, 私密)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '逻辑删除时间',
  PRIMARY KEY (`training_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户培训经历表';

-- ----------------------------
-- 创建兼职经历表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `user_part_time_history` (
  `part_time_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '兼职经历主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID (逻辑外键, 关联 user_base.user_id)',
  `part_time_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '兼职名称',
  `part_time_type` tinyint DEFAULT NULL COMMENT '兼职类型 (1-咨询, 2-设计, 3-开发, 4-运营, 5-销售, 6-客服, 7-服务员, 8-其他)',
  `part_time_provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '兼职提供者 (如: 公司, 机构)',
  `part_time_location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '兼职地点 (如: 城市)',
  `part_time_salary` decimal(10,2) DEFAULT NULL COMMENT '兼职薪资',
  `service_period` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '服务周期 (如: 1个月, 3个月, 6个月, 1年)',
  `start_date` date DEFAULT NULL COMMENT '兼职开始日期 (年-月-日)',
  `end_date` date DEFAULT NULL COMMENT '兼职结束日期 (年-月-日, NULL表示进行中)',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '兼职描述 (如: 兼职内容, 兼职成果)',
  `visibility` enum('PUBLIC','CONNECTIONS','PRIVATE') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PRIVATE' COMMENT '可见性 (公开, 好友可见, 私密)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '逻辑删除时间',
  PRIMARY KEY (`part_time_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户兼职经历表';

-- 更新工作经历表，添加PRD文档中的额外字段
ALTER TABLE `user_work_history`
ADD COLUMN `company_logo` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公司/组织Logo URL' AFTER `company_name`,
ADD COLUMN `company_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公司/组织官网 URL' AFTER `company_logo`,
ADD COLUMN `company_size` int DEFAULT NULL COMMENT '公司/组织规模 (如: 100-500人)' AFTER `company_url`,
ADD COLUMN `company_industry` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公司/组织行业 (如: 互联网, 金融, 教育, 医疗等)' AFTER `company_size`,
ADD COLUMN `company_location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公司/组织地点 (如: 北京, 上海, 广州, 深圳等)' AFTER `company_industry`,
ADD COLUMN `achievements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '工作业绩/成果' AFTER `description`,
ADD COLUMN `reporting_to` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '汇报对象 (如: 张三)' AFTER `achievements`,
ADD COLUMN `reason_for_leaving` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '离职原因 (如: 个人发展, 公司调整, 家庭原因等)' AFTER `reporting_to`,
ADD COLUMN `salary_min` decimal(10,2) DEFAULT NULL COMMENT '薪资最小值 (如: 10000)' AFTER `reason_for_leaving`,
ADD COLUMN `salary_max` decimal(10,2) DEFAULT NULL COMMENT '薪资最大值 (如: 20000)' AFTER `salary_min`,
ADD COLUMN `certification_type` tinyint DEFAULT NULL COMMENT '认证方式 (如: 1-工牌、2-劳动合同、3-社保、4-个税)' AFTER `salary_max`,
ADD COLUMN `certification_status` tinyint DEFAULT '0' COMMENT '认证状态 (如: 0-未认证、1-已认证)' AFTER `certification_type`;

-- 更新教育经历表，添加PRD文档中的额外字段
ALTER TABLE `user_education_history`
ADD COLUMN `degree_level` tinyint DEFAULT NULL COMMENT '学位等级 (1-本科, 2-硕士, 3-博士, 4-高中等)' AFTER `degree`,
ADD COLUMN `secondary_major` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '第二专业或研究领域 (可选)' AFTER `major`,
ADD COLUMN `major_area` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '专业方向 (可选)' AFTER `secondary_major`,
ADD COLUMN `major_gpa` float DEFAULT NULL COMMENT '专业GPA (可选)' AFTER `major_area`,
ADD COLUMN `club_experience` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '社团经历' AFTER `description`;

-- 更新用户联系方式表的contact_type字段类型
ALTER TABLE `user_contact_methods`
MODIFY COLUMN `contact_type` tinyint DEFAULT NULL COMMENT '联系方式类型 (1-备用邮箱, 2-工作电话, 3-紧急联系人, 4-微信, 5-QQ、6-即刻、7-小红书、8-微博、9-抖音、10-其他)',
MODIFY COLUMN `label` tinyint DEFAULT NULL COMMENT '用户自定义标签 (1-备用邮箱, 2-工作电话, 3-紧急联系人, 4-社交账号)'; 