-- ----------------------------
-- 用户教育经历表
-- ----------------------------
CREATE TABLE `user_education_history` (
                                          `edu_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '教育经历主键ID',
                                          `user_id` bigint unsigned NOT NULL COMMENT '用户ID (逻辑外键, 关联 user_base.user_id)',
                                          `school_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '学校名称',
                                          `degree` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '学位 (如: 学士, 硕士, 博士, 高中等)',
                                          `major` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '专业或研究领域',
                                          `start_date` date DEFAULT NULL COMMENT '入学日期 (年-月-日)',
                                          `end_date` date DEFAULT NULL COMMENT '毕业/离校日期 (年-月-日, NULL表示在读)',
                                          `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '描述/在校经历/荣誉等',
                                          `visibility` enum('PUBLIC','CONNECTIONS','PRIVATE') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PRIVATE' COMMENT '可见性 (公开, 好友可见, 私密)',
                                          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                                          `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
                                          `deleted_at` timestamp NULL DEFAULT NULL COMMENT '逻辑删除时间',
                                          PRIMARY KEY (`edu_id`),
                                          KEY `idx_user_id` (`user_id`),
                                          KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户教育经历表';

-- ----------------------------
-- 用户工作经历表
-- ----------------------------
CREATE TABLE `user_work_history` (
                                     `work_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '工作经历主键ID',
                                     `user_id` bigint unsigned NOT NULL COMMENT '用户ID (逻辑外键, 关联 user_base.user_id)',
                                     `company_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公司/组织名称',
                                     `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '职位/头衔',
                                     `department` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所属部门',
                                     `start_date` date DEFAULT NULL COMMENT '入职日期 (年-月-日)',
                                     `end_date` date DEFAULT NULL COMMENT '离职日期 (年-月-日, NULL表示在职)',
                                     `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '工作职责/成就描述',
                                     `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工作地点 (如: 城市)',
                                     `visibility` enum('PUBLIC','CONNECTIONS','PRIVATE') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PRIVATE' COMMENT '可见性 (公开, 好友可见, 私密)',
                                     `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                                     `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
                                     `deleted_at` timestamp NULL DEFAULT NULL COMMENT '逻辑删除时间',
                                     PRIMARY KEY (`work_id`),
                                     KEY `idx_user_id` (`user_id`),
                                     KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户工作经历表';

-- ----------------------------
-- 用户联系方式表
-- ----------------------------
CREATE TABLE `user_contact_methods` (
                                        `contact_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '联系方式主键ID',
                                        `user_id` bigint unsigned NOT NULL COMMENT '用户ID (逻辑外键, 关联 user_base.user_id)',
                                        `contact_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系方式类型 (如 EMAIL_SECONDARY, PHONE_WORK, WECHAT_ID, LINKEDIN_URL, GITHUB_URL, PERSONAL_WEBSITE)',
                                        `contact_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系方式的值 (邮箱地址, 电话号码, URL, ID等)',
                                        `label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户自定义标签 (如: 备用邮箱, 工作电话)',
                                        `visibility` enum('PUBLIC','CONNECTIONS','PRIVATE') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PRIVATE' COMMENT '可见性',
                                        `is_verified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已验证 (0: 未验证, 1: 已验证)',
                                        `verified_at` timestamp NULL DEFAULT NULL COMMENT '验证时间',
                                        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                                        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
                                        `deleted_at` timestamp NULL DEFAULT NULL COMMENT '逻辑删除时间',
                                        PRIMARY KEY (`contact_id`),
                                        KEY `idx_user_id` (`user_id`),
                                        KEY `idx_contact_type` (`contact_type`),
                                        KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户附加联系方式表';

-- ----------------------------
-- 用户项目经历表
-- ----------------------------
CREATE TABLE `user_project_history` (
  `project_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '项目经历主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID (逻辑外键, 关联 user_base.user_id)',
  `project_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '项目名称',
  `role` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户在项目中的角色/职责 (如: 开发者, 项目经理, 设计师)',
  `start_date` date DEFAULT NULL COMMENT '项目开始日期 (年-月-日)',
  `end_date` date DEFAULT NULL COMMENT '项目结束日期 (年-月-日, NULL表示进行中)',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '项目描述 (目标, 用户贡献, 使用技术, 成果等)',
  `project_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '项目链接 (如: 代码仓库, 演示地址, 官网)',
  `associated_organization` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联组织/公司 (可选, 用于说明项目归属)',
  `visibility` enum('PUBLIC','CONNECTIONS','PRIVATE') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PRIVATE' COMMENT '可见性 (公开, 好友可见, 私密)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '逻辑删除时间',
  PRIMARY KEY (`project_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户项目经历表';