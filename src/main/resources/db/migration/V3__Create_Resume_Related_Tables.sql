-- ----------------------------
-- 用户技能表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `user_skills` (
  `skill_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '技能主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID (逻辑外键, 关联 user_base.user_id)',
  `skill_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '技能名称',
  `skill_type` tinyint NOT NULL DEFAULT '1' COMMENT '技能类型 (1-IT技能, 2-业务技能, 3-语言技能, 4-其他技能)',
  `proficiency_level` tinyint DEFAULT NULL COMMENT '熟练程度 (1-初级, 2-中级, 3-高级, 4-专家)',
  `years_of_experience` decimal(3,1) DEFAULT NULL COMMENT '使用年限',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '技能描述',
  `source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'manual' COMMENT '数据来源 (manual-手动添加, resume-简历解析, import-导入)',
  `visibility` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'public' COMMENT '可见性 (public-公开, friends-好友可见, private-私密)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '记录删除时间(逻辑删除)',
  PRIMARY KEY (`skill_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_skill_name` (`skill_name`),
  KEY `idx_skill_type` (`skill_type`),
  KEY `idx_source` (`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户技能表';

-- ----------------------------
-- 用户证书表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `user_certificates` (
  `certificate_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '证书主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID (逻辑外键, 关联 user_base.user_id)',
  `certificate_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '证书名称',
  `issuing_organization` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '颁发机构',
  `certificate_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证书编号',
  `issue_date` date DEFAULT NULL COMMENT '颁发日期',
  `expiry_date` date DEFAULT NULL COMMENT '过期日期 (NULL表示永久有效)',
  `certificate_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证书链接或图片URL',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证书描述',
  `verification_status` tinyint DEFAULT '0' COMMENT '验证状态 (0-未验证, 1-已验证, 2-验证失败)',
  `source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'manual' COMMENT '数据来源 (manual-手动添加, resume-简历解析, import-导入)',
  `visibility` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'public' COMMENT '可见性 (public-公开, friends-好友可见, private-私密)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '记录删除时间(逻辑删除)',
  PRIMARY KEY (`certificate_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_certificate_name` (`certificate_name`),
  KEY `idx_issuing_organization` (`issuing_organization`),
  KEY `idx_source` (`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户证书表';

-- ----------------------------
-- 用户语言能力表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `user_languages` (
  `language_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '语言能力主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID (逻辑外键, 关联 user_base.user_id)',
  `language_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '语言名称',
  `listening_level` tinyint DEFAULT NULL COMMENT '听力水平 (1-初级, 2-中级, 3-高级, 4-母语)',
  `speaking_level` tinyint DEFAULT NULL COMMENT '口语水平 (1-初级, 2-中级, 3-高级, 4-母语)',
  `reading_level` tinyint DEFAULT NULL COMMENT '阅读水平 (1-初级, 2-中级, 3-高级, 4-母语)',
  `writing_level` tinyint DEFAULT NULL COMMENT '写作水平 (1-初级, 2-中级, 3-高级, 4-母语)',
  `overall_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '总体水平描述',
  `certificate_info` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '相关证书信息',
  `source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'manual' COMMENT '数据来源 (manual-手动添加, resume-简历解析, import-导入)',
  `visibility` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'public' COMMENT '可见性 (public-公开, friends-好友可见, private-私密)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '记录删除时间(逻辑删除)',
  PRIMARY KEY (`language_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_language_name` (`language_name`),
  KEY `idx_source` (`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户语言能力表';

-- ----------------------------
-- 用户获奖记录表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `user_awards` (
  `award_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '获奖记录主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID (逻辑外键, 关联 user_base.user_id)',
  `award_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '奖项名称',
  `award_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '奖项级别 (如: 国家级, 省级, 市级, 校级)',
  `awarding_organization` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '颁奖机构',
  `award_date` date DEFAULT NULL COMMENT '获奖日期',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '获奖描述',
  `award_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '奖项链接或证书图片URL',
  `source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'manual' COMMENT '数据来源 (manual-手动添加, resume-简历解析, import-导入)',
  `visibility` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'public' COMMENT '可见性 (public-公开, friends-好友可见, private-私密)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '记录删除时间(逻辑删除)',
  PRIMARY KEY (`award_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_award_name` (`award_name`),
  KEY `idx_award_level` (`award_level`),
  KEY `idx_source` (`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户获奖记录表';

-- ----------------------------
-- 简历解析记录表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `resume_parse_records` (
  `record_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '解析记录主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID (逻辑外键, 关联 user_base.user_id)',
  `original_filename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始文件名',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件类型 (doc, docx, pdf)',
  `file_hash` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件MD5哈希值',
  `parse_status` tinyint NOT NULL DEFAULT '0' COMMENT '解析状态 (0-待解析, 1-解析中, 2-解析成功, 3-解析失败)',
  `parse_result` json DEFAULT NULL COMMENT '完整的解析结果JSON',
  `error_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '错误信息',
  `third_party_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '第三方服务返回的ID',
  `parse_duration` int DEFAULT NULL COMMENT '解析耗时(毫秒)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '记录删除时间(逻辑删除)',
  PRIMARY KEY (`record_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parse_status` (`parse_status`),
  KEY `idx_file_hash` (`file_hash`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历解析记录表';
