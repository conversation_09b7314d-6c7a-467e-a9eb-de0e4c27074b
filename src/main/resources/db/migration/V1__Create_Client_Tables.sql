-- ----------------------------
-- OAuth2 客户端详情表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `oauth_client_details` (
  `client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端唯一标识符',
  `client_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户端密钥 (应用层必须加密存储)',
  `client_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端名称 (如: 官方网站, iOS应用)',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '客户端描述',
  `scope` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户端授权范围 (逗号分隔, 如: read, write)',
  `authorized_grant_types` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '允许的授权类型 (逗号分隔, 如: authorization_code, client_credentials, password, implicit, refresh_token)',
  `web_server_redirect_uri` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '授权码模式或隐式模式的回调URI (逗号分隔)',
  `authorities` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户端拥有的权限 (逗号分隔, Spring Security概念)',
  `access_token_validity` int DEFAULT NULL COMMENT '访问令牌有效期秒数',
  `refresh_token_validity` int DEFAULT NULL COMMENT '刷新令牌有效期秒数',
  `additional_information` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '附加信息 (可存储JSON)',
  `autoapprove` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '自动授权范围 (逗号分隔, 或 "true" 表示全部)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '客户端状态 (1: 正常, 0: 禁用)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '逻辑删除时间 (NULL表示未删除)',
  PRIMARY KEY (`client_id`),
  KEY `idx_deleted_at` (`deleted_at`) COMMENT '逻辑删除索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OAuth2 客户端详情表，用于管理接入的客户端应用';

-- ----------------------------
-- 用户操作审计日志表 (更新)
-- ----------------------------
ALTER TABLE `user_audit_log` 
ADD COLUMN `client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发起请求的客户端ID (逻辑外键关联 oauth_client_details)',
ADD INDEX `idx_client_id` (`client_id`) COMMENT '客户端ID索引'; 