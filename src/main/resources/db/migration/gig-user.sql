-- auto-generated definition
create table customer_user
(
    id              bigint auto_increment
        primary key,
    name            varchar(64)                          null comment '姓名',
    code            varchar(64)                          null comment '身份证',
    phone           varchar(64)                          not null comment '电话号码',
    gender          smallint   default -1                null comment '0女，1男',
    avatar          varchar(1000)                        null comment '头像',
    address         varchar(255)                         null comment '地址',
    birthday        varchar(64)                          null comment '出生日',
    nickname        varchar(255)                         null comment '昵称',
    height          varchar(50)                          null comment '身高(cm)',
    weight          varchar(50)                          null comment '体重(kg)',
    description     varchar(255)                         null comment '描述',
    status          smallint   default 0                 null comment '0.未激活，1.激活，。2.注销',
    register_source smallint   default 1                 null comment '1/微信,2/支付宝 3/商家中心',
    data_source     smallint                             null comment '数据来源：1-人资2-小程序3-求职机',
    certify_passed  tinyint(1) default 0                 null comment '是否通过个人身份认证',
    certify_model   smallint   default 2                 null comment '商户模式  1.个人 ，2.商户',
    last_login_time datetime                             null comment '最后登录时间',
    update_time     datetime                             null on update CURRENT_TIMESTAMP,
    create_time     datetime   default CURRENT_TIMESTAMP null,
    delete_time     datetime                             null,
    constraint unique_index
        unique (phone)
)
    comment '用户信息表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;



-- auto-generated definition
create table customer_user_login
(
    id               bigint auto_increment
        primary key,
    customer_user_id bigint                             null comment '用户表ID',
    phone            varchar(64)                        null comment '用户手机号',
    unique_id        varchar(64)                        null comment '微信唯一b标识id / 支付宝唯一标识id',
    source           int      default 1                 null comment '1/微信登录,2/支付宝小程序 3/商家版',
    other_info       varchar(500)                       null comment '其他信息',
    create_time      datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_time      datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '修改时间',
    last_login_time  datetime                           null comment '最近登录时间',
    delete_time      datetime                           null,
    constraint unique_index
        unique (unique_id, source)
)
    comment '用户登陆信息表' collate = utf8mb4_general_ci
                             row_format = DYNAMIC;

