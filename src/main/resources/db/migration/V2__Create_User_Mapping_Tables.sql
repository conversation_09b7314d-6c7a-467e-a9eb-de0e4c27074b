-- ----------------------------
-- 用户外部系统映射表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `user_external_mapping` (
  `mapping_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '映射记录主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '系统A (用户中心) 的用户ID (逻辑外键)',
  `external_system_client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '外部系统客户端ID (逻辑外键关联 oauth_client_details)',
  `external_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户在外部系统中的唯一标识符',
  `metadata` json DEFAULT NULL COMMENT '附加信息 (例如: 映射状态, 关联时间等)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  PRIMARY KEY (`mapping_id`),
  UNIQUE KEY `uk_external_identity` (`external_system_client_id`, `external_user_id`) COMMENT '同一外部系统下的外部用户ID唯一',
  KEY `idx_user_id` (`user_id`),
  KEY `idx_external_system_client_id` (`external_system_client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户与外部系统身份映射关系表';

-- ----------------------------
-- 添加测试数据: 旧系统客户端
-- ----------------------------
INSERT INTO `oauth_client_details` (`client_id`, `client_name`, `description`, `status`, `created_at`, `updated_at`)
VALUES ('LEGACY_SYSTEM', '旧系统', '候选者系统数据迁移', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE `updated_at` = NOW(); 