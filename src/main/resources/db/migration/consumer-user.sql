-- auto-generated definition
create table customer_user
(
    id                              bigint auto_increment
        primary key,
    name                            varchar(64)                           null comment '姓名',
    code                            varchar(64)                           null comment '身份证',
    phone                           varchar(64)                           not null comment '电话号码',
    history_phone                   varchar(64)                           null comment '历史手机号',
    registration_id                 varchar(64)                           null comment '极光推送ID',
    salt                            varchar(64)                           null,
    password                        varchar(64)                           null comment '密码',
    create_ip                       varchar(64)                           null comment '登陆IP、',
    gender                          smallint(1) default -1                null comment '0女，1男',
    avatar                          varchar(1000)                         null comment '头像',
    email                           varchar(64)                           null comment '邮箱',
    address                         varchar(255)                          null comment '地址',
    birthday                        varchar(64)                           null comment '出生日',
    description                     varchar(255)                          null comment '描述',
    status                          smallint(1) default 0                 null comment '0.未激活，1.激活，。2.注销',
    easemob_uuid                    varchar(255)                          null comment '环信账号uuid',
    easemob_account                 varchar(255)                          null comment '环信账号',
    easemob_password                varchar(255)                          null comment '环信密码',
    last_login_time                 datetime                              null comment '最后登录时间',
    update_time                     datetime                              null on update CURRENT_TIMESTAMP,
    create_time                     datetime    default CURRENT_TIMESTAMP null,
    register_source                 varchar(64)                           null comment '注册来源：
app  app注册
public   公众号注册
alimini  小程序注册
aliminibacktune 支付宝一号背调小程序
lao       老数据
bImport   B端上传
dingdingMini  钉钉小程序注册
del 已删除假C
csdn  csdn小程序注册',
    channel_name                    varchar(64)                           null comment '注册来源渠道',
    activity_name                   varchar(64)                           null comment '注册来源活动',
    zhi_ma_url                      varchar(4000)                         null comment '职得工作证跳转小程序链接',
    zhi_ma_picture_url              varchar(4000)                         null comment '职得工作证图片url,可以直接用于展式',
    zhi_ma_update_url               varchar(4000)                         null comment '工作证图片更新的url,需要跳转到工作证小程序中进行更新',
    zhi_ma_status                   int(10)                               null comment '芝麻工作证是否授权   1/未开通 2/未授权 3/已授权
',
    is_cancel                       int(1)      default 0                 null comment '是否注销：1是，0否',
    is_alipay_favorite_applet       int(2)                                null comment '是否支付宝收藏小程序： 1/是，0/否',
    is_alipay_attention_life_number int(2)                                null comment '是否支付宝关注生活号： 1/是。0/否',
    lucky_draw_code                 varchar(200)                          null comment '活动抽奖码',
    is_impass_authorization         int(2)      default 0                 null comment 'impass授权  1/完成授权 0/未完成授权',
    is_cloud_resume_authorization   varchar(50)                           null comment '云简历授权开通状态，VALID：有效，INVALID：无效',
    cloud_resume_authorization_time datetime                              null comment '云简历授权时间',
    cloud_resume_authorization_info varchar(500)                          null comment '云简历授权变更信息',
    json_system_info                longtext                              null,
    constraint unique_index
        unique (phone)
)
    comment '用户信息表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;

create index idx_register_source
    on customer_user (register_source);


-- auto-generated definition
create table customer_user_login
(
    id               bigint auto_increment
        primary key,
    create_ip        varchar(64)                        null comment '登陆ip地址',
    customer_user_id bigint                             null comment '用户表ID',
    phone            varchar(64)                        null comment '用户手机号',
    unique_id        varchar(64)                        null comment '微信唯一b标识id',
    ref_id           varchar(64)                        null comment '第三方唯一ID',
    source           int(1)                             null comment '1.微信登录，2.app登录，3.极光登录，4.支付宝小程序登录，5.阿里买家登录, 6.支付宝登录，7，钉钉小程序登录,8.抖音小程序登录 ,9.支付宝1号背调小程序登录，10.csdn登录',
    other_info       varchar(100)                       null comment '第三方其他信息',
    create_time      datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_time      datetime                           null comment '修改时间',
    is_deleted       int(1)   default 0                 null comment '是否删除 1删除 0正常',
    last_login_time  datetime                           null comment '最近登录时间',
    login_times      int                                null comment '登录次数',
    activity         decimal(10, 2)                     null comment '活跃度',
    out_agreement_no varchar(100)                       null comment '云简历用户授权号',
    constraint unque_index
        unique (ref_id, source)
)
    comment '用户登陆信息表' collate = utf8mb4_general_ci
                             row_format = DYNAMIC;

-- auto-generated definition
create table customer_user_resume_education
(
    id                bigint auto_increment
        primary key,
    resume_id         bigint                             not null comment '简历ID',
    customer_user_id  bigint                             not null comment '用户id',
    dict_school_id    bigint                             null comment '关联字典表学校id',
    school_name       varchar(255)                       null comment '学校名称',
    school_logo       varchar(255)                       null comment '学校log',
    dict_education_id bigint                             null comment '关联字典表学历id',
    education_name    varchar(255)                       null comment '学历',
    dict_major_id     bigint                             null comment '关联字典表专业id',
    major_name        varchar(255)                       null comment '专业名称',
    enrollment        int(1)                             null comment '1.统招，2.成人教育，3.自考',
    is_deleted        int(1)   default 0                 null comment '是否删除 1删除 0正常',
    start_time        datetime                           null comment '开始入学时间',
    end_time          datetime                           null comment '结业时间',
    create_time       datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_time       datetime                           null on update CURRENT_TIMESTAMP comment '更新时间',
    is_authed         int(1)   default 0                 null comment '天下信用是否已认证 1认证 0没认证',
    cert_result       int(1)                             null comment '认证结果：0一致 1不一致 2未查到',
    education_number  varchar(80)                        null comment '学历编号'
)
    comment '用户简历教育经历表' collate = utf8mb4_general_ci
                                 row_format = DYNAMIC;

create index uni_index
    on customer_user_resume_education (customer_user_id, resume_id);


-- auto-generated definition
create table customer_user_resume_word_experience
(
    id                  bigint auto_increment
        primary key,
    resume_id           bigint                                not null comment '简历ID',
    customer_user_id    bigint                                not null comment '用户id',
    job_type_id         bigint                                null comment '职位类型id',
    job_type_name       varchar(255)                          null comment '职位类型名称',
    job_name            varchar(255)                          null comment '职位名称',
    work_province_id    bigint                                null comment '省份id',
    work_province_name  varchar(255)                          null comment '省份名称',
    work_city_id        bigint                                null comment '城市id',
    work_city_name      varchar(255)                          null comment '城市名称',
    dict_company_id     bigint                                null comment '关联字典表公司id',
    company_logo        varchar(255)                          null comment '公司logo',
    company_name        varchar(255)                          null comment '公司名称',
    trade_id            bigint                                null comment '行业id',
    trade_name          varchar(255)                          null comment '行业名称',
    reports_to          varchar(255)                          null comment '汇报对象',
    subordinate_num     int                                   null comment '下属人数',
    salary              int                                   null comment '月薪',
    months_salary       int                                   null comment '多少个月',
    stock_right         int(1)      default 0                 null comment '是否有股权 1有   0没有',
    options             int(1)      default 0                 null comment '是否有期权 1有  0没有',
    description         text                                  null comment '=描述',
    start_time          datetime                              null comment '开始时间',
    end_time            datetime                              null comment '结束时间',
    create_time         datetime    default CURRENT_TIMESTAMP null comment '创建时间',
    update_time         datetime                              null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted          int(1)      default 0                 null comment '是否删除 1删除 0正常',
    is_authed           int(1)      default 0                 null comment '天下信用是否认证 1认证 0没认证',
    cert_result         int(1)                                null comment '认证结果：0一致 1不一致 2未查到',
    resume_verification smallint(1) default 0                 null comment '简历验真认证: 0/未认证,1/认证中,2/认证成功,3/认证失败'
)
    comment '用户简历工作经验表' collate = utf8mb4_general_ci
                                 row_format = DYNAMIC;

create index ` job_type_id_index`
    on customer_user_resume_word_experience (job_type_id);

create index `index`
    on customer_user_resume_word_experience (customer_user_id, resume_id);

create index work_city_id_index
    on customer_user_resume_word_experience (work_city_id);


-- auto-generated definition
create table customer_resume_base
(
    id                   bigint auto_increment
        primary key,
    customer_user_id     bigint                                 null comment '用户表ID',
    surname              varchar(32)                            null comment '姓氏',
    is_real_name         int(1)       default 0                 null comment '是否实名认证：1已认证，0未认证',
    real_name_type       int(1)                                 null comment '实名认证方式：1支付宝实名认证，2个人信用报告认证',
    real_name_time       datetime                               null comment '实名认证时间',
    customer_user_name   varchar(64)                            null comment '简历人才名称',
    code                 varchar(64)                            null comment '身份证号',
    name                 varchar(255)                           null comment '简历名称',
    phone                varchar(64)                            null comment '手机号',
    email                varchar(255)                           null comment '邮箱',
    gender               int(1)       default -1                null comment '性别：0女 1男',
    birthday             varchar(32)                            null comment '出生日',
    start_work_time      varchar(32)                            null comment '开始工作时间',
    avatar               varchar(255)                           null comment '头像',
    hign_education       varchar(64)                            null comment '最高学历',
    school               varchar(255)                           null comment '最高学历学校名称',
    major                varchar(255) default ''                null comment '最高学历专业',
    address              varchar(255)                           null comment '联系地址',
    personal_description varchar(500)                           null comment '个人描述（简介）',
    job_state            int(1)                                 null comment '0.离职-随时到岗,1.在职-月内到岗,2.在职-考虑机会',
    skill_tags           varchar(3000)                          null comment '技能标签',
    news_tag             varchar(3000)                          null comment '职场新闻标签',
    office_animals       varchar(5000)                          null comment '职场测试',
    resume_source        varchar(32)  default 'C'               null comment '来源 C: C端注册完善，CRM:crm简历导入，B：B端上传',
    resume_url           varchar(255)                           null comment '原简历地址（CRM导入解析的简历地址）',
    platform             varchar(255)                           null comment '原简历来源（CRM导入解析的简历平台来源）',
    position_name        varchar(255)                           null comment 'nlp解析的职位名称',
    create_name          varchar(64)                            null comment '创建人名称',
    create_time          datetime     default CURRENT_TIMESTAMP null comment '创建时间',
    update_time          datetime                               null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted           int(1)       default 0                 null comment '是否删除 1删除 0整除',
    `function`           varchar(255)                           null comment '职能',
    work_address         varchar(255)                           null comment '工作地址',
    salary               varchar(64)                            null comment '当前薪资',
    expected_salary      varchar(64)                            null comment '期望薪资',
    pinyin_name          varchar(64)                            null comment '拼音名称',
    name_is_authed       int(1)       default 0                 null comment '天下信用是否已认证，1认证 0没认证',
    criminal_record      int(1)       default 0                 null comment '刑事记录 1有前科 0良好',
    completion           int          default 0                 null comment '简历完善度',
    name_cert_result     int(1)                                 null comment '认证结果：0一致 1不一致 2未查到',
    education_id         varchar(20)                            null comment '天下信用选中--学历id',
    certification_id     varchar(20)                            null comment '天下信用选中--证书id',
    person_evaluation    varchar(800)                           null comment '个人评价',
    qq                   varchar(20)                            null comment 'qq',
    we_chat              varchar(30)                            null comment '微信',
    place                varchar(30)                            null comment '籍贯',
    nation               varchar(30)                            null comment '民族',
    marriage             varchar(20)                            null comment '婚姻',
    age                  varchar(5)                             null comment '年龄',
    foreign_lang         varchar(20)                            null comment '外语级别 en3: 英语三级 en4: 英语四级 en6: 英语六级 enFore: 英语专四 enEight: 英语专八 other: 其他小语种 none: 暂无',
    huax_data            text                                   null comment '画像',
    neitui_email         int(1)       default 0                 null comment '是否需要发送内推邮件：1是  0否'
)
    comment '用户简历表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;

create index idx_uptime
    on customer_resume_base (update_time);

create index phone_index
    on customer_resume_base (phone);

create index user_index
    on customer_resume_base (customer_user_id);


-- auto-generated definition
create table customer_resume_project_experience
(
    id               bigint auto_increment
        primary key,
    customer_user_id bigint                             not null comment '用户ID',
    resume_id        bigint                             not null comment '简历ID',
    name             varchar(255)                       not null comment '项目名称',
    role             varchar(255)                       not null comment '担任角色',
    start_time       varchar(32)                        not null comment '开始时间',
    end_time         varchar(32)                        not null comment '结束时间',
    project_describe text                               not null comment '项目描述',
    achievement      text                               null comment '项目业绩',
    link             varchar(255)                       null comment '项目链接',
    create_time      datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_time      datetime                           null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted       int(1)   default 0                 null comment '是否删除 1删除 0正常'
)
    comment '简历项目经历表' collate = utf8mb4_general_ci
                             row_format = DYNAMIC;

create index `index`
    on customer_resume_project_experience (customer_user_id, resume_id);

