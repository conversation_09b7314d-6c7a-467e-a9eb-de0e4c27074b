<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tinyzk.user.center.mapper.AdminUserMapper">

    <!-- 用户列表查询结果映射 -->
    <resultMap id="UserListResultMap" type="com.tinyzk.user.center.vo.UserListVO">
        <id property="userId" column="user_id"/>
        <result property="nickname" column="nickname"/>
        <result property="status" column="status"/>
        <result property="realNameVerified" column="real_name_verified"/>
        <result property="createdAt" column="created_at"/>
        <result property="lastLoginAt" column="last_login_at"/>
        <result property="avatarUrl" column="avatar_url"/>
    </resultMap>
    
    <!-- 用户标识查询结果映射 -->
    <resultMap id="IdentifierResultMap" type="com.tinyzk.user.center.vo.UserListVO$IdentifierVO">
        <result property="userId" column="user_id"/>
        <result property="type" column="identity_type"/>
        <result property="value" column="identifier"/>
    </resultMap>
    
    <!-- 用户基础信息结果映射 -->
    <resultMap id="UserBaseInfoResultMap" type="com.tinyzk.user.center.vo.UserDetailVO$UserBaseInfo">
        <id property="userId" column="user_id"/>
        <result property="realNameVerified" column="real_name_verified"/>
        <result property="realName" column="real_name"/>
        <result property="idCardNumber" column="id_card_number"/>
        <result property="status" column="status"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="deletedAt" column="deleted_at"/>
    </resultMap>
    
    <!-- 用户资料信息结果映射 -->
    <resultMap id="UserProfileInfoResultMap" type="com.tinyzk.user.center.vo.UserDetailVO$UserProfileInfo">
        <id property="profileId" column="profile_id"/>
        <result property="nickname" column="nickname"/>
        <result property="avatarUrl" column="avatar_url"/>
        <result property="gender" column="gender"/>
        <result property="birthday" column="birthday"/>
        <result property="nationality" column="nationality"/>
        <result property="ethnicity" column="ethnicity"/>
        <result property="specialStatus" column="special_status"/>
        <result property="politicalStatus" column="political_status"/>
        <result property="maritalStatus" column="marital_status"/>
        <result property="fertilityStatus" column="fertility_status"/>
        <result property="healthStatus" column="health_status"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionName" column="region_name"/>
        <result property="address" column="address"/>
        <result property="bio" column="bio"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>
    
    <!-- 用户认证信息结果映射 -->
    <resultMap id="UserAuthInfoResultMap" type="com.tinyzk.user.center.vo.UserDetailVO$UserAuthInfo">
        <id property="authId" column="auth_id"/>
        <result property="identityType" column="identity_type"/>
        <result property="identifier" column="identifier"/>
        <result property="verified" column="verified"/>
        <result property="lastLoginAt" column="last_login_at"/>
        <result property="createdAt" column="created_at"/>
    </resultMap>
    
    <!-- 用户联系方式信息结果映射 -->
    <resultMap id="UserContactInfoResultMap" type="com.tinyzk.user.center.vo.UserDetailVO$UserContactInfo">
        <id property="contactId" column="contact_id"/>
        <result property="contactType" column="contact_type"/>
        <result property="contactValue" column="contact_value"/>
        <result property="label" column="label"/>
        <result property="isVerified" column="is_verified"/>
        <result property="verifiedAt" column="verified_at"/>
    </resultMap>
    
    <!-- 用户教育经历信息结果映射 -->
    <resultMap id="UserEducationInfoResultMap" type="com.tinyzk.user.center.vo.UserDetailVO$UserEducationInfo">
        <id property="eduId" column="edu_id"/>
        <result property="schoolName" column="school_name"/>
        <result property="degree" column="degree"/>
        <result property="degreeLevel" column="degree_level"/>
        <result property="major" column="major"/>
        <result property="secondaryMajor" column="secondary_major"/>
        <result property="majorArea" column="major_area"/>
        <result property="majorGpa" column="major_gpa"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="description" column="description"/>
    </resultMap>
    
    <!-- 用户工作经历信息结果映射 -->
    <resultMap id="UserWorkInfoResultMap" type="com.tinyzk.user.center.vo.UserDetailVO$UserWorkInfo">
        <id property="workId" column="work_id"/>
        <result property="companyName" column="company_name"/>
        <result property="companyLogo" column="company_logo"/>
        <result property="companyUrl" column="company_url"/>
        <result property="companySize" column="company_size"/>
        <result property="companyIndustry" column="company_industry"/>
        <result property="companyLocation" column="company_location"/>
        <result property="positionName" column="position_name"/>
        <result property="department" column="department"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="description" column="description"/>
        <result property="achievements" column="achievements"/>
    </resultMap>
    
    <!-- 用户项目经历信息结果映射 -->
    <resultMap id="UserProjectInfoResultMap" type="com.tinyzk.user.center.vo.UserDetailVO$UserProjectInfo">
        <id property="projectId" column="project_id"/>
        <result property="projectName" column="project_name"/>
        <result property="role" column="role"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="description" column="description"/>
        <result property="projectUrl" column="project_url"/>
        <result property="associatedOrganization" column="associated_organization"/>
    </resultMap>
    
    <!-- 用户培训经历信息结果映射 -->
    <resultMap id="UserTrainingInfoResultMap" type="com.tinyzk.user.center.vo.UserDetailVO$UserTrainingInfo">
        <id property="trainingId" column="training_id"/>
        <result property="trainingName" column="training_name"/>
        <result property="trainingType" column="training_type"/>
        <result property="trainingProvider" column="training_provider"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="description" column="description"/>
    </resultMap>
    
    <!-- 用户兼职经历信息结果映射 -->
    <resultMap id="UserPartTimeInfoResultMap" type="com.tinyzk.user.center.vo.UserDetailVO$UserPartTimeInfo">
        <id property="partTimeId" column="part_time_id"/>
        <result property="partTimeName" column="part_time_name"/>
        <result property="partTimeType" column="part_time_type"/>
        <result property="partTimeProvider" column="part_time_provider"/>
        <result property="partTimeLocation" column="part_time_location"/>
        <result property="servicePeriod" column="service_period"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="description" column="description"/>
    </resultMap>
    
    <!-- 分页查询用户列表 -->
    <select id="selectUserListPage" resultMap="UserListResultMap">
        SELECT
            ub.user_id,
            up.nickname,
            up.avatar_url,
            ub.status,
            ub.real_name_verified,
            ub.created_at,
            ual.max_last_login_at AS last_login_at
        FROM user_base ub
        LEFT JOIN user_profile up ON ub.user_id = up.user_id AND up.deleted_at IS NULL
        LEFT JOIN (
            SELECT user_id, MAX(last_login_at) AS max_last_login_at
            FROM user_auth
            WHERE deleted_at IS NULL
            GROUP BY user_id
        ) ual ON ub.user_id = ual.user_id
        <where>
            ub.deleted_at IS NULL
            <!-- 用户ID条件 -->
            <if test="dto.userId != null">
                AND ub.user_id = #{dto.userId}
            </if>
            
            <!-- 用户昵称条件（模糊匹配） -->
            <if test="dto.nickname != null and dto.nickname != ''">
                AND up.nickname LIKE CONCAT('%', #{dto.nickname}, '%')
            </if>
            
            <!-- 登录标识条件 (using EXISTS subquery) -->
            <if test="dto.identifier != null and dto.identifier != ''">
                AND EXISTS (
                    SELECT 1
                    FROM user_auth ua_filter
                    WHERE ua_filter.user_id = ub.user_id
                      AND ua_filter.identifier LIKE CONCAT('%', #{dto.identifier}, '%')
                      AND ua_filter.deleted_at IS NULL
                )
            </if>
            
            <!-- 用户状态条件 -->
            <if test="dto.status != null">
                AND ub.status = #{dto.status}
            </if>
            
            <!-- 实名认证状态条件 -->
            <if test="dto.realNameVerified != null">
                AND ub.real_name_verified = #{dto.realNameVerified}
            </if>
            
            <!-- 注册时间范围条件 -->
            <if test="dto.registeredAfter != null">
                AND ub.created_at &gt;= #{dto.registeredAfter}
            </if>
            <if test="dto.registeredBefore != null">
                AND ub.created_at &lt;= #{dto.registeredBefore}
            </if>
            
            <!-- 最后登录时间范围条件 -->
            <if test="dto.lastLoginAfter != null">
                AND ual.max_last_login_at &gt;= #{dto.lastLoginAfter}
            </if>
            <if test="dto.lastLoginBefore != null">
                AND ual.max_last_login_at &lt;= #{dto.lastLoginBefore}
            </if>
        </where>
        
        <!-- 排序条件 -->
        <choose>
            <when test="dto.sortBy == 'userId' and dto.sortOrder == 'asc'">
                ORDER BY ub.user_id ASC
            </when>
            <when test="dto.sortBy == 'userId' and dto.sortOrder != 'asc'">
                ORDER BY ub.user_id DESC
            </when>
            <when test="dto.sortBy == 'nickname' and dto.sortOrder == 'asc'">
                ORDER BY up.nickname ASC
            </when>
            <when test="dto.sortBy == 'nickname' and dto.sortOrder != 'asc'">
                ORDER BY up.nickname DESC
            </when>
            <when test="dto.sortBy == 'lastLoginAt' and dto.sortOrder == 'asc'">
                ORDER BY last_login_at ASC
            </when>
            <when test="dto.sortBy == 'lastLoginAt' and dto.sortOrder != 'asc'">
                ORDER BY last_login_at DESC
            </when>
            <when test="dto.sortOrder == 'asc'">
                ORDER BY ub.created_at ASC
            </when>
            <otherwise>
                ORDER BY ub.created_at DESC
            </otherwise>
        </choose>
    </select>
    
    <!-- Count查询，用于分页 -->
    <select id="selectUserListPage_count" resultType="long">
        SELECT COUNT(ub.user_id)
        FROM user_base ub
        LEFT JOIN user_profile up ON ub.user_id = up.user_id AND up.deleted_at IS NULL
        LEFT JOIN (
            SELECT user_id, MAX(last_login_at) AS max_last_login_at
            FROM user_auth
            WHERE deleted_at IS NULL
            GROUP BY user_id
        ) ual ON ub.user_id = ual.user_id
        <where>
            ub.deleted_at IS NULL
            <!-- 用户ID条件 -->
            <if test="dto.userId != null">
                AND ub.user_id = #{dto.userId}
            </if>
            <!-- 用户昵称条件（模糊匹配） -->
            <if test="dto.nickname != null and dto.nickname != ''">
                AND up.nickname LIKE CONCAT('%', #{dto.nickname}, '%')
            </if>
            <!-- 登录标识条件 (using EXISTS subquery) -->
            <if test="dto.identifier != null and dto.identifier != ''">
                AND EXISTS (
                    SELECT 1
                    FROM user_auth ua_filter
                    WHERE ua_filter.user_id = ub.user_id
                      AND ua_filter.identifier LIKE CONCAT('%', #{dto.identifier}, '%')
                      AND ua_filter.deleted_at IS NULL
                )
            </if>
            <!-- 用户状态条件 -->
            <if test="dto.status != null">
                AND ub.status = #{dto.status}
            </if>
            <!-- 实名认证状态条件 -->
            <if test="dto.realNameVerified != null">
                AND ub.real_name_verified = #{dto.realNameVerified}
            </if>
            <!-- 注册时间范围条件 -->
            <if test="dto.registeredAfter != null">
                AND ub.created_at &gt;= #{dto.registeredAfter}
            </if>
            <if test="dto.registeredBefore != null">
                AND ub.created_at &lt;= #{dto.registeredBefore}
            </if>
            <!-- 最后登录时间范围条件 -->
            <if test="dto.lastLoginAfter != null">
                AND ual.max_last_login_at &gt;= #{dto.lastLoginAfter}
            </if>
            <if test="dto.lastLoginBefore != null">
                AND ual.max_last_login_at &lt;= #{dto.lastLoginBefore}
            </if>
        </where>
    </select>
    
    <!-- 查询用户标识信息 -->
    <select id="selectUserIdentifiers" resultMap="IdentifierResultMap">
        SELECT
            ua.user_id,
            ua.identity_type,
            ua.identifier
        FROM user_auth ua
        WHERE ua.user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>
    
    <!-- 查询用户基础信息 -->
    <select id="selectUserBaseInfo" resultMap="UserBaseInfoResultMap">
        SELECT
            user_id,
            real_name_verified,
            real_name,
            id_card_number,
            status,
            created_at,
            updated_at,
            deleted_at
        FROM user_base
        WHERE user_id = #{userId}
        AND deleted_at IS NULL
    </select>
    
    <!-- 查询用户资料信息 -->
    <select id="selectUserProfileInfo" resultMap="UserProfileInfoResultMap">
        SELECT
            profile_id,
            nickname,
            avatar_url,
            gender,
            birthday,
            nationality,
            ethnicity,
            special_status,
            political_status,
            marital_status,
            fertility_status,
            health_status,
            region_code,
            region_name,
            address,
            bio,
            created_at,
            updated_at
        FROM user_profile
        WHERE user_id = #{userId}
        AND deleted_at IS NULL
    </select>
    
    <!-- 查询用户认证信息 -->
    <select id="selectUserAuthInfo" resultMap="UserAuthInfoResultMap">
        SELECT
            auth_id,
            identity_type,
            identifier,
            verified,
            last_login_at,
            created_at
        FROM user_auth
        WHERE user_id = #{userId}
        AND deleted_at IS NULL
    </select>
    
    <!-- 查询用户联系方式信息 -->
    <select id="selectUserContactInfo" resultMap="UserContactInfoResultMap">
        SELECT
            contact_id,
            contact_type,
            contact_value,
            label,
            is_verified,
            verified_at
        FROM user_contact_methods
        WHERE user_id = #{userId}
        AND deleted_at IS NULL
    </select>
    
    <!-- 查询用户教育经历信息 -->
    <select id="selectUserEducationInfo" resultMap="UserEducationInfoResultMap">
        SELECT
            edu_id,
            school_name,
            degree,
            degree_level,
            major,
            secondary_major,
            major_area,
            major_gpa,
            start_date,
            end_date,
            description
        FROM user_education_history
        WHERE user_id = #{userId}
        AND deleted_at IS NULL
        ORDER BY start_date DESC
    </select>
    
    <!-- 查询用户工作经历信息 -->
    <select id="selectUserWorkInfo" resultMap="UserWorkInfoResultMap">
        SELECT
            work_id,
            company_name,
            company_logo,
            company_url,
            company_size,
            company_industry,
            company_location,
            position_name,
            department,
            start_date,
            end_date,
            description,
            achievements
        FROM user_work_history
        WHERE user_id = #{userId}
        AND deleted_at IS NULL
        ORDER BY start_date DESC
    </select>
    
    <!-- 查询用户项目经历信息 -->
    <select id="selectUserProjectInfo" resultMap="UserProjectInfoResultMap">
        SELECT
            project_id,
            project_name,
            role,
            start_date,
            end_date,
            description,
            project_url,
            associated_organization
        FROM user_project_history
        WHERE user_id = #{userId}
        AND deleted_at IS NULL
        ORDER BY start_date DESC
    </select>
    
    <!-- 查询用户培训经历信息 -->
    <select id="selectUserTrainingInfo" resultMap="UserTrainingInfoResultMap">
        SELECT
            training_id,
            training_name,
            training_type,
            training_provider,
            start_date,
            end_date,
            description
        FROM user_training_history
        WHERE user_id = #{userId}
        AND deleted_at IS NULL
        ORDER BY start_date DESC
    </select>
    
    <!-- 查询用户兼职经历信息 -->
    <select id="selectUserPartTimeInfo" resultMap="UserPartTimeInfoResultMap">
        SELECT
            part_time_id,
            part_time_name,
            part_time_type,
            part_time_provider,
            part_time_location,
            service_period,
            start_date,
            end_date,
            description
        FROM user_part_time_history
        WHERE user_id = #{userId}
        AND deleted_at IS NULL
        ORDER BY start_date DESC
    </select>
    
    <!-- 更新用户基础信息 -->
    <update id="updateUserBase">
        UPDATE user_base
        <set>
            <if test="dto.status != null">
                status = #{dto.status},
            </if>
            <if test="dto.realNameVerified != null">
                real_name_verified = #{dto.realNameVerified},
            </if>
            updated_at = NOW()
        </set>
        WHERE user_id = #{userId}
        AND deleted_at IS NULL
    </update>
    
    <!-- 更新用户资料信息 -->
    <update id="updateUserProfile">
        UPDATE user_profile
        <set>
            <if test="dto.nickname != null">
                nickname = #{dto.nickname},
            </if>
            <if test="dto.avatarUrl != null">
                avatar_url = #{dto.avatarUrl},
            </if>
            <if test="dto.gender != null">
                gender = #{dto.gender},
            </if>
            <if test="dto.birthday != null">
                birthday = #{dto.birthday},
            </if>
            <if test="dto.bio != null">
                bio = #{dto.bio},
            </if>
            <if test="dto.nationality != null">
                nationality = #{dto.nationality},
            </if>
            <if test="dto.ethnicity != null">
                ethnicity = #{dto.ethnicity},
            </if>
            <if test="dto.specialStatus != null">
                special_status = #{dto.specialStatus.value},
            </if>
            <if test="dto.politicalStatus != null">
                political_status = #{dto.politicalStatus.value},
            </if>
            <if test="dto.maritalStatus != null">
                marital_status = #{dto.maritalStatus.value},
            </if>
            <if test="dto.fertilityStatus != null">
                fertility_status = #{dto.fertilityStatus.value},
            </if>
            <if test="dto.healthStatus != null">
                health_status = #{dto.healthStatus.value},
            </if>
            <if test="dto.regionCode != null">
                region_code = #{dto.regionCode},
            </if>
            <if test="dto.regionName != null">
                region_name = #{dto.regionName},
            </if>
            <if test="dto.address != null">
                address = #{dto.address},
            </if>
            updated_at = NOW()
        </set>
        WHERE user_id = #{userId}
        AND deleted_at IS NULL
    </update>
</mapper>
