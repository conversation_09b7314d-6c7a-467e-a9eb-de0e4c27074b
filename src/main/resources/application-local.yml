server:
  port: 8080

spring:
  application:
    name: user-center

  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************
    username: gigpal
    password: test-gigpal-2024PW

  # Redis配置
  data:
    redis:
      host: ***********
      port: 6379
      password: Hzxiaozhuankuai2024!
      database: 0

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.tinyzk.user.center.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted_at
      logic-delete-value: "now()"
      logic-not-delete-value: "NULL"

# 日志配置
logging:
  config: classpath:logback-spring.xml

knife4j:
  # 开启增强配置 
  enable: true
 # 开启生产环境屏蔽
  production: false
  setting:
    language: zh_cn

# JWT Configuration
jwt:
  secret: PleaseReplaceThisWithAVeryStrongAndLongRandomSecretKey ASAP!
  expiration: 3600000 # 1 hour in milliseconds (1 * 60 * 60 * 1000)

# 简历解析配置
resume:
  parse:
    api-url: http://*************:8000
    timeout: 30000
    max-retries: 3
    supported-file-types: doc,docx,pdf
    max-file-size: 10485760  # 10MB